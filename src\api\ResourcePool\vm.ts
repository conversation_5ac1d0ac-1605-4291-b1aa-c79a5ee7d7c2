import request from '/@/utils/request';
// 创建虚拟机
export const vmNew = (data: object) => {
	return request({
		url: '/theapi/v5/vm/create',
		method: 'post',
		data,
	});
};
// 虚拟机 查询
export const tabsVmQuery = (data: object) => {
	return request({
		// url: '/theapi/v5/host/vm/list', // 目前先不使用
		// url: '/theapi/v5/vm/list', // 未过滤的虚拟机==所有虚拟机
		url: '/theapi/v5/vm/lists',
		method: 'post',
		data,
	});
};
// 虚拟机 开机
export const vmOpen = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/open',
		method: 'post',
		data,
	});
};
// 虚拟机 关机
export const vmClose = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/close',
		method: 'post',
		data,
	});
};
// 虚拟机 关闭电源
export const vmDestroy = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/destroy',
		method: 'post',
		data,
	});
};
// 虚拟机 暂停
export const vmPause = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/pause',
		method: 'post',
		data,
	});
};
// 虚拟机 恢复
export const vmRestore = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/recover',
		method: 'post',
		data,
	});
};
// 虚拟机 重启
export const vmRestart = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/reboot',
		method: 'post',
		data,
	});
};
// 虚拟机 强制重启
export const vmForceRestart = (data: object) => {
	return request({
		url: '/theapi/v5/vm/op/force/restart',
		method: 'post',
		data,
	});
};

// 虚拟机 克隆
export const vmClone = (data: object) => {
	return request({
		url: '/theapi/***',
		method: 'post',
		data,
	});
};
// 虚拟机 迁移
export const vmTransfer = (data: object) => {
	return request({
		url: '/theapi/***',
		method: 'post',
		data,
	});
};

// 虚拟机 移入回收站
export const vmMoveinRecycle = (data: object) => {
	return request({
		url: '/theapi/v5/vm/recycle/movein',
		method: 'post',
		data,
	});
};
// 虚拟机回收站 删除
export const recycleVmDelete = (data: object) => {
	return request({
		url: '/theapi/v5/vm/delete',
		method: 'post',
		data,
	});
};
// 虚拟机回收站 移出
export const recycleVmRemove = (data: object) => {
	return request({
		url: '/theapi/v5/vm/recycle/moveout',
		method: 'post',
		data,
	});
};

import request from '/@/utils/request';
// 集群告警 查询
export const clusterAlarmQuery = () => {
	return request({
		url: '/theapi/v2/alerts/all',
		method: 'get',
	});
};
// 集群告警 忽略
export const clusterAlarmIgnore = (data: object) => {
	return request({
		url: '/theapi/v2/alerts/add/silences',
		method: 'post',
		data,
	});
};
// 存储告警 查询
export const storageAlarmQuery = () => {
	return request({
		url: '/theapi/v2/alerts/ceph',
		method: 'get',
	});
};
// 存储告警 忽略
export const storageAlarmIgnore = (data: object) => {
	return request({
		url: '/theapi/v2/alerts/add/silencesceph',
		method: 'post',
		data,
	});
};
// 告警规则 查询
export const alarmRulesQuery = (data: object) => {
	return request({
		url: '/theapi/v1/alertsrules/all',
		method: 'post',
		data,
	});
};
// 告警规则 添加
export const alarmRulesNew = (data: object) => {
	return request({
		url: '/theapi/v1/alertsrules/add',
		method: 'post',
		data,
	});
};
// 告警规则 修改
export const alarmRulesEdit = (data: object) => {
	return request({
		url: '/theapi/v1/alertsrules/edit',
		method: 'post',
		data,
	});
};
// 告警规则 删除
export const alarmRulesDelete = (data: object) => {
	return request({
		url: '/theapi/v1/alertsrules/delete',
		method: 'post',
		data,
	});
};
// 告警规则 启用禁用
export const alarmRulesAction = (data: object) => {
	return request({
		url: '/theapi/v1/alertsrules/status',
		method: 'post',
		data,
	});
};
// 日志 默认查询
export const logTableQuery = (data: object) => {
	return request({
		// url: '/theapi/v1/log/all',
		// url: '/theapi/v3/log/all',
		url: '/glcLog/v1/log/search',
		method: 'post',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded'
		},
		data,
	});
};
// 日志 条件查询
export const logConditionQuery = () => {
	return request({
		// url: '/theapi/v1/log/config',
		url: '/theapi/v3/log/config',
		method: 'get',
	});
};

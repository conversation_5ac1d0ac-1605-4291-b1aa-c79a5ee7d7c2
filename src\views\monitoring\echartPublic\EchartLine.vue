<template>
	<div class="task-statistics">
		<my-echarts :options="state.chartOption"></my-echarts>
	</div>
</template>

<script setup lang="ts" name="EchartsLine">
import { defineAsyncComponent, reactive, onMounted, ref, Ref, inject, watch } from 'vue';
const MyEcharts = defineAsyncComponent(() => import('/@/components/echarts/index.vue'));
import { chartColor, upAndDown, formatUnit, formatNumber, formatTime } from '/@/model/monitoring.ts';

const props = defineProps({
	chartData: {
		type: Object,
		required: true,
	},
	time: {
		type: Number,
		required: true,
	},
});

const getOption = () => {
  let listData = new Array()
  props.chartData.data.forEach((item:any) => {
    listData.push({
      name: item.title,
      type: 'line',
      stack: 'Total',
      data: item.list,
    })
  })
  let dataShow=(vl:any)=>{
    if(vl==undefined) {
      return "none"
    }
  }
	return {
    color: chartColor,
    tooltip: {
      trigger: 'axis',
      formatter: function (params:any) {
        let html = "";
        params.forEach((v:any) => {
          html += `<div style="color: #666;font-size: 14px;line-height: 24px;">
                  <span style="display:${dataShow(v.value)};">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                    chartColor[v.componentIndex]
                  };"></span>
                  ${upAndDown("name",v.seriesName)}
                  <span style="color:${upAndDown("color",v.seriesName)}">${upAndDown("text",v.seriesName)}</span>
                  <span style="color:${chartColor["#ccc"]};display: inline-block;width:80px;text-align: right;">${
            formatNumber(props.chartData.unit,v.value)<0?formatNumber(props.chartData.unit,v.value)*-1:formatNumber(props.chartData.unit,v.value)
          } ${ formatUnit(props.chartData.unit,v.value) }</span></span>`;
        })
        return html;
      },
    },
    legend: {
      itemWidth: 0,
      itemHeight: 10,
      type:'scroll',
    },
    grid: {
      left: '2%',
      right: '2%',
      bottom: '2%',
      containLabel: true
    },
    // 下载图片
    // toolbox: {
    //   feature: {
    //     saveAsImage: {}
    //   }
    // },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: formatTime(props.time,props.chartData.time)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        textStyle: {
          color: "#b3b3b3",
          fontSize: 14,
        },
        formatter: function(value:any) {
            // 这里将原始的值（value）和单位（'元'）拼接起来
          return formatNumber(props.chartData.unit,value) + formatUnit(props.chartData.unit,value)
        }
      },
    },
    series: listData
  };
};
// 定义变量内容
const state = reactive<EmptyObjectType>({
	chartOption: getOption(),
});

// 页面加载时
onMounted(() => {
	window.addEventListener('resize', () => {
		state.chartOption = getOption();
	});
});
watch(
	() => props.time,
	(val) => {
		state.chartOption = getOption();
	}
);
watch(
	() => props.chartData,
	(val) => {
		state.chartOption = getOption();
	}
);
</script>
<style scoped lang="scss">
.task-statistics {
	height: calc(100%);
}
</style>

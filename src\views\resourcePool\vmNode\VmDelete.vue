<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    class="dialog-500"
  >
    <div>
      <span>是否删除下列{{ props.deleteTime.split('/')[0] }}？</span>
      <p style="color:red;word-wrap: break-word">{{ props.treeItem.name }}</p>
      <div class="chec-area">
        <el-checkbox v-model="formItem.status" label="彻底删除" /> <span :style="{color: formItem.status?'#000':'#ccc'}">启用后将彻底删除虚拟机，否则将移入回收站。</span>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="TableDelete">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  deleteTime: {
    type: String,
    required: true
  }
});
const formItem = reactive({
  isShow: false,
  title: '删除',
  status: false,
});

const emit = defineEmits(['deleteOK']);
const confirm =()=>{
  formItem.isShow = false;
  formItem.status?emit('deleteOK', 'delete'):emit('deleteOK', 'movein');
}
watch(
  ()=> props.deleteTime,
  (val)=>{
    console.log(props.treeItem)
    formItem.isShow = true;
    formItem.status = false;
    formItem.title = '删除'+val.split('/')[0]
  }
);
</script>
<style lang="scss" scoped>
  .chec-area {
    display: flex;
    align-items: center;
    padding: 15px 0;
    span {
      margin-left: 5px;
    }
    
  }
</style>
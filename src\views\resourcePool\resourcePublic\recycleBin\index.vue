<template>
	<div class="resource-pool-container">
		<div class="tabs-btn-area">
			<div>
				<el-button type="primary" plain @click="refresh">刷新</el-button>
				<el-button type="primary" plain @click="restoreClick(state.tableSelect)">还原</el-button>
				<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
			</div>
			<div>
				<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
					<template #append>
						<el-button :icon="Search" @click="refresh"></el-button>
					</template>
				</el-input>
			</div>
		</div>
		<div class="tabs-table-area">
			<my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
				@selectionChange="selectChange"
			>
				<!-- CPU架构 -->
				<template #cpu_arch="{ row }">
					<span>{{ frameworkConvert(row.cpu_arch) }}</span>
				</template>
				<!-- 操作 -->
				<template #operation="{ row }">
					<el-button type="primary" plain @click="restoreClick([row])">还原</el-button>
					<el-button type="danger" @click="deleteClick([row])">删除</el-button>
				</template>
			</my-table>
		</div>
		<TableRestore :names="formDelet.tableNames" :restoreTime="state.restoreTime" @restoreOK="restoreOK"></TableRestore>
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>
<script setup lang="ts" name="ClusterAlarm">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { vmRecycleColumns } from '/@/model/resource.ts'; // 表列、正则
import { tabsVmQuery, recycleVmDelete, recycleVmRemove } from '/@/api/ResourcePool/vm'; // 接口
import { dayjs, ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableRestore = defineAsyncComponent(() => import('./TableRestore.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
// 定义变量内容
const state = reactive({
	columns: vmRecycleColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	restoreTime: '',
	deleteTime: '',
});

interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = [];
	return new Promise(async (resolve) => {
		tabsVmQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页显示条数
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
			recycle: true, // 回收站查询字段 不传或false为非回收站虚拟机查询
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
const emit = defineEmits(['returnOK']);
// 还原
const restoreClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.restoreTime = '虚拟机/' + new Date();
	}
};
// 还原数据
const restoreOK = (item: string) => {
	recycleVmRemove({
		names: formDelet.tableNames,
		ids: formDelet.tableIDs,
	}).then((res) => {
		if (res.msg == 'ok') {
			refresh();
			ElMessage.success('删除虚拟机操作完成');
      emit('returnOK', 'refresh');
		} else {
			ElMessage.error('删除虚拟机操作失败');
		}
	});
};
// 删除
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '虚拟机/' + new Date();
	}
};
// 返回数据
const returnOK = (item: string) => {
	recycleVmDelete({
		names: formDelet.tableNames,
		ids: formDelet.tableIDs,
	}).then((res) => {
		if (res.msg == 'ok') {
			refresh();
			ElMessage.success('删除虚拟机操作完成');
      emit('returnOK', 'refresh');
		} else {
			ElMessage.error(res.msg);
		}
	});
};
onMounted(() => {});
</script>
<style scoped lang="scss">
.resource-pool-container {
	width: calc(100%);
	height: calc(100%);
	width: 100%;
	height: 100%;
	.tabs-btn-area {
		height: 40px;
		display: flex;
		justify-content: space-between;
	}
	.tabs-table-area {
		width: calc(100%);
		height: calc(100% - 50px);
		position: relative;
	}
}
</style>
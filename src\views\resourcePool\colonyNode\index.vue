<template>
<div class="tabs-item-area">
  <div class="tabs-item-btn">
    <el-button type="primary" @click="addHostClick" :icon="CirclePlus">添加物理机</el-button>
    <!-- <el-button type="info" @click="editClick" :icon="Edit">修改集群</el-button>
    <el-button type="primary" @click="deleteClick" :icon="Delete">删除集群</el-button>
    <el-button type="info" :icon="Collection">集群HA</el-button>
    <el-button type="primary" :icon="TrendCharts">集群DRS</el-button> -->
    <!-- <el-button type="primary" round @click="addVMClick">添加虚拟机</el-button> -->
    <el-dropdown class="dropdown-bth" trigger="click" @command="handleCommand">
      <el-button type="info">
        <!-- <el-icon><MoreFilled /></el-icon><el-icon class="el-icon--right"><arrow-down /></el-icon> -->
        <!-- 更多<el-icon><MoreFilled /></el-icon> -->
        更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="xg">修改集群</el-dropdown-item>
          <el-dropdown-item command="ha" disabled>集群HA</el-dropdown-item>
          <el-dropdown-item command="drs" disabled>集群DRS</el-dropdown-item>
          <el-dropdown-item command="sc" style="color:red" divided>删除集群</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
	<div class="route-list">
			<div v-for="item in state.routeList" :class="{ 'route-item': true, 'is-active': isActive(item) }" :key="item" @click="tagClick(item)">
				<span><span>{{ item }}</span></span>
			</div>
	</div>
	<div class="tabs-item-center">
    <ColonySummary v-if="state.acive == '概要'" :treeItem="props.treeItem" :acive="state.acive"></ColonySummary>
    <Host v-if="state.acive == '宿主机'" :treeItem="props.treeItem" :acive="state.acive"></Host>
    <VM v-if="state.acive == '虚拟机'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></VM>
    <Storage v-if="state.acive == '存储池'" :treeItem="props.treeItem" :acive="state.acive"></Storage>
    <DistributedSwitch v-if="state.acive == '分布式交换机'" :treeItem="props.treeItem" :acive="state.acive"></DistributedSwitch>
    <Shuttering v-if="state.acive == '模板'" :treeItem="props.treeItem" :acive="state.acive"></Shuttering>
    <RecycleBin v-if="state.acive == '回收站'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></RecycleBin>
  </div>
  <ColonyEdit :editTime="state.editTime" :treeItem="props.treeItem" @returnOK="returnOK"></ColonyEdit>
  <ColonyDelete :deleteTime="state.deleteTime" :treeItem="props.treeItem" @returnOK="returnOK"></ColonyDelete>
  <AddHost :addHostTime="state.addHostTime" :treeItem="props.treeItem" @returnOK="returnOK"></AddHost>
  <AddVM :addVmTime="state.addVmTime" :treeItem="props.treeItem" @returnOK="returnOK"></AddVM>
</div>
</template>
<script setup lang="ts" name="ColonyNode">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { CirclePlus,Edit,Delete,Collection,TrendCharts } from '@element-plus/icons-vue'; // ICON
const ColonySummary = defineAsyncComponent(() => import('./ColonySummary.vue'));
const Host = defineAsyncComponent(() => import('../resourcePublic/host/Host.vue'));
const VM = defineAsyncComponent(() => import('../resourcePublic/vm/VM.vue'));
const Storage = defineAsyncComponent(() => import('../resourcePublic/storage/Storage.vue'));
const DistributedSwitch = defineAsyncComponent(() => import('../resourcePublic/distributedSwitch/index.vue'));
const Shuttering = defineAsyncComponent(() => import('../resourcePublic/shuttering/index.vue'));
const RecycleBin = defineAsyncComponent(() => import('../resourcePublic/recycleBin/index.vue'));

const ColonyEdit = defineAsyncComponent(() => import('./ColonyEdit.vue'));
const ColonyDelete = defineAsyncComponent(() => import('./ColonyDelete.vue'));
const AddHost = defineAsyncComponent(() => import('./AddHost.vue'));
const AddVM = defineAsyncComponent(() => import('../resourcePublic/addVM/index.vue'));
const handleCommand = (command: string | number | object) => {
  switch (command) {
    case 'xg':
      editClick()
      break;
    case 'ha':
      console.log('集群HA未开发')
      break;
    case 'drs':
      console.log('集群DRS未开发')
    case 'sc':
      deleteClick()
      break;
  }
}

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  }
});
const state = reactive({
  routeList: ['概要','宿主机','虚拟机','存储池','分布式交换机','模板','回收站'],
  acive: '概要',
  editTime: '',
  deleteTime: '',
  addHostTime: '',
  addVmTime: '',
});
const tagClick=(v: string) => {
  state.acive = v
}
const isActive = (v: string) => {
	if(state.acive ===v) {
    return true
  } else {
    return false
  }
};
const editClick = () => {
  state.editTime = ""+new Date()
}
const deleteClick = () => {
  state.deleteTime = ""+new Date()
}
const addHostClick = () => {
  state.addHostTime = ""+new Date()
}
const addVMClick = () => {
  state.addVmTime = ""+new Date()
}

const emit = defineEmits(['returnOK']);
const returnOK = (item: string)=>{
  emit('returnOK', item);
}
watch(
  ()=> props.treeItem,
  (val)=>{
    state.acive = '概要'
  }
);
</script>
<style lang="scss" scoped>
.tabs-item-area {
  width: calc(100%);
	height: calc(100%);
  .dropdown-bth {
    margin: 0 15px;
  }
  .route-list {
    width: calc(100% - 40px);
    height: 55px;
    // background: var(--el-fill-color-blank);
    background: #faf7f7;
    border-radius: 26px;
    margin: 10px;
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .route-item {
      position: relative;
      padding: 0 20px;
      font-size: 14px;
      line-height: 50px;
      cursor: pointer;
      margin: 0 10px;
      color: var(--el-color-title);
      border-radius: 3px;
      display: flex;
      height: 75%;
      align-items: center;

      &:hover {
        background: var(--el-color-primary-light-9);
        font-weight: bold;
        color: var(--el-color-primary);
        &::before {
          content: ' ';
          position: absolute;
          width: 4px;
          height: 18px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--el-color-primary);
          left: 0;
        }
      }
    }

    .is-active {
      // background: var(--el-color-primary-light-9);
      background: #fff9f5;
      font-weight: bold;
      color: var(--el-color-primary);
      &::before {
        content: ' ';
        position: absolute;
        width: 4px;
        height: 18px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--el-color-primary);
        left: 0;
      }
    }
  }
  .tabs-item-center {
    padding: 10px;
    border-radius: 10px;
    width: calc(100%);
    height: calc(100% - 110px);
    background: var(--el-fill-color-blank);
  }

}
</style>

{"version": 3, "file": "browser-es-module-loader.js", "sources": ["../../../node_modules/es-module-loader/core/common.js", "../../../node_modules/es-module-loader/core/loader-polyfill.js", "../../../node_modules/es-module-loader/core/resolve.js", "../../../node_modules/es-module-loader/core/register-loader.js", "../src/browser-es-module-loader.js"], "sourcesContent": ["/*\r\n * Environment\r\n */\r\nexport var isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\r\nexport var isNode = typeof process !== 'undefined' && process.versions && process.versions.node;\r\nexport var isWindows = typeof process !== 'undefined' && typeof process.platform === 'string' && process.platform.match(/^win/);\r\n\r\nvar envGlobal = typeof self !== 'undefined' ? self : global;\r\nexport { envGlobal as global }\r\n\r\n/*\r\n * Simple Symbol() shim\r\n */\r\nvar hasSymbol = typeof Symbol !== 'undefined';\r\nexport function createSymbol (name) {\r\n  return hasSymbol ? Symbol() : '@@' + name;\r\n}\r\n\r\nexport var toStringTag = hasSymbol && Symbol.toStringTag;\r\n\r\nexport function pathToFileUrl (filePath) {\r\n  return 'file://' + (isWindows ? '/' : '') + (isWindows ? filePath.replace(/\\\\/g, '/') : filePath);\r\n}\r\n\r\nexport function fileUrlToPath (fileUrl) {\r\n  if (fileUrl.substr(0, 7) !== 'file://')\r\n    throw new RangeError(fileUrl + ' is not a valid file url');\r\n  if (isWindows)\r\n    return fileUrl.substr(8).replace(/\\\\/g, '/');\r\n  else\r\n    return fileUrl.substr(7);\r\n}\r\n\r\n/*\r\n * Environment baseURI\r\n */\r\nexport var baseURI;\r\n\r\n// environent baseURI detection\r\nif (typeof document != 'undefined' && document.getElementsByTagName) {\r\n  baseURI = document.baseURI;\r\n\r\n  if (!baseURI) {\r\n    var bases = document.getElementsByTagName('base');\r\n    baseURI = bases[0] && bases[0].href || window.location.href;\r\n  }\r\n}\r\nelse if (typeof location != 'undefined') {\r\n  baseURI = location.href;\r\n}\r\n\r\n// sanitize out the hash and querystring\r\nif (baseURI) {\r\n  baseURI = baseURI.split('#')[0].split('?')[0];\r\n  var slashIndex = baseURI.lastIndexOf('/');\r\n  if (slashIndex !== -1)\r\n    baseURI = baseURI.substr(0, slashIndex + 1);\r\n}\r\nelse if (typeof process !== 'undefined' && process.cwd) {\r\n  baseURI = 'file://' + (isWindows ? '/' : '') + process.cwd();\r\n  if (isWindows)\r\n    baseURI = baseURI.replace(/\\\\/g, '/');\r\n}\r\nelse {\r\n  throw new TypeError('No environment baseURI');\r\n}\r\n\r\n// ensure baseURI has trailing \"/\"\r\nif (baseURI[baseURI.length - 1] !== '/')\r\n  baseURI += '/';\r\n\r\n/*\r\n * LoaderError with chaining for loader stacks\r\n */\r\nvar errArgs = new Error(0, '_').fileName == '_';\r\nfunction LoaderError__Check_error_message_for_loader_stack (childErr, newMessage) {\r\n  // Convert file:/// URLs to paths in Node\r\n  if (!isBrowser)\r\n    newMessage = newMessage.replace(isWindows ? /file:\\/\\/\\//g : /file:\\/\\//g, '');\r\n\r\n  var message = (childErr.message || childErr) + '\\n  ' + newMessage;\r\n\r\n  var err;\r\n  if (errArgs && childErr.fileName)\r\n    err = new Error(message, childErr.fileName, childErr.lineNumber);\r\n  else\r\n    err = new Error(message);\r\n\r\n\r\n  var stack = childErr.originalErr ? childErr.originalErr.stack : childErr.stack;\r\n\r\n  if (isNode)\r\n    // node doesn't show the message otherwise\r\n    err.stack = message + '\\n  ' + stack;\r\n  else\r\n    err.stack = stack;\r\n\r\n  err.originalErr = childErr.originalErr || childErr;\r\n\r\n  return err;\r\n}\r\nexport { LoaderError__Check_error_message_for_loader_stack as addToError }\r\n", "import { addToError, createSymbol, toStringTag } from './common.js';\r\n\r\nexport { Loader, ModuleNamespace, REGISTRY }\r\n\r\nvar resolvedPromise = Promise.resolve();\r\n\r\n/*\r\n * Simple Array values shim\r\n */\r\nfunction arrayValues (arr) {\r\n  if (arr.values)\r\n    return arr.values();\r\n\r\n  if (typeof Symbol === 'undefined' || !Symbol.iterator)\r\n    throw new Error('Symbol.iterator not supported in this browser');\r\n\r\n  var iterable = {};\r\n  iterable[Symbol.iterator] = function () {\r\n    var keys = Object.keys(arr);\r\n    var keyIndex = 0;\r\n    return {\r\n      next: function () {\r\n        if (keyIndex < keys.length)\r\n          return {\r\n            value: arr[keys[keyIndex++]],\r\n            done: false\r\n          };\r\n        else\r\n          return {\r\n            value: undefined,\r\n            done: true\r\n          };\r\n      }\r\n    };\r\n  };\r\n  return iterable;\r\n}\r\n\r\n/*\r\n * 3. Reflect.Loader\r\n *\r\n * We skip the entire native internal pipeline, just providing the bare API\r\n */\r\n// 3.1.1\r\nfunction Loader () {\r\n  this.registry = new Registry();\r\n}\r\n// 3.3.1\r\nLoader.prototype.constructor = Loader;\r\n\r\nfunction ensureInstantiated (module) {\r\n  if (module === undefined)\r\n    return;\r\n  if (module instanceof ModuleNamespace === false && module[toStringTag] !== 'module')\r\n    throw new TypeError('Module instantiation did not return a valid namespace object.');\r\n  return module;\r\n}\r\n\r\n// 3.3.2\r\nLoader.prototype.import = function (key, parent) {\r\n  if (typeof key !== 'string')\r\n    throw new TypeError('Loader import method must be passed a module key string');\r\n  // custom resolveInstantiate combined hook for better perf\r\n  var loader = this;\r\n  return resolvedPromise\r\n  .then(function () {\r\n    return loader[RESOLVE_INSTANTIATE](key, parent);\r\n  })\r\n  .then(ensureInstantiated)\r\n  //.then(Module.evaluate)\r\n  .catch(function (err) {\r\n    throw addToError(err, 'Loading ' + key + (parent ? ' from ' + parent : ''));\r\n  });\r\n};\r\n// 3.3.3\r\nvar RESOLVE = Loader.resolve = createSymbol('resolve');\r\n\r\n/*\r\n * Combined resolve / instantiate hook\r\n *\r\n * Not in current reduced spec, but necessary to separate RESOLVE from RESOLVE + INSTANTIATE as described\r\n * in the spec notes of this repo to ensure that loader.resolve doesn't instantiate when not wanted.\r\n *\r\n * We implement RESOLVE_INSTANTIATE as a single hook instead of a separate INSTANTIATE in order to avoid\r\n * the need for double registry lookups as a performance optimization.\r\n */\r\nvar RESOLVE_INSTANTIATE = Loader.resolveInstantiate = createSymbol('resolveInstantiate');\r\n\r\n// default resolveInstantiate is just to call resolve and then get from the registry\r\n// this provides compatibility for the resolveInstantiate optimization\r\nLoader.prototype[RESOLVE_INSTANTIATE] = function (key, parent) {\r\n  var loader = this;\r\n  return loader.resolve(key, parent)\r\n  .then(function (resolved) {\r\n    return loader.registry.get(resolved);\r\n  });\r\n};\r\n\r\nfunction ensureResolution (resolvedKey) {\r\n  if (resolvedKey === undefined)\r\n    throw new RangeError('No resolution found.');\r\n  return resolvedKey;\r\n}\r\n\r\nLoader.prototype.resolve = function (key, parent) {\r\n  var loader = this;\r\n  return resolvedPromise\r\n  .then(function() {\r\n    return loader[RESOLVE](key, parent);\r\n  })\r\n  .then(ensureResolution)\r\n  .catch(function (err) {\r\n    throw addToError(err, 'Resolving ' + key + (parent ? ' to ' + parent : ''));\r\n  });\r\n};\r\n\r\n// 3.3.4 (import without evaluate)\r\n// this is not documented because the use of deferred evaluation as in Module.evaluate is not\r\n// documented, as it is not considered a stable feature to be encouraged\r\n// Loader.prototype.load may well be deprecated if this stays disabled\r\n/* Loader.prototype.load = function (key, parent) {\r\n  return Promise.resolve(this[RESOLVE_INSTANTIATE](key, parent || this.key))\r\n  .catch(function (err) {\r\n    throw addToError(err, 'Loading ' + key + (parent ? ' from ' + parent : ''));\r\n  });\r\n}; */\r\n\r\n/*\r\n * 4. Registry\r\n *\r\n * Instead of structuring through a Map, just use a dictionary object\r\n * We throw for construction attempts so this doesn't affect the public API\r\n *\r\n * Registry has been adjusted to use Namespace objects over ModuleStatus objects\r\n * as part of simplifying loader API implementation\r\n */\r\nvar iteratorSupport = typeof Symbol !== 'undefined' && Symbol.iterator;\r\nvar REGISTRY = createSymbol('registry');\r\nfunction Registry() {\r\n  this[REGISTRY] = {};\r\n}\r\n// 4.4.1\r\nif (iteratorSupport) {\r\n  // 4.4.2\r\n  Registry.prototype[Symbol.iterator] = function () {\r\n    return this.entries()[Symbol.iterator]();\r\n  };\r\n\r\n  // 4.4.3\r\n  Registry.prototype.entries = function () {\r\n    var registry = this[REGISTRY];\r\n    return arrayValues(Object.keys(registry).map(function (key) {\r\n      return [key, registry[key]];\r\n    }));\r\n  };\r\n}\r\n\r\n// 4.4.4\r\nRegistry.prototype.keys = function () {\r\n  return arrayValues(Object.keys(this[REGISTRY]));\r\n};\r\n// 4.4.5\r\nRegistry.prototype.values = function () {\r\n  var registry = this[REGISTRY];\r\n  return arrayValues(Object.keys(registry).map(function (key) {\r\n    return registry[key];\r\n  }));\r\n};\r\n// 4.4.6\r\nRegistry.prototype.get = function (key) {\r\n  return this[REGISTRY][key];\r\n};\r\n// 4.4.7\r\nRegistry.prototype.set = function (key, namespace) {\r\n  if (!(namespace instanceof ModuleNamespace || namespace[toStringTag] === 'module'))\r\n    throw new Error('Registry must be set with an instance of Module Namespace');\r\n  this[REGISTRY][key] = namespace;\r\n  return this;\r\n};\r\n// 4.4.8\r\nRegistry.prototype.has = function (key) {\r\n  return Object.hasOwnProperty.call(this[REGISTRY], key);\r\n};\r\n// 4.4.9\r\nRegistry.prototype.delete = function (key) {\r\n  if (Object.hasOwnProperty.call(this[REGISTRY], key)) {\r\n    delete this[REGISTRY][key];\r\n    return true;\r\n  }\r\n  return false;\r\n};\r\n\r\n/*\r\n * Simple ModuleNamespace Exotic object based on a baseObject\r\n * We export this for allowing a fast-path for module namespace creation over Module descriptors\r\n */\r\n// var EVALUATE = createSymbol('evaluate');\r\nvar BASE_OBJECT = createSymbol('baseObject');\r\n\r\n// 8.3.1 Reflect.Module\r\n/*\r\n * Best-effort simplified non-spec implementation based on\r\n * a baseObject referenced via getters.\r\n *\r\n * Allows:\r\n *\r\n *   loader.registry.set('x', new Module({ default: 'x' }));\r\n *\r\n * Optional evaluation function provides experimental Module.evaluate\r\n * support for non-executed modules in registry.\r\n */\r\nfunction ModuleNamespace (baseObject/*, evaluate*/) {\r\n  Object.defineProperty(this, BASE_OBJECT, {\r\n    value: baseObject\r\n  });\r\n\r\n  // evaluate defers namespace population\r\n  /* if (evaluate) {\r\n    Object.defineProperty(this, EVALUATE, {\r\n      value: evaluate,\r\n      configurable: true,\r\n      writable: true\r\n    });\r\n  }\r\n  else { */\r\n    Object.keys(baseObject).forEach(extendNamespace, this);\r\n  //}\r\n};\r\n// 8.4.2\r\nModuleNamespace.prototype = Object.create(null);\r\n\r\nif (toStringTag)\r\n  Object.defineProperty(ModuleNamespace.prototype, toStringTag, {\r\n    value: 'Module'\r\n  });\r\n\r\nfunction extendNamespace (key) {\r\n  Object.defineProperty(this, key, {\r\n    enumerable: true,\r\n    get: function () {\r\n      return this[BASE_OBJECT][key];\r\n    }\r\n  });\r\n}\r\n\r\n/* function doEvaluate (evaluate, context) {\r\n  try {\r\n    evaluate.call(context);\r\n  }\r\n  catch (e) {\r\n    return e;\r\n  }\r\n}\r\n\r\n// 8.4.1 Module.evaluate... not documented or used because this is potentially unstable\r\nModule.evaluate = function (ns) {\r\n  var evaluate = ns[EVALUATE];\r\n  if (evaluate) {\r\n    ns[EVALUATE] = undefined;\r\n    var err = doEvaluate(evaluate);\r\n    if (err) {\r\n      // cache the error\r\n      ns[EVALUATE] = function () {\r\n        throw err;\r\n      };\r\n      throw err;\r\n    }\r\n    Object.keys(ns[BASE_OBJECT]).forEach(extendNamespace, ns);\r\n  }\r\n  // make chainable\r\n  return ns;\r\n}; */\r\n", "import { isNode } from './common.js';\r\n\r\n/*\r\n * Optimized URL normalization assuming a syntax-valid URL parent\r\n */\r\nfunction throwResolveError (relUrl, parentUrl) {\r\n  throw new RangeError('Unable to resolve \"' + relUrl + '\" to ' + parentUrl);\r\n}\r\nvar backslashRegEx = /\\\\/g;\r\nexport function resolveIfNotPlain (relUrl, parentUrl) {\r\n  if (relUrl[0] === ' ' || relUrl[relUrl.length - 1] === ' ')\r\n    relUrl = relUrl.trim();\r\n  var parentProtocol = parentUrl && parentUrl.substr(0, parentUrl.indexOf(':') + 1);\r\n\r\n  var firstChar = relUrl[0];\r\n  var secondChar = relUrl[1];\r\n\r\n  // protocol-relative\r\n  if (firstChar === '/' && secondChar === '/') {\r\n    if (!parentProtocol)\r\n      throwResolveError(relUrl, parentUrl);\r\n    if (relUrl.indexOf('\\\\') !== -1)\r\n      relUrl = relUrl.replace(backslashRegEx, '/');\r\n    return parentProtocol + relUrl;\r\n  }\r\n  // relative-url\r\n  else if (firstChar === '.' && (secondChar === '/' || secondChar === '.' && (relUrl[2] === '/' || relUrl.length === 2 && (relUrl += '/')) ||\r\n      relUrl.length === 1  && (relUrl += '/')) ||\r\n      firstChar === '/') {\r\n    if (relUrl.indexOf('\\\\') !== -1)\r\n      relUrl = relUrl.replace(backslashRegEx, '/');\r\n    var parentIsPlain = !parentProtocol || parentUrl[parentProtocol.length] !== '/';\r\n\r\n    // read pathname from parent if a URL\r\n    // pathname taken to be part after leading \"/\"\r\n    var pathname;\r\n    if (parentIsPlain) {\r\n      // resolving to a plain parent -> skip standard URL prefix, and treat entire parent as pathname\r\n      if (parentUrl === undefined)\r\n        throwResolveError(relUrl, parentUrl);\r\n      pathname = parentUrl;\r\n    }\r\n    else if (parentUrl[parentProtocol.length + 1] === '/') {\r\n      // resolving to a :// so we need to read out the auth and host\r\n      if (parentProtocol !== 'file:') {\r\n        pathname = parentUrl.substr(parentProtocol.length + 2);\r\n        pathname = pathname.substr(pathname.indexOf('/') + 1);\r\n      }\r\n      else {\r\n        pathname = parentUrl.substr(8);\r\n      }\r\n    }\r\n    else {\r\n      // resolving to :/ so pathname is the /... part\r\n      pathname = parentUrl.substr(parentProtocol.length + 1);\r\n    }\r\n\r\n    if (firstChar === '/') {\r\n      if (parentIsPlain)\r\n        throwResolveError(relUrl, parentUrl);\r\n      else\r\n        return parentUrl.substr(0, parentUrl.length - pathname.length - 1) + relUrl;\r\n    }\r\n\r\n    // join together and split for removal of .. and . segments\r\n    // looping the string instead of anything fancy for perf reasons\r\n    // '../../../../../z' resolved to 'x/y' is just 'z' regardless of parentIsPlain\r\n    var segmented = pathname.substr(0, pathname.lastIndexOf('/') + 1) + relUrl;\r\n\r\n    var output = [];\r\n    var segmentIndex = -1;\r\n\r\n    for (var i = 0; i < segmented.length; i++) {\r\n      // busy reading a segment - only terminate on '/'\r\n      if (segmentIndex !== -1) {\r\n        if (segmented[i] === '/') {\r\n          output.push(segmented.substring(segmentIndex, i + 1));\r\n          segmentIndex = -1;\r\n        }\r\n        continue;\r\n      }\r\n\r\n      // new segment - check if it is relative\r\n      if (segmented[i] === '.') {\r\n        // ../ segment\r\n        if (segmented[i + 1] === '.' && (segmented[i + 2] === '/' || i + 2 === segmented.length)) {\r\n          output.pop();\r\n          i += 2;\r\n        }\r\n        // ./ segment\r\n        else if (segmented[i + 1] === '/' || i + 1 === segmented.length) {\r\n          i += 1;\r\n        }\r\n        else {\r\n          // the start of a new segment as below\r\n          segmentIndex = i;\r\n          continue;\r\n        }\r\n\r\n        // this is the plain URI backtracking error (../, package:x -> error)\r\n        if (parentIsPlain && output.length === 0)\r\n          throwResolveError(relUrl, parentUrl);\r\n\r\n        continue;\r\n      }\r\n\r\n      // it is the start of a new segment\r\n      segmentIndex = i;\r\n    }\r\n    // finish reading out the last segment\r\n    if (segmentIndex !== -1)\r\n      output.push(segmented.substr(segmentIndex));\r\n\r\n    return parentUrl.substr(0, parentUrl.length - pathname.length) + output.join('');\r\n  }\r\n\r\n  // sanitizes and verifies (by returning undefined if not a valid URL-like form)\r\n  // Windows filepath compatibility is an added convenience here\r\n  var protocolIndex = relUrl.indexOf(':');\r\n  if (protocolIndex !== -1) {\r\n    if (isNode) {\r\n      // C:\\x becomes file:///c:/x (we don't support C|\\x)\r\n      if (relUrl[1] === ':' && relUrl[2] === '\\\\' && relUrl[0].match(/[a-z]/i))\r\n        return 'file:///' + relUrl.replace(backslashRegEx, '/');\r\n    }\r\n    return relUrl;\r\n  }\r\n}\r\n", "import { Loader, ModuleNamespace, REGISTRY } from './loader-polyfill.js';\r\nimport { resolveIfNotPlain } from './resolve.js';\r\nimport { addToError, global, createSymbol, baseURI, toStringTag } from './common.js';\r\n\r\nexport default RegisterLoader;\r\n\r\nvar resolvedPromise = Promise.resolve();\r\nvar emptyArray = [];\r\n\r\n/*\r\n * Register Loader\r\n *\r\n * Builds directly on top of loader polyfill to provide:\r\n * - loader.register support\r\n * - hookable higher-level resolve\r\n * - instantiate hook returning a ModuleNamespace or undefined for es module loading\r\n * - loader error behaviour as in HTML and loader specs, caching load and eval errors separately\r\n * - build tracing support by providing a .trace=true and .loads object format\r\n */\r\n\r\nvar REGISTER_INTERNAL = createSymbol('register-internal');\r\n\r\nfunction RegisterLoader () {\r\n  Loader.call(this);\r\n\r\n  var registryDelete = this.registry.delete;\r\n  this.registry.delete = function (key) {\r\n    var deleted = registryDelete.call(this, key);\r\n\r\n    // also delete from register registry if linked\r\n    if (records.hasOwnProperty(key) && !records[key].linkRecord) {\r\n      delete records[key];\r\n      deleted = true;\r\n    }\r\n\r\n    return deleted;\r\n  };\r\n\r\n  var records = {};\r\n\r\n  this[REGISTER_INTERNAL] = {\r\n    // last anonymous System.register call\r\n    lastRegister: undefined,\r\n    // in-flight es module load records\r\n    records: records\r\n  };\r\n\r\n  // tracing\r\n  this.trace = false;\r\n}\r\n\r\nRegisterLoader.prototype = Object.create(Loader.prototype);\r\nRegisterLoader.prototype.constructor = RegisterLoader;\r\n\r\nvar INSTANTIATE = RegisterLoader.instantiate = createSymbol('instantiate');\r\n\r\n// default normalize is the WhatWG style normalizer\r\nRegisterLoader.prototype[RegisterLoader.resolve = Loader.resolve] = function (key, parentKey) {\r\n  return resolveIfNotPlain(key, parentKey || baseURI);\r\n};\r\n\r\nRegisterLoader.prototype[INSTANTIATE] = function (key, processAnonRegister) {};\r\n\r\n// once evaluated, the linkRecord is set to undefined leaving just the other load record properties\r\n// this allows tracking new binding listeners for es modules through importerSetters\r\n// for dynamic modules, the load record is removed entirely.\r\nfunction createLoadRecord (state, key, registration) {\r\n  return state.records[key] = {\r\n    key: key,\r\n\r\n    // defined System.register cache\r\n    registration: registration,\r\n\r\n    // module namespace object\r\n    module: undefined,\r\n\r\n    // es-only\r\n    // this sticks around so new module loads can listen to binding changes\r\n    // for already-loaded modules by adding themselves to their importerSetters\r\n    importerSetters: undefined,\r\n\r\n    loadError: undefined,\r\n    evalError: undefined,\r\n\r\n    // in-flight linking record\r\n    linkRecord: {\r\n      // promise for instantiated\r\n      instantiatePromise: undefined,\r\n      dependencies: undefined,\r\n      execute: undefined,\r\n      executingRequire: false,\r\n\r\n      // underlying module object bindings\r\n      moduleObj: undefined,\r\n\r\n      // es only, also indicates if es or not\r\n      setters: undefined,\r\n\r\n      // promise for instantiated dependencies (dependencyInstantiations populated)\r\n      depsInstantiatePromise: undefined,\r\n      // will be the array of dependency load record or a module namespace\r\n      dependencyInstantiations: undefined,\r\n\r\n      // top-level await!\r\n      evaluatePromise: undefined,\r\n\r\n      // NB optimization and way of ensuring module objects in setters\r\n      // indicates setters which should run pre-execution of that dependency\r\n      // setters is then just for completely executed module objects\r\n      // alternatively we just pass the partially filled module objects as\r\n      // arguments into the execute function\r\n      // hoisted: undefined\r\n    }\r\n  };\r\n}\r\n\r\nRegisterLoader.prototype[Loader.resolveInstantiate] = function (key, parentKey) {\r\n  var loader = this;\r\n  var state = this[REGISTER_INTERNAL];\r\n  var registry = this.registry[REGISTRY];\r\n\r\n  return resolveInstantiate(loader, key, parentKey, registry, state)\r\n  .then(function (instantiated) {\r\n    if (instantiated instanceof ModuleNamespace || instantiated[toStringTag] === 'module')\r\n      return instantiated;\r\n\r\n    // resolveInstantiate always returns a load record with a link record and no module value\r\n    var link = instantiated.linkRecord;\r\n\r\n    // if already beaten to done, return\r\n    if (!link) {\r\n      if (instantiated.module)\r\n        return instantiated.module;\r\n      throw instantiated.evalError;\r\n    }\r\n\r\n    return deepInstantiateDeps(loader, instantiated, link, registry, state)\r\n    .then(function () {\r\n      return ensureEvaluate(loader, instantiated, link, registry, state);\r\n    });\r\n  });\r\n};\r\n\r\nfunction resolveInstantiate (loader, key, parentKey, registry, state) {\r\n  // normalization shortpath for already-normalized key\r\n  // could add a plain name filter, but doesn't yet seem necessary for perf\r\n  var module = registry[key];\r\n  if (module)\r\n    return Promise.resolve(module);\r\n\r\n  var load = state.records[key];\r\n\r\n  // already linked but not in main registry is ignored\r\n  if (load && !load.module) {\r\n    if (load.loadError)\r\n      return Promise.reject(load.loadError);\r\n    return instantiate(loader, load, load.linkRecord, registry, state);\r\n  }\r\n\r\n  return loader.resolve(key, parentKey)\r\n  .then(function (resolvedKey) {\r\n    // main loader registry always takes preference\r\n    module = registry[resolvedKey];\r\n    if (module)\r\n      return module;\r\n\r\n    load = state.records[resolvedKey];\r\n\r\n    // already has a module value but not already in the registry (load.module)\r\n    // means it was removed by registry.delete, so we should\r\n    // disgard the current load record creating a new one over it\r\n    // but keep any existing registration\r\n    if (!load || load.module)\r\n      load = createLoadRecord(state, resolvedKey, load && load.registration);\r\n\r\n    if (load.loadError)\r\n      return Promise.reject(load.loadError);\r\n\r\n    var link = load.linkRecord;\r\n    if (!link)\r\n      return load;\r\n\r\n    return instantiate(loader, load, link, registry, state);\r\n  });\r\n}\r\n\r\nfunction createProcessAnonRegister (loader, load, state) {\r\n  return function () {\r\n    var lastRegister = state.lastRegister;\r\n\r\n    if (!lastRegister)\r\n      return !!load.registration;\r\n\r\n    state.lastRegister = undefined;\r\n    load.registration = lastRegister;\r\n\r\n    return true;\r\n  };\r\n}\r\n\r\nfunction instantiate (loader, load, link, registry, state) {\r\n  return link.instantiatePromise || (link.instantiatePromise =\r\n  // if there is already an existing registration, skip running instantiate\r\n  (load.registration ? resolvedPromise : resolvedPromise.then(function () {\r\n    state.lastRegister = undefined;\r\n    return loader[INSTANTIATE](load.key, loader[INSTANTIATE].length > 1 && createProcessAnonRegister(loader, load, state));\r\n  }))\r\n  .then(function (instantiation) {\r\n    // direct module return from instantiate -> we're done\r\n    if (instantiation !== undefined) {\r\n      if (!(instantiation instanceof ModuleNamespace || instantiation[toStringTag] === 'module'))\r\n        throw new TypeError('Instantiate did not return a valid Module object.');\r\n\r\n      delete state.records[load.key];\r\n      if (loader.trace)\r\n        traceLoad(loader, load, link);\r\n      return registry[load.key] = instantiation;\r\n    }\r\n\r\n    // run the cached loader.register declaration if there is one\r\n    var registration = load.registration;\r\n    // clear to allow new registrations for future loads (combined with registry delete)\r\n    load.registration = undefined;\r\n    if (!registration)\r\n      throw new TypeError('Module instantiation did not call an anonymous or correctly named System.register.');\r\n\r\n    link.dependencies = registration[0];\r\n\r\n    load.importerSetters = [];\r\n\r\n    link.moduleObj = {};\r\n\r\n    // process System.registerDynamic declaration\r\n    if (registration[2]) {\r\n      link.moduleObj.default = link.moduleObj.__useDefault = {};\r\n      link.executingRequire = registration[1];\r\n      link.execute = registration[2];\r\n    }\r\n\r\n    // process System.register declaration\r\n    else {\r\n      registerDeclarative(loader, load, link, registration[1]);\r\n    }\r\n\r\n    return load;\r\n  })\r\n  .catch(function (err) {\r\n    load.linkRecord = undefined;\r\n    throw load.loadError = load.loadError || addToError(err, 'Instantiating ' + load.key);\r\n  }));\r\n}\r\n\r\n// like resolveInstantiate, but returning load records for linking\r\nfunction resolveInstantiateDep (loader, key, parentKey, registry, state, traceDepMap) {\r\n  // normalization shortpaths for already-normalized key\r\n  // DISABLED to prioritise consistent resolver calls\r\n  // could add a plain name filter, but doesn't yet seem necessary for perf\r\n  /* var load = state.records[key];\r\n  var module = registry[key];\r\n\r\n  if (module) {\r\n    if (traceDepMap)\r\n      traceDepMap[key] = key;\r\n\r\n    // registry authority check in case module was deleted or replaced in main registry\r\n    if (load && load.module && load.module === module)\r\n      return load;\r\n    else\r\n      return module;\r\n  }\r\n\r\n  // already linked but not in main registry is ignored\r\n  if (load && !load.module) {\r\n    if (traceDepMap)\r\n      traceDepMap[key] = key;\r\n    return instantiate(loader, load, load.linkRecord, registry, state);\r\n  } */\r\n  return loader.resolve(key, parentKey)\r\n  .then(function (resolvedKey) {\r\n    if (traceDepMap)\r\n      traceDepMap[key] = resolvedKey;\r\n\r\n    // normalization shortpaths for already-normalized key\r\n    var load = state.records[resolvedKey];\r\n    var module = registry[resolvedKey];\r\n\r\n    // main loader registry always takes preference\r\n    if (module && (!load || load.module && module !== load.module))\r\n      return module;\r\n\r\n    if (load && load.loadError)\r\n      throw load.loadError;\r\n\r\n    // already has a module value but not already in the registry (load.module)\r\n    // means it was removed by registry.delete, so we should\r\n    // disgard the current load record creating a new one over it\r\n    // but keep any existing registration\r\n    if (!load || !module && load.module)\r\n      load = createLoadRecord(state, resolvedKey, load && load.registration);\r\n\r\n    var link = load.linkRecord;\r\n    if (!link)\r\n      return load;\r\n\r\n    return instantiate(loader, load, link, registry, state);\r\n  });\r\n}\r\n\r\nfunction traceLoad (loader, load, link) {\r\n  loader.loads = loader.loads || {};\r\n  loader.loads[load.key] = {\r\n    key: load.key,\r\n    deps: link.dependencies,\r\n    dynamicDeps: [],\r\n    depMap: link.depMap || {}\r\n  };\r\n}\r\n\r\n/*\r\n * Convert a CJS module.exports into a valid object for new Module:\r\n *\r\n *   new Module(getEsModule(module.exports))\r\n *\r\n * Sets the default value to the module, while also reading off named exports carefully.\r\n */\r\nfunction registerDeclarative (loader, load, link, declare) {\r\n  var moduleObj = link.moduleObj;\r\n  var importerSetters = load.importerSetters;\r\n\r\n  var definedExports = false;\r\n\r\n  // closure especially not based on link to allow link record disposal\r\n  var declared = declare.call(global, function (name, value) {\r\n    if (typeof name === 'object') {\r\n      var changed = false;\r\n      for (var p in name) {\r\n        value = name[p];\r\n        if (p !== '__useDefault' && (!(p in moduleObj) || moduleObj[p] !== value)) {\r\n          changed = true;\r\n          moduleObj[p] = value;\r\n        }\r\n      }\r\n      if (changed === false)\r\n        return value;\r\n    }\r\n    else {\r\n      if ((definedExports || name in moduleObj) && moduleObj[name] === value)\r\n        return value;\r\n      moduleObj[name] = value;\r\n    }\r\n\r\n    for (var i = 0; i < importerSetters.length; i++)\r\n      importerSetters[i](moduleObj);\r\n\r\n    return value;\r\n  }, new ContextualLoader(loader, load.key));\r\n\r\n  link.setters = declared.setters || [];\r\n  link.execute = declared.execute;\r\n  if (declared.exports) {\r\n    link.moduleObj = moduleObj = declared.exports;\r\n    definedExports = true;\r\n  }\r\n}\r\n\r\nfunction instantiateDeps (loader, load, link, registry, state) {\r\n  if (link.depsInstantiatePromise)\r\n    return link.depsInstantiatePromise;\r\n\r\n  var depsInstantiatePromises = Array(link.dependencies.length);\r\n\r\n  for (var i = 0; i < link.dependencies.length; i++)\r\n    depsInstantiatePromises[i] = resolveInstantiateDep(loader, link.dependencies[i], load.key, registry, state, loader.trace && link.depMap || (link.depMap = {}));\r\n\r\n  var depsInstantiatePromise = Promise.all(depsInstantiatePromises)\r\n  .then(function (dependencyInstantiations) {\r\n    link.dependencyInstantiations = dependencyInstantiations;\r\n\r\n    // run setters to set up bindings to instantiated dependencies\r\n    if (link.setters) {\r\n      for (var i = 0; i < dependencyInstantiations.length; i++) {\r\n        var setter = link.setters[i];\r\n        if (setter) {\r\n          var instantiation = dependencyInstantiations[i];\r\n\r\n          if (instantiation instanceof ModuleNamespace || instantiation[toStringTag] === 'module') {\r\n            setter(instantiation);\r\n          }\r\n          else {\r\n            if (instantiation.loadError)\r\n              throw instantiation.loadError;\r\n            setter(instantiation.module || instantiation.linkRecord.moduleObj);\r\n            // this applies to both es and dynamic registrations\r\n            if (instantiation.importerSetters)\r\n              instantiation.importerSetters.push(setter);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return load;\r\n  });\r\n\r\n  if (loader.trace)\r\n    depsInstantiatePromise = depsInstantiatePromise.then(function () {\r\n      traceLoad(loader, load, link);\r\n      return load;\r\n    });\r\n\r\n  depsInstantiatePromise = depsInstantiatePromise.catch(function (err) {\r\n    // throw up the instantiateDeps stack\r\n    link.depsInstantiatePromise = undefined;\r\n    throw addToError(err, 'Loading ' + load.key);\r\n  });\r\n\r\n  depsInstantiatePromise.catch(function () {});\r\n\r\n  return link.depsInstantiatePromise = depsInstantiatePromise;\r\n}\r\n\r\nfunction deepInstantiateDeps (loader, load, link, registry, state) {\r\n  var seen = [];\r\n  function addDeps (load, link) {\r\n    if (!link)\r\n      return resolvedPromise;\r\n    if (seen.indexOf(load) !== -1)\r\n      return resolvedPromise;\r\n    seen.push(load);\r\n    \r\n    return instantiateDeps(loader, load, link, registry, state)\r\n    .then(function () {\r\n      var depPromises;\r\n      for (var i = 0; i < link.dependencies.length; i++) {\r\n        var depLoad = link.dependencyInstantiations[i];\r\n        if (!(depLoad instanceof ModuleNamespace || depLoad[toStringTag] === 'module')) {\r\n          depPromises = depPromises || [];\r\n          depPromises.push(addDeps(depLoad, depLoad.linkRecord));\r\n        }\r\n      }\r\n      if (depPromises)\r\n        return Promise.all(depPromises);\r\n    });\r\n  };\r\n\r\n  return addDeps(load, link);\r\n}\r\n\r\n/*\r\n * System.register\r\n */\r\nRegisterLoader.prototype.register = function (key, deps, declare) {\r\n  var state = this[REGISTER_INTERNAL];\r\n\r\n  // anonymous modules get stored as lastAnon\r\n  if (declare === undefined) {\r\n    state.lastRegister = [key, deps, undefined];\r\n  }\r\n\r\n  // everything else registers into the register cache\r\n  else {\r\n    var load = state.records[key] || createLoadRecord(state, key, undefined);\r\n    load.registration = [deps, declare, undefined];\r\n  }\r\n};\r\n\r\n/*\r\n * System.registerDyanmic\r\n */\r\nRegisterLoader.prototype.registerDynamic = function (key, deps, executingRequire, execute) {\r\n  var state = this[REGISTER_INTERNAL];\r\n\r\n  // anonymous modules get stored as lastAnon\r\n  if (typeof key !== 'string') {\r\n    state.lastRegister = [key, deps, executingRequire];\r\n  }\r\n\r\n  // everything else registers into the register cache\r\n  else {\r\n    var load = state.records[key] || createLoadRecord(state, key, undefined);\r\n    load.registration = [deps, executingRequire, execute];\r\n  }\r\n};\r\n\r\n// ContextualLoader class\r\n// backwards-compatible with previous System.register context argument by exposing .id, .key\r\nfunction ContextualLoader (loader, key) {\r\n  this.loader = loader;\r\n  this.key = this.id = key;\r\n  this.meta = {\r\n    url: key\r\n    // scriptElement: null\r\n  };\r\n}\r\n/*ContextualLoader.prototype.constructor = function () {\r\n  throw new TypeError('Cannot subclass the contextual loader only Reflect.Loader.');\r\n};*/\r\nContextualLoader.prototype.import = function (key) {\r\n  if (this.loader.trace)\r\n    this.loader.loads[this.key].dynamicDeps.push(key);\r\n  return this.loader.import(key, this.key);\r\n};\r\n/*ContextualLoader.prototype.resolve = function (key) {\r\n  return this.loader.resolve(key, this.key);\r\n};*/\r\n\r\nfunction ensureEvaluate (loader, load, link, registry, state) {\r\n  if (load.module)\r\n    return load.module;\r\n  if (load.evalError)\r\n    throw load.evalError;\r\n  if (link.evaluatePromise)\r\n    return link.evaluatePromise;\r\n\r\n  if (link.setters) {\r\n    var evaluatePromise = doEvaluateDeclarative(loader, load, link, registry, state, [load]);\r\n    if (evaluatePromise)\r\n      return evaluatePromise;\r\n  }\r\n  else {\r\n    doEvaluateDynamic(loader, load, link, registry, state, [load]);\r\n  }\r\n  return load.module;\r\n}\r\n\r\nfunction makeDynamicRequire (loader, key, dependencies, dependencyInstantiations, registry, state, seen) {\r\n  // we can only require from already-known dependencies\r\n  return function (name) {\r\n    for (var i = 0; i < dependencies.length; i++) {\r\n      if (dependencies[i] === name) {\r\n        var depLoad = dependencyInstantiations[i];\r\n        var module;\r\n\r\n        if (depLoad instanceof ModuleNamespace || depLoad[toStringTag] === 'module') {\r\n          module = depLoad;\r\n        }\r\n        else {\r\n          if (depLoad.evalError)\r\n            throw depLoad.evalError;\r\n          if (depLoad.module === undefined && seen.indexOf(depLoad) === -1 && !depLoad.linkRecord.evaluatePromise) {\r\n            if (depLoad.linkRecord.setters) {\r\n              doEvaluateDeclarative(loader, depLoad, depLoad.linkRecord, registry, state, [depLoad]);\r\n            }\r\n            else {\r\n              seen.push(depLoad);\r\n              doEvaluateDynamic(loader, depLoad, depLoad.linkRecord, registry, state, seen);\r\n            }\r\n          }\r\n          module = depLoad.module || depLoad.linkRecord.moduleObj;\r\n        }\r\n\r\n        return '__useDefault' in module ? module.__useDefault : module;\r\n      }\r\n    }\r\n    throw new Error('Module ' + name + ' not declared as a System.registerDynamic dependency of ' + key);\r\n  };\r\n}\r\n\r\nfunction evalError (load, err) {\r\n  load.linkRecord = undefined;\r\n  var evalError = addToError(err, 'Evaluating ' + load.key);\r\n  if (load.evalError === undefined)\r\n    load.evalError = evalError;\r\n  throw evalError;\r\n}\r\n\r\n// es modules evaluate dependencies first\r\n// returns the error if any\r\nfunction doEvaluateDeclarative (loader, load, link, registry, state, seen) {\r\n  var depLoad, depLink;\r\n  var depLoadPromises;\r\n  for (var i = 0; i < link.dependencies.length; i++) {\r\n    var depLoad = link.dependencyInstantiations[i];\r\n    if (depLoad instanceof ModuleNamespace || depLoad[toStringTag] === 'module')\r\n      continue;\r\n\r\n    // custom Module returned from instantiate\r\n    depLink = depLoad.linkRecord;\r\n    if (depLink) {\r\n      if (depLoad.evalError) {\r\n        evalError(load, depLoad.evalError);\r\n      }\r\n      else if (depLink.setters) {\r\n        if (seen.indexOf(depLoad) === -1) {\r\n          seen.push(depLoad);\r\n          try {\r\n            var depLoadPromise = doEvaluateDeclarative(loader, depLoad, depLink, registry, state, seen);\r\n          }\r\n          catch (e) {\r\n            evalError(load, e);\r\n          }\r\n          if (depLoadPromise) {\r\n            depLoadPromises = depLoadPromises || [];\r\n            depLoadPromises.push(depLoadPromise.catch(function (err) {\r\n              evalError(load, err);\r\n            }));\r\n          }\r\n        }\r\n      }\r\n      else {\r\n        try {\r\n          doEvaluateDynamic(loader, depLoad, depLink, registry, state, [depLoad]);\r\n        }\r\n        catch (e) {\r\n          evalError(load, e);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  if (depLoadPromises)\r\n    return link.evaluatePromise = Promise.all(depLoadPromises)\r\n    .then(function () {\r\n      if (link.execute) {\r\n        // ES System.register execute\r\n        // \"this\" is null in ES\r\n        try {\r\n          var execPromise = link.execute.call(nullContext);\r\n        }\r\n        catch (e) {\r\n          evalError(load, e);\r\n        }\r\n        if (execPromise)\r\n          return execPromise.catch(function (e) {\r\n            evalError(load, e);\r\n          })\r\n          .then(function () {\r\n            load.linkRecord = undefined;\r\n            return registry[load.key] = load.module = new ModuleNamespace(link.moduleObj);\r\n          });\r\n      }\r\n    \r\n      // dispose link record\r\n      load.linkRecord = undefined;\r\n      registry[load.key] = load.module = new ModuleNamespace(link.moduleObj);\r\n    });\r\n\r\n  if (link.execute) {\r\n    // ES System.register execute\r\n    // \"this\" is null in ES\r\n    try {\r\n      var execPromise = link.execute.call(nullContext);\r\n    }\r\n    catch (e) {\r\n      evalError(load, e);\r\n    }\r\n    if (execPromise)\r\n      return link.evaluatePromise = execPromise.catch(function (e) {\r\n        evalError(load, e);\r\n      })\r\n      .then(function () {\r\n        load.linkRecord = undefined;\r\n        return registry[load.key] = load.module = new ModuleNamespace(link.moduleObj);\r\n      });\r\n  }\r\n\r\n  // dispose link record\r\n  load.linkRecord = undefined;\r\n  registry[load.key] = load.module = new ModuleNamespace(link.moduleObj);\r\n}\r\n\r\n// non es modules explicitly call moduleEvaluate through require\r\nfunction doEvaluateDynamic (loader, load, link, registry, state, seen) {\r\n  // System.registerDynamic execute\r\n  // \"this\" is \"exports\" in CJS\r\n  var module = { id: load.key };\r\n  var moduleObj = link.moduleObj;\r\n  Object.defineProperty(module, 'exports', {\r\n    configurable: true,\r\n    set: function (exports) {\r\n      moduleObj.default = moduleObj.__useDefault = exports;\r\n    },\r\n    get: function () {\r\n      return moduleObj.__useDefault;\r\n    }\r\n  });\r\n\r\n  var require = makeDynamicRequire(loader, load.key, link.dependencies, link.dependencyInstantiations, registry, state, seen);\r\n\r\n  // evaluate deps first\r\n  if (!link.executingRequire)\r\n    for (var i = 0; i < link.dependencies.length; i++)\r\n      require(link.dependencies[i]);\r\n\r\n  try {\r\n    var output = link.execute.call(global, require, moduleObj.default, module);\r\n    if (output !== undefined)\r\n      module.exports = output;\r\n  }\r\n  catch (e) {\r\n    evalError(load, e);\r\n  }\r\n\r\n  load.linkRecord = undefined;\r\n\r\n  // pick up defineProperty calls to module.exports when we can\r\n  if (module.exports !== moduleObj.__useDefault)\r\n    moduleObj.default = moduleObj.__useDefault = module.exports;\r\n\r\n  var moduleDefault = moduleObj.default;\r\n\r\n  // __esModule flag extension support via lifting\r\n  if (moduleDefault && moduleDefault.__esModule) {\r\n    for (var p in moduleDefault) {\r\n      if (Object.hasOwnProperty.call(moduleDefault, p))\r\n        moduleObj[p] = moduleDefault[p];\r\n    }\r\n  }\r\n\r\n  registry[load.key] = load.module = new ModuleNamespace(link.moduleObj);\r\n\r\n  // run importer setters and clear them\r\n  // this allows dynamic modules to update themselves into es modules\r\n  // as soon as execution has completed\r\n  if (load.importerSetters)\r\n    for (var i = 0; i < load.importerSetters.length; i++)\r\n      load.importerSetters[i](load.module);\r\n  load.importerSetters = undefined;\r\n}\r\n\r\n// the closest we can get to call(undefined)\r\nvar nullContext = Object.create(null);\r\nif (Object.freeze)\r\n  Object.freeze(nullContext);\r\n", "import RegisterLoader from 'es-module-loader/core/register-loader.js';\nimport { InternalModuleNamespace as ModuleNamespace } from 'es-module-loader/core/loader-polyfill.js';\n\nimport { baseURI, global, isBrowser } from 'es-module-loader/core/common.js';\nimport { resolveIfNotPlain } from 'es-module-loader/core/resolve.js';\n\nvar loader;\n\n// <script type=\"module\"> support\nvar anonSources = {};\nif (typeof document != 'undefined' && document.getElementsByTagName) {\n  var handleError = function(err) {\n    // dispatch an error event so that we can display in errors in browsers\n    // that don't yet support unhandledrejection\n    if (window.onunhandledrejection === undefined) {\n      try {\n        var evt = new Event('error');\n      } catch (_eventError) {\n        var evt = document.createEvent('Event');\n        evt.initEvent('error', true, true);\n      }\n      evt.message = err.message;\n      if (err.fileName) {\n        evt.filename = err.fileName;\n        evt.lineno = err.lineNumber;\n        evt.colno = err.columnNumber;\n      } else if (err.sourceURL) {\n        evt.filename = err.sourceURL;\n        evt.lineno = err.line;\n        evt.colno = err.column;\n      }\n      evt.error = err;\n      window.dispatchEvent(evt);\n    }\n\n    // throw so it still shows up in the console\n    throw err;\n  }\n\n  var ready = function() {\n    document.removeEventListener('DOMContentLoaded', ready, false );\n\n    var anonCnt = 0;\n\n    var scripts = document.getElementsByTagName('script');\n    for (var i = 0; i < scripts.length; i++) {\n      var script = scripts[i];\n      if (script.type == 'module' && !script.loaded) {\n        script.loaded = true;\n        if (script.src) {\n          loader.import(script.src).catch(handleError);\n        }\n        // anonymous modules supported via a custom naming scheme and registry\n        else {\n          var uri = './<anon' + ++anonCnt + '>.js';\n          if (script.id !== \"\"){\n            uri = \"./\" + script.id;\n          }\n\n          var anonName = resolveIfNotPlain(uri, baseURI);\n          anonSources[anonName] = script.innerHTML;\n          loader.import(anonName).catch(handleError);\n        }\n      }\n    }\n  }\n\n  // simple DOM ready\n  if (document.readyState !== 'loading')\n    setTimeout(ready);\n  else\n    document.addEventListener('DOMContentLoaded', ready, false);\n}\n\nfunction BrowserESModuleLoader(baseKey) {\n  if (baseKey)\n    this.baseKey = resolveIfNotPlain(baseKey, baseURI) || resolveIfNotPlain('./' + baseKey, baseURI);\n\n  RegisterLoader.call(this);\n\n  var loader = this;\n\n  // ensure System.register is available\n  global.System = global.System || {};\n  if (typeof global.System.register == 'function')\n    var prevRegister = global.System.register;\n  global.System.register = function() {\n    loader.register.apply(loader, arguments);\n    if (prevRegister)\n      prevRegister.apply(this, arguments);\n  };\n}\nBrowserESModuleLoader.prototype = Object.create(RegisterLoader.prototype);\n\n// normalize is never given a relative name like \"./x\", that part is already handled\nBrowserESModuleLoader.prototype[RegisterLoader.resolve] = function(key, parent) {\n  var resolved = RegisterLoader.prototype[RegisterLoader.resolve].call(this, key, parent || this.baseKey) || key;\n  if (!resolved)\n    throw new RangeError('ES module loader does not resolve plain module names, resolving \"' + key + '\" to ' + parent);\n\n  return resolved;\n};\n\nfunction xhrFetch(url, resolve, reject) {\n  var xhr = new XMLHttpRequest();\n  var load = function(source) {\n    resolve(xhr.responseText);\n  }\n  var error = function() {\n    reject(new Error('XHR error' + (xhr.status ? ' (' + xhr.status + (xhr.statusText ? ' ' + xhr.statusText  : '') + ')' : '') + ' loading ' + url));\n  }\n\n  xhr.onreadystatechange = function () {\n    if (xhr.readyState === 4) {\n      // in Chrome on file:/// URLs, status is 0\n      if (xhr.status == 0) {\n        if (xhr.responseText) {\n          load();\n        }\n        else {\n          // when responseText is empty, wait for load or error event\n          // to inform if it is a 404 or empty file\n          xhr.addEventListener('error', error);\n          xhr.addEventListener('load', load);\n        }\n      }\n      else if (xhr.status === 200) {\n        load();\n      }\n      else {\n        error();\n      }\n    }\n  };\n  xhr.open(\"GET\", url, true);\n  xhr.send(null);\n}\n\nvar WorkerPool = function (script, size) {\n  var current = document.currentScript;\n  // IE doesn't support currentScript\n  if (!current) {\n    // Find an entry with out basename\n    var scripts = document.getElementsByTagName('script');\n    for (var i = 0; i < scripts.length; i++) {\n      if (scripts[i].src.indexOf(\"browser-es-module-loader.js\") !== -1) {\n        current = scripts[i];\n        break;\n      }\n    }\n    if (!current)\n      throw Error(\"Could not find own <script> element\");\n  }\n  script = current.src.substr(0, current.src.lastIndexOf(\"/\")) + \"/\" + script;\n  this._workers = new Array(size);\n  this._ind = 0;\n  this._size = size;\n  this._jobs = 0;\n  this.onmessage = undefined;\n  this._stopTimeout = undefined;\n  for (var i = 0; i < size; i++) {\n    var wrkr = new Worker(script);\n    wrkr._count = 0;\n    wrkr._ind = i;\n    wrkr.onmessage = this._onmessage.bind(this, wrkr);\n    wrkr.onerror = this._onerror.bind(this);\n    this._workers[i] = wrkr;\n  }\n\n  this._checkJobs();\n};\nWorkerPool.prototype = {\n  postMessage: function (msg) {\n    if (this._stopTimeout !== undefined) {\n      clearTimeout(this._stopTimeout);\n      this._stopTimeout = undefined;\n    }\n    var wrkr = this._workers[this._ind % this._size];\n    wrkr._count++;\n    this._jobs++;\n    wrkr.postMessage(msg);\n    this._ind++;\n  },\n\n  _onmessage: function (wrkr, evt) {\n    wrkr._count--;\n    this._jobs--;\n    this.onmessage(evt, wrkr);\n    this._checkJobs();\n  },\n\n  _onerror: function(err) {\n    try {\n        var evt = new Event('error');\n    } catch (_eventError) {\n        var evt = document.createEvent('Event');\n        evt.initEvent('error', true, true);\n    }\n    evt.message = err.message;\n    evt.filename = err.filename;\n    evt.lineno = err.lineno;\n    evt.colno = err.colno;\n    evt.error = err.error;\n    window.dispatchEvent(evt);\n  },\n\n  _checkJobs: function () {\n    if (this._jobs === 0 && this._stopTimeout === undefined) {\n      // wait for 2s of inactivity before stopping (that should be enough for local loading)\n      this._stopTimeout = setTimeout(this._stop.bind(this), 2000);\n    }\n  },\n\n  _stop: function () {\n    this._workers.forEach(function(wrkr) {\n      wrkr.terminate();\n    });\n  }\n};\n\nvar promiseMap = new Map();\nvar babelWorker = new WorkerPool('babel-worker.js', 3);\nbabelWorker.onmessage = function (evt) {\n    var promFuncs = promiseMap.get(evt.data.key);\n    promFuncs.resolve(evt.data);\n    promiseMap.delete(evt.data.key);\n};\n\n// instantiate just needs to run System.register\n// so we fetch the source, convert into the Babel System module format, then evaluate it\nBrowserESModuleLoader.prototype[RegisterLoader.instantiate] = function(key, processAnonRegister) {\n  var loader = this;\n\n  // load as ES with Babel converting into System.register\n  return new Promise(function(resolve, reject) {\n    // anonymous module\n    if (anonSources[key]) {\n      resolve(anonSources[key])\n      anonSources[key] = undefined;\n    }\n    // otherwise we fetch\n    else {\n      xhrFetch(key, resolve, reject);\n    }\n  })\n  .then(function(source) {\n    // check our cache first\n    var cacheEntry = localStorage.getItem(key);\n    if (cacheEntry) {\n      cacheEntry = JSON.parse(cacheEntry);\n      // TODO: store a hash instead\n      if (cacheEntry.source === source) {\n        return Promise.resolve({key: key, code: cacheEntry.code, source: cacheEntry.source});\n      }\n    }\n    return new Promise(function (resolve, reject) {\n      promiseMap.set(key, {resolve: resolve, reject: reject});\n      babelWorker.postMessage({key: key, source: source});\n    });\n  }).then(function (data) {\n    // evaluate without require, exports and module variables\n    // we leave module in for now to allow module.require access\n    try {\n      var cacheEntry = JSON.stringify({source: data.source, code: data.code});\n      localStorage.setItem(key, cacheEntry);\n    } catch (e) {\n      if (window.console) {\n        window.console.warn('Unable to cache transpiled version of ' + key + ': ' + e);\n      }\n    }\n    (0, eval)(data.code + '\\n//# sourceURL=' + data.key + '!transpiled');\n    processAnonRegister();\n  });\n};\n\n// create a default loader instance in the browser\nif (isBrowser)\n  loader = new BrowserESModuleLoader();\n\nexport default BrowserESModuleLoader;\n"], "names": ["resolvedPromise", "addToError", "<PERSON><PERSON><PERSON><PERSON>", "global"], "mappings": ";;;;;;AAAA;;;AAGA,AAAO,IAAI,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW,CAAC;AACxF,AAAO,IAAI,MAAM,GAAG,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;AAChG,AAAO,IAAI,SAAS,GAAG,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;AAEhI,IAAI,SAAS,GAAG,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG,MAAM,CAAC;AAC5D,AAEA;;;AAGA,IAAI,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AAC9C,AAAO,SAAS,YAAY,EAAE,IAAI,EAAE;EAClC,OAAO,SAAS,GAAG,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;CAC3C;;AAED,AAAO,IAAI,WAAW,GAAG,SAAS,IAAI,MAAM,CAAC,WAAW,CAAC;;AAEzD,AAAO,AAEN;;AAED,AAAO,AAON;;;;;AAKD,AAAO,IAAI,OAAO,CAAC;;;AAGnB,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,QAAQ,CAAC,oBAAoB,EAAE;EACnE,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;;EAE3B,IAAI,CAAC,OAAO,EAAE;IACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAClD,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;GAC7D;CACF;KACI,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;EACvC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC;CACzB;;;AAGD,IAAI,OAAO,EAAE;EACX,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAI,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;EAC1C,IAAI,UAAU,KAAK,CAAC,CAAC;IACnB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;CAC/C;KACI,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE;EACtD,OAAO,GAAG,SAAS,IAAI,SAAS,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;EAC7D,IAAI,SAAS;IACX,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;CACzC;KACI;EACH,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;CAC/C;;;AAGD,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;EACrC,OAAO,IAAI,GAAG,CAAC;;;;;AAKjB,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC;AAChD,SAAS,iDAAiD,EAAE,QAAQ,EAAE,UAAU,EAAE;;EAEhF,IAAI,CAAC,SAAS;IACZ,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,GAAG,cAAc,GAAG,YAAY,EAAE,EAAE,CAAC,CAAC;;EAEjF,IAAI,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,IAAI,MAAM,GAAG,UAAU,CAAC;;EAEnE,IAAI,GAAG,CAAC;EACR,IAAI,OAAO,IAAI,QAAQ,CAAC,QAAQ;IAC9B,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;;IAEjE,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;;;EAG3B,IAAI,KAAK,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;;EAE/E,IAAI,MAAM;;IAER,GAAG,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;;IAErC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;;EAEpB,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;;EAEnD,OAAO,GAAG,CAAC;CACZ,AACD,AAA0E;;ACjG1E,IAAIA,iBAAe,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;;;;;AAKxC,SAAS,WAAW,EAAE,GAAG,EAAE;EACzB,IAAI,GAAG,CAAC,MAAM;IACZ,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC;;EAEtB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;IACnD,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;;EAEnE,IAAI,QAAQ,GAAG,EAAE,CAAC;EAClB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY;IACtC,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,OAAO;MACL,IAAI,EAAE,YAAY;QAChB,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM;UACxB,OAAO;YACL,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5B,IAAI,EAAE,KAAK;WACZ,CAAC;;UAEF,OAAO;YACL,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,IAAI;WACX,CAAC;OACL;KACF,CAAC;GACH,CAAC;EACF,OAAO,QAAQ,CAAC;CACjB;;;;;;;;AAQD,SAAS,MAAM,IAAI;EACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;CAChC;;AAED,MAAM,CAAC,SAAS,CAAC,WAAW,GAAG,MAAM,CAAC;;AAEtC,SAAS,kBAAkB,EAAE,MAAM,EAAE;EACnC,IAAI,MAAM,KAAK,SAAS;IACtB,OAAO;EACT,IAAI,MAAM,YAAY,eAAe,KAAK,KAAK,IAAI,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;IACjF,MAAM,IAAI,SAAS,CAAC,+DAA+D,CAAC,CAAC;EACvF,OAAO,MAAM,CAAC;CACf;;;AAGD,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE,MAAM,EAAE;EAC/C,IAAI,OAAO,GAAG,KAAK,QAAQ;IACzB,MAAM,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;;EAEjF,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,OAAOA,iBAAe;GACrB,IAAI,CAAC,YAAY;IAChB,OAAO,MAAM,CAAC,mBAAmB,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;GACjD,CAAC;GACD,IAAI,CAAC,kBAAkB,CAAC;;GAExB,KAAK,CAAC,UAAU,GAAG,EAAE;IACpB,MAAMC,iDAAU,CAAC,GAAG,EAAE,UAAU,GAAG,GAAG,IAAI,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;GAC7E,CAAC,CAAC;CACJ,CAAC;;AAEF,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;;;;;;;;;;;AAWvD,IAAI,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,GAAG,YAAY,CAAC,oBAAoB,CAAC,CAAC;;;;AAIzF,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,UAAU,GAAG,EAAE,MAAM,EAAE;EAC7D,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC;GACjC,IAAI,CAAC,UAAU,QAAQ,EAAE;IACxB,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;GACtC,CAAC,CAAC;CACJ,CAAC;;AAEF,SAAS,gBAAgB,EAAE,WAAW,EAAE;EACtC,IAAI,WAAW,KAAK,SAAS;IAC3B,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;EAC/C,OAAO,WAAW,CAAC;CACpB;;AAED,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,GAAG,EAAE,MAAM,EAAE;EAChD,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,OAAOD,iBAAe;GACrB,IAAI,CAAC,WAAW;IACf,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;GACrC,CAAC;GACD,IAAI,CAAC,gBAAgB,CAAC;GACtB,KAAK,CAAC,UAAU,GAAG,EAAE;IACpB,MAAMC,iDAAU,CAAC,GAAG,EAAE,YAAY,GAAG,GAAG,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;GAC7E,CAAC,CAAC;CACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;AAsBF,IAAI,eAAe,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,CAAC;AACvE,IAAI,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACxC,SAAS,QAAQ,GAAG;EAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;CACrB;;AAED,IAAI,eAAe,EAAE;;EAEnB,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY;IAChD,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;GAC1C,CAAC;;;EAGF,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;IACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;MAC1D,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;KAC7B,CAAC,CAAC,CAAC;GACL,CAAC;CACH;;;AAGD,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;EACpC,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACjD,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;EACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC9B,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;IAC1D,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;GACtB,CAAC,CAAC,CAAC;CACL,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;EACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;CAC5B,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE,SAAS,EAAE;EACjD,IAAI,EAAE,SAAS,YAAY,eAAe,IAAI,SAAS,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC;IAChF,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;EAC/E,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;EAChC,OAAO,IAAI,CAAC;CACb,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE;EACtC,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;CACxD,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE;EACzC,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,EAAE;IACnD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,IAAI,CAAC;GACb;EACD,OAAO,KAAK,CAAC;CACd,CAAC;;;;;;;AAOF,IAAI,WAAW,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;AAc7C,SAAS,eAAe,EAAE,UAAU,gBAAgB;EAClD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE;IACvC,KAAK,EAAE,UAAU;GAClB,CAAC,CAAC;;;;;;;;;;;IAWD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;;CAE1D,AAAC;;AAEF,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;AAEhD,IAAI,WAAW;EACb,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,WAAW,EAAE;IAC5D,KAAK,EAAE,QAAQ;GAChB,CAAC,CAAC;;AAEL,SAAS,eAAe,EAAE,GAAG,EAAE;EAC7B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE;IAC/B,UAAU,EAAE,IAAI;IAChB,GAAG,EAAE,YAAY;MACf,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;KAC/B;GACF,CAAC,CAAC;CACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BI;;AC7QL;;;AAGA,SAAS,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE;EAC7C,MAAM,IAAI,UAAU,CAAC,qBAAqB,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;CAC5E;AACD,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,AAAO,SAAS,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE;EACpD,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;IACxD,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;EACzB,IAAI,cAAc,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;EAElF,IAAI,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;;;EAG3B,IAAI,SAAS,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE;IAC3C,IAAI,CAAC,cAAc;MACjB,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACvC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;MAC7B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;IAC/C,OAAO,cAAc,GAAG,MAAM,CAAC;GAChC;;OAEI,IAAI,SAAS,KAAK,GAAG,KAAK,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,CAAC;MACpI,MAAM,CAAC,MAAM,KAAK,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,CAAC;MACxC,SAAS,KAAK,GAAG,EAAE;IACrB,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;MAC7B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;IAC/C,IAAI,aAAa,GAAG,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;;;;IAIhF,IAAI,QAAQ,CAAC;IACb,IAAI,aAAa,EAAE;;MAEjB,IAAI,SAAS,KAAK,SAAS;QACzB,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;MACvC,QAAQ,GAAG,SAAS,CAAC;KACtB;SACI,IAAI,SAAS,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;;MAErD,IAAI,cAAc,KAAK,OAAO,EAAE;QAC9B,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;OACvD;WACI;QACH,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;OAChC;KACF;SACI;;MAEH,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACxD;;IAED,IAAI,SAAS,KAAK,GAAG,EAAE;MACrB,IAAI,aAAa;QACf,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;;QAErC,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;KAC/E;;;;;IAKD,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;;IAE3E,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;;IAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;MAEzC,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACxB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACtD,YAAY,GAAG,CAAC,CAAC,CAAC;SACnB;QACD,SAAS;OACV;;;MAGD,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;QAExB,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,EAAE;UACxF,MAAM,CAAC,GAAG,EAAE,CAAC;UACb,CAAC,IAAI,CAAC,CAAC;SACR;;aAEI,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE;UAC/D,CAAC,IAAI,CAAC,CAAC;SACR;aACI;;UAEH,YAAY,GAAG,CAAC,CAAC;UACjB,SAAS;SACV;;;QAGD,IAAI,aAAa,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;UACtC,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;;QAEvC,SAAS;OACV;;;MAGD,YAAY,GAAG,CAAC,CAAC;KAClB;;IAED,IAAI,YAAY,KAAK,CAAC,CAAC;MACrB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;;IAE9C,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;GAClF;;;;EAID,IAAI,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;EACxC,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;IACxB,IAAI,MAAM,EAAE;;MAEV,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;QACtE,OAAO,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;KAC3D;IACD,OAAO,MAAM,CAAC;GACf;CACF;;ACzHD,IAAI,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AACxC,AAEA;;;;;;;;;;;AAWA,IAAI,iBAAiB,GAAG,YAAY,CAAC,mBAAmB,CAAC,CAAC;;AAE1D,SAASC,gBAAc,IAAI;EACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAElB,IAAI,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC1C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE;IACpC,IAAI,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;;;IAG7C,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE;MAC3D,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;MACpB,OAAO,GAAG,IAAI,CAAC;KAChB;;IAED,OAAO,OAAO,CAAC;GAChB,CAAC;;EAEF,IAAI,OAAO,GAAG,EAAE,CAAC;;EAEjB,IAAI,CAAC,iBAAiB,CAAC,GAAG;;IAExB,YAAY,EAAE,SAAS;;IAEvB,OAAO,EAAE,OAAO;GACjB,CAAC;;;EAGF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACpB;;AAEDA,gBAAc,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3DA,gBAAc,CAAC,SAAS,CAAC,WAAW,GAAGA,gBAAc,CAAC;;AAEtD,IAAI,WAAW,GAAGA,gBAAc,CAAC,WAAW,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;;;AAG3EA,gBAAc,CAAC,SAAS,CAACA,gBAAc,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,SAAS,EAAE;EAC5F,OAAO,iBAAiB,CAAC,GAAG,EAAE,SAAS,IAAI,OAAO,CAAC,CAAC;CACrD,CAAC;;AAEFA,gBAAc,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE,mBAAmB,EAAE,EAAE,CAAC;;;;;AAK/E,SAAS,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE;EACnD,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG;IAC1B,GAAG,EAAE,GAAG;;;IAGR,YAAY,EAAE,YAAY;;;IAG1B,MAAM,EAAE,SAAS;;;;;IAKjB,eAAe,EAAE,SAAS;;IAE1B,SAAS,EAAE,SAAS;IACpB,SAAS,EAAE,SAAS;;;IAGpB,UAAU,EAAE;;MAEV,kBAAkB,EAAE,SAAS;MAC7B,YAAY,EAAE,SAAS;MACvB,OAAO,EAAE,SAAS;MAClB,gBAAgB,EAAE,KAAK;;;MAGvB,SAAS,EAAE,SAAS;;;MAGpB,OAAO,EAAE,SAAS;;;MAGlB,sBAAsB,EAAE,SAAS;;MAEjC,wBAAwB,EAAE,SAAS;;;MAGnC,eAAe,EAAE,SAAS;;;;;;;;KAQ3B;GACF,CAAC;CACH;;AAEDA,gBAAc,CAAC,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,UAAU,GAAG,EAAE,SAAS,EAAE;EAC9E,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;EACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;;EAEvC,OAAO,kBAAkB,CAAC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC;GACjE,IAAI,CAAC,UAAU,YAAY,EAAE;IAC5B,IAAI,YAAY,YAAY,eAAe,IAAI,YAAY,CAAC,WAAW,CAAC,KAAK,QAAQ;MACnF,OAAO,YAAY,CAAC;;;IAGtB,IAAI,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC;;;IAGnC,IAAI,CAAC,IAAI,EAAE;MACT,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO,YAAY,CAAC,MAAM,CAAC;MAC7B,MAAM,YAAY,CAAC,SAAS,CAAC;KAC9B;;IAED,OAAO,mBAAmB,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC;KACtE,IAAI,CAAC,YAAY;MAChB,OAAO,cAAc,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KACpE,CAAC,CAAC;GACJ,CAAC,CAAC;CACJ,CAAC;;AAEF,SAAS,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE;;;EAGpE,IAAI,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;EAC3B,IAAI,MAAM;IACR,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;EAEjC,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;;EAG9B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IACxB,IAAI,IAAI,CAAC,SAAS;MAChB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxC,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;GACpE;;EAED,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC;GACpC,IAAI,CAAC,UAAU,WAAW,EAAE;;IAE3B,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC/B,IAAI,MAAM;MACR,OAAO,MAAM,CAAC;;IAEhB,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;;;;;IAMlC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM;MACtB,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;;IAEzE,IAAI,IAAI,CAAC,SAAS;MAChB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;IAExC,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3B,IAAI,CAAC,IAAI;MACP,OAAO,IAAI,CAAC;;IAEd,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;GACzD,CAAC,CAAC;CACJ;;AAED,SAAS,yBAAyB,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE;EACvD,OAAO,YAAY;IACjB,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;;IAEtC,IAAI,CAAC,YAAY;MACf,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;;IAE7B,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;IAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;;IAEjC,OAAO,IAAI,CAAC;GACb,CAAC;CACH;;AAED,SAAS,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;EACzD,OAAO,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,kBAAkB;;EAE1D,CAAC,IAAI,CAAC,YAAY,GAAG,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,YAAY;IACtE,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;IAC/B,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,yBAAyB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;GACxH,CAAC;GACD,IAAI,CAAC,UAAU,aAAa,EAAE;;IAE7B,IAAI,aAAa,KAAK,SAAS,EAAE;MAC/B,IAAI,EAAE,aAAa,YAAY,eAAe,IAAI,aAAa,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC;QACxF,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;;MAE3E,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC/B,IAAI,MAAM,CAAC,KAAK;QACd,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;MAChC,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;KAC3C;;;IAGD,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;;IAErC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;IAC9B,IAAI,CAAC,YAAY;MACf,MAAM,IAAI,SAAS,CAAC,oFAAoF,CAAC,CAAC;;IAE5G,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEpC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;;IAE1B,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;;;IAGpB,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;MACnB,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC;MAC1D,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;MACxC,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;KAChC;;;SAGI;MACH,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1D;;IAED,OAAO,IAAI,CAAC;GACb,CAAC;GACD,KAAK,CAAC,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC5B,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAID,iDAAU,CAAC,GAAG,EAAE,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;GACvF,CAAC,CAAC,CAAC;CACL;;;AAGD,SAAS,qBAAqB,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;EAwBpF,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC;GACpC,IAAI,CAAC,UAAU,WAAW,EAAE;IAC3B,IAAI,WAAW;MACb,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;;;IAGjC,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACtC,IAAI,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;;;IAGnC,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC;MAC5D,OAAO,MAAM,CAAC;;IAEhB,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS;MACxB,MAAM,IAAI,CAAC,SAAS,CAAC;;;;;;IAMvB,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;MACjC,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;;IAEzE,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3B,IAAI,CAAC,IAAI;MACP,OAAO,IAAI,CAAC;;IAEd,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;GACzD,CAAC,CAAC;CACJ;;AAED,SAAS,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE;EACtC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;EAClC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;IACvB,GAAG,EAAE,IAAI,CAAC,GAAG;IACb,IAAI,EAAE,IAAI,CAAC,YAAY;IACvB,WAAW,EAAE,EAAE;IACf,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;GAC1B,CAAC;CACH;;;;;;;;;AASD,SAAS,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;EACzD,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAC/B,IAAI,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;;EAE3C,IAAI,cAAc,GAAG,KAAK,CAAC;;;EAG3B,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAACE,SAAM,EAAE,UAAU,IAAI,EAAE,KAAK,EAAE;IACzD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI,OAAO,GAAG,KAAK,CAAC;MACpB,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;QAClB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,IAAI,CAAC,KAAK,cAAc,KAAK,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE;UACzE,OAAO,GAAG,IAAI,CAAC;UACf,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;SACtB;OACF;MACD,IAAI,OAAO,KAAK,KAAK;QACnB,OAAO,KAAK,CAAC;KAChB;SACI;MACH,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,SAAS,KAAK,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK;QACpE,OAAO,KAAK,CAAC;MACf,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;KACzB;;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE;MAC7C,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;;IAEhC,OAAO,KAAK,CAAC;GACd,EAAE,IAAI,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;EAE3C,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC;EACtC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;EAChC,IAAI,QAAQ,CAAC,OAAO,EAAE;IACpB,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC;IAC9C,cAAc,GAAG,IAAI,CAAC;GACvB;CACF;;AAED,SAAS,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;EAC7D,IAAI,IAAI,CAAC,sBAAsB;IAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC;;EAErC,IAAI,uBAAuB,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;;EAE9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE;IAC/C,uBAAuB,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEjK,IAAI,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;GAChE,IAAI,CAAC,UAAU,wBAAwB,EAAE;IACxC,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;;;IAGzD,IAAI,IAAI,CAAC,OAAO,EAAE;MAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,wBAAwB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxD,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,MAAM,EAAE;UACV,IAAI,aAAa,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC;;UAEhD,IAAI,aAAa,YAAY,eAAe,IAAI,aAAa,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;YACvF,MAAM,CAAC,aAAa,CAAC,CAAC;WACvB;eACI;YACH,IAAI,aAAa,CAAC,SAAS;cACzB,MAAM,aAAa,CAAC,SAAS,CAAC;YAChC,MAAM,CAAC,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;;YAEnE,IAAI,aAAa,CAAC,eAAe;cAC/B,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;WAC9C;SACF;OACF;KACF;;IAED,OAAO,IAAI,CAAC;GACb,CAAC,CAAC;;EAEH,IAAI,MAAM,CAAC,KAAK;IACd,sBAAsB,GAAG,sBAAsB,CAAC,IAAI,CAAC,YAAY;MAC/D,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;MAC9B,OAAO,IAAI,CAAC;KACb,CAAC,CAAC;;EAEL,sBAAsB,GAAG,sBAAsB,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE;;IAEnE,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;IACxC,MAAMF,iDAAU,CAAC,GAAG,EAAE,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;GAC9C,CAAC,CAAC;;EAEH,sBAAsB,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;;EAE7C,OAAO,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;CAC7D;;AAED,SAAS,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;EACjE,IAAI,IAAI,GAAG,EAAE,CAAC;EACd,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IAC5B,IAAI,CAAC,IAAI;MACP,OAAO,eAAe,CAAC;IACzB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;MAC3B,OAAO,eAAe,CAAC;IACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;IAEhB,OAAO,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC;KAC1D,IAAI,CAAC,YAAY;MAChB,IAAI,WAAW,CAAC;MAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,IAAI,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,EAAE,OAAO,YAAY,eAAe,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,EAAE;UAC9E,WAAW,GAAG,WAAW,IAAI,EAAE,CAAC;UAChC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;SACxD;OACF;MACD,IAAI,WAAW;QACb,OAAO,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;KACnC,CAAC,CAAC;GACJ,AAAC;;EAEF,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;CAC5B;;;;;AAKDC,gBAAc,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE;EAChE,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;;;EAGpC,IAAI,OAAO,KAAK,SAAS,EAAE;IACzB,KAAK,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;GAC7C;;;OAGI;IACH,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;IACzE,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;GAChD;CACF,CAAC;;;;;AAKFA,gBAAc,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE;EACzF,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;;;EAGpC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;IAC3B,KAAK,CAAC,YAAY,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;GACpD;;;OAGI;IACH,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;IACzE,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;GACvD;CACF,CAAC;;;;AAIF,SAAS,gBAAgB,EAAE,MAAM,EAAE,GAAG,EAAE;EACtC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;EACzB,IAAI,CAAC,IAAI,GAAG;IACV,GAAG,EAAE,GAAG;;GAET,CAAC;CACH;;;;AAID,gBAAgB,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE;EACjD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;IACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACpD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;CAC1C,CAAC;;;;;AAKF,SAAS,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;EAC5D,IAAI,IAAI,CAAC,MAAM;IACb,OAAO,IAAI,CAAC,MAAM,CAAC;EACrB,IAAI,IAAI,CAAC,SAAS;IAChB,MAAM,IAAI,CAAC,SAAS,CAAC;EACvB,IAAI,IAAI,CAAC,eAAe;IACtB,OAAO,IAAI,CAAC,eAAe,CAAC;;EAE9B,IAAI,IAAI,CAAC,OAAO,EAAE;IAChB,IAAI,eAAe,GAAG,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACzF,IAAI,eAAe;MACjB,OAAO,eAAe,CAAC;GAC1B;OACI;IACH,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;GAChE;EACD,OAAO,IAAI,CAAC,MAAM,CAAC;CACpB;;AAED,SAAS,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,wBAAwB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;;EAEvG,OAAO,UAAU,IAAI,EAAE;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MAC5C,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC5B,IAAI,OAAO,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,MAAM,CAAC;;QAEX,IAAI,OAAO,YAAY,eAAe,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;UAC3E,MAAM,GAAG,OAAO,CAAC;SAClB;aACI;UACH,IAAI,OAAO,CAAC,SAAS;YACnB,MAAM,OAAO,CAAC,SAAS,CAAC;UAC1B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,EAAE;YACvG,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE;cAC9B,qBAAqB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;aACxF;iBACI;cACH,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;cACnB,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC/E;WACF;UACD,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC;SACzD;;QAED,OAAO,cAAc,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC;OAChE;KACF;IACD,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,0DAA0D,GAAG,GAAG,CAAC,CAAC;GACtG,CAAC;CACH;;AAED,SAAS,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE;EAC7B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;EAC5B,IAAI,SAAS,GAAGD,iDAAU,CAAC,GAAG,EAAE,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1D,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;IAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EAC7B,MAAM,SAAS,CAAC;CACjB;;;;AAID,SAAS,qBAAqB,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;EACzE,IAAI,OAAO,EAAE,OAAO,CAAC;EACrB,IAAI,eAAe,CAAC;EACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACjD,IAAI,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAI,OAAO,YAAY,eAAe,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK,QAAQ;MACzE,SAAS;;;IAGX,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;IAC7B,IAAI,OAAO,EAAE;MACX,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;OACpC;WACI,IAAI,OAAO,CAAC,OAAO,EAAE;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;UAChC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UACnB,IAAI;YACF,IAAI,cAAc,GAAG,qBAAqB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;WAC7F;UACD,OAAO,CAAC,EAAE;YACR,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;WACpB;UACD,IAAI,cAAc,EAAE;YAClB,eAAe,GAAG,eAAe,IAAI,EAAE,CAAC;YACxC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE;cACvD,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC;WACL;SACF;OACF;WACI;QACH,IAAI;UACF,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;SACzE;QACD,OAAO,CAAC,EAAE;UACR,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SACpB;OACF;KACF;GACF;;EAED,IAAI,eAAe;IACjB,OAAO,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;KACzD,IAAI,CAAC,YAAY;MAChB,IAAI,IAAI,CAAC,OAAO,EAAE;;;QAGhB,IAAI;UACF,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAClD;QACD,OAAO,CAAC,EAAE;UACR,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SACpB;QACD,IAAI,WAAW;UACb,OAAO,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YACpC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;WACpB,CAAC;WACD,IAAI,CAAC,YAAY;YAChB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAC5B,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;WAC/E,CAAC,CAAC;OACN;;;MAGD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;MAC5B,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACxE,CAAC,CAAC;;EAEL,IAAI,IAAI,CAAC,OAAO,EAAE;;;IAGhB,IAAI;MACF,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAClD;IACD,OAAO,CAAC,EAAE;MACR,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACpB;IACD,IAAI,WAAW;MACb,OAAO,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QAC3D,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;OACpB,CAAC;OACD,IAAI,CAAC,YAAY;QAChB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;OAC/E,CAAC,CAAC;GACN;;;EAGD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;EAC5B,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;CACxE;;;AAGD,SAAS,iBAAiB,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE;;;EAGrE,IAAI,MAAM,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;EAC9B,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAC/B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE;IACvC,YAAY,EAAE,IAAI;IAClB,GAAG,EAAE,UAAU,OAAO,EAAE;MACtB,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,YAAY,GAAG,OAAO,CAAC;KACtD;IACD,GAAG,EAAE,YAAY;MACf,OAAO,SAAS,CAAC,YAAY,CAAC;KAC/B;GACF,CAAC,CAAC;;EAEH,IAAI,OAAO,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,wBAAwB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;;EAG5H,IAAI,CAAC,IAAI,CAAC,gBAAgB;IACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE;MAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElC,IAAI;IACF,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAACE,SAAM,EAAE,OAAO,EAAE,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC3E,IAAI,MAAM,KAAK,SAAS;MACtB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;GAC3B;EACD,OAAO,CAAC,EAAE;IACR,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;GACpB;;EAED,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;;;EAG5B,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,YAAY;IAC3C,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC;;EAE9D,IAAI,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC;;;EAGtC,IAAI,aAAa,IAAI,aAAa,CAAC,UAAU,EAAE;IAC7C,KAAK,IAAI,CAAC,IAAI,aAAa,EAAE;MAC3B,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAC9C,SAAS,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;KACnC;GACF;;EAED,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;;;;EAKvE,IAAI,IAAI,CAAC,eAAe;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE;MAClD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACzC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;CAClC;;;AAGD,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,IAAI,MAAM,CAAC,MAAM;EACf,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;;AC5sB7B,IAAI,MAAM,CAAC;;;AAGX,IAAI,WAAW,GAAG,EAAE,CAAC;AACrB,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,QAAQ,CAAC,oBAAoB,EAAE;EACnE,IAAI,WAAW,GAAG,SAAS,GAAG,EAAE;;;IAG9B,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;MAC7C,IAAI;QACF,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;OAC9B,CAAC,OAAO,WAAW,EAAE;QACpB,IAAI,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;OACpC;MACD,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;MAC1B,IAAI,GAAG,CAAC,QAAQ,EAAE;QAChB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;QAC5B,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC;QAC5B,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC;OAC9B,MAAM,IAAI,GAAG,CAAC,SAAS,EAAE;QACxB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC;QAC7B,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;OACxB;MACD,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC;MAChB,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;KAC3B;;;IAGD,MAAM,GAAG,CAAC;GACX,CAAA;;EAED,IAAI,KAAK,GAAG,WAAW;IACrB,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;;IAEhE,IAAI,OAAO,GAAG,CAAC,CAAC;;IAEhB,IAAI,OAAO,GAAG,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACvC,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QAC7C,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,IAAI,MAAM,CAAC,GAAG,EAAE;UACd,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SAC9C;;aAEI;UACH,IAAI,GAAG,GAAG,SAAS,GAAG,EAAE,OAAO,GAAG,MAAM,CAAC;UACzC,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC;YACnB,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC;WACxB;;UAED,IAAI,QAAQ,GAAG,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;UAC/C,WAAW,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC;UACzC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SAC5C;OACF;KACF;GACF,CAAA;;;EAGD,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS;IACnC,UAAU,CAAC,KAAK,CAAC,CAAC;;IAElB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;CAC/D;;AAED,SAAS,qBAAqB,CAAC,OAAO,EAAE;EACtC,IAAI,OAAO;IACT,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,iBAAiB,CAAC,IAAI,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC;;EAEnGD,gBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE1B,IAAI,MAAM,GAAG,IAAI,CAAC;;;EAGlBC,SAAM,CAAC,MAAM,GAAGA,SAAM,CAAC,MAAM,IAAI,EAAE,CAAC;EACpC,IAAI,OAAOA,SAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,UAAU;IAC7C,IAAI,YAAY,GAAGA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;EAC5CA,SAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,WAAW;IAClC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACzC,IAAI,YAAY;MACd,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;GACvC,CAAC;CACH;AACD,qBAAqB,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAACD,gBAAc,CAAC,SAAS,CAAC,CAAC;;;AAG1E,qBAAqB,CAAC,SAAS,CAACA,gBAAc,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,EAAE,MAAM,EAAE;EAC9E,IAAI,QAAQ,GAAGA,gBAAc,CAAC,SAAS,CAACA,gBAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;EAC/G,IAAI,CAAC,QAAQ;IACX,MAAM,IAAI,UAAU,CAAC,mEAAmE,GAAG,GAAG,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;;EAErH,OAAO,QAAQ,CAAC;CACjB,CAAC;;AAEF,SAAS,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE;EACtC,IAAI,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;EAC/B,IAAI,IAAI,GAAG,SAAS,MAAM,EAAE;IAC1B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;GAC3B,CAAA;EACD,IAAI,KAAK,GAAG,WAAW;IACrB,MAAM,CAAC,IAAI,KAAK,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;GAClJ,CAAA;;EAED,GAAG,CAAC,kBAAkB,GAAG,YAAY;IACnC,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE;;MAExB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;QACnB,IAAI,GAAG,CAAC,YAAY,EAAE;UACpB,IAAI,EAAE,CAAC;SACR;aACI;;;UAGH,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;UACrC,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACpC;OACF;WACI,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,IAAI,EAAE,CAAC;OACR;WACI;QACH,KAAK,EAAE,CAAC;OACT;KACF;GACF,CAAC;EACF,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;EAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAChB;;AAED,IAAI,UAAU,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE;EACvC,IAAI,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC;;EAErC,IAAI,CAAC,OAAO,EAAE;;IAEZ,IAAI,OAAO,GAAG,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACvC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,EAAE;QAChE,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM;OACP;KACF;IACD,IAAI,CAAC,OAAO;MACV,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;GACtD;EACD,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC;EAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;EAChC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;EACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EAClB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EAC3B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;EAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;IAC7B,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAClD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;GACzB;;EAED,IAAI,CAAC,UAAU,EAAE,CAAC;CACnB,CAAC;AACF,UAAU,CAAC,SAAS,GAAG;EACrB,WAAW,EAAE,UAAU,GAAG,EAAE;IAC1B,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;MACnC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;MAChC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;KAC/B;IACD,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IACjD,IAAI,CAAC,MAAM,EAAE,CAAC;IACd,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACtB,IAAI,CAAC,IAAI,EAAE,CAAC;GACb;;EAED,UAAU,EAAE,UAAU,IAAI,EAAE,GAAG,EAAE;IAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;IACd,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;GACnB;;EAED,QAAQ,EAAE,SAAS,GAAG,EAAE;IACtB,IAAI;QACA,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;KAChC,CAAC,OAAO,WAAW,EAAE;QAClB,IAAI,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACtC;IACD,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAC1B,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC5B,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IACxB,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IACtB,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IACtB,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;GAC3B;;EAED,UAAU,EAAE,YAAY;IACtB,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;;MAEvD,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;KAC7D;GACF;;EAED,KAAK,EAAE,YAAY;IACjB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE;MACnC,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB,CAAC,CAAC;GACJ;CACF,CAAC;;AAEF,IAAI,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3B,IAAI,WAAW,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACvD,WAAW,CAAC,SAAS,GAAG,UAAU,GAAG,EAAE;IACnC,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7C,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5B,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACnC,CAAC;;;;AAIF,qBAAqB,CAAC,SAAS,CAACA,gBAAc,CAAC,WAAW,CAAC,GAAG,SAAS,GAAG,EAAE,mBAAmB,EAAE;EAC/F,IAAI,MAAM,GAAG,IAAI,CAAC;;;EAGlB,OAAO,IAAI,OAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;;IAE3C,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;MACpB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAA;MACzB,WAAW,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;KAC9B;;SAEI;MACH,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KAChC;GACF,CAAC;GACD,IAAI,CAAC,SAAS,MAAM,EAAE;;IAErB,IAAI,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3C,IAAI,UAAU,EAAE;MACd,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;;MAEpC,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,EAAE;QAChC,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;OACtF;KACF;IACD,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;MAC5C,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;MACxD,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;KACrD,CAAC,CAAC;GACJ,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;;;IAGtB,IAAI;MACF,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MACxE,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;KACvC,CAAC,OAAO,CAAC,EAAE;MACV,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,wCAAwC,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;OAChF;KACF;IACD,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,kBAAkB,GAAG,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC;IACrE,mBAAmB,EAAE,CAAC;GACvB,CAAC,CAAC;CACJ,CAAC;;;AAGF,IAAI,SAAS;EACX,MAAM,GAAG,IAAI,qBAAqB,EAAE,CAAC,AAEvC,AAAqC,;;,;;"}
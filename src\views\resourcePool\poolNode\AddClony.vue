<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="添加集群"
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="集群名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入主机池名称"/>
      </el-form-item>
      <!-- <el-form-item label="CPU架构">
        <el-radio-group v-model="formItem.framework">
          <el-radio value="X86">X86</el-radio>
          <el-radio value="ARM">ARM</el-radio>
          <el-radio value="MIPS">MIPS</el-radio>
          <el-radio value="loongarch64">loongarch64</el-radio>
          <el-radio value="SW">申威</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="备注">
        <el-input v-model="formItem.remark" :rows="2" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="AddClony">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { treeAddClony } from '/@/api/ResourcePool'; // 接口
import { propName } from '/@/model/resource.js'; // 表列、正则

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  addColonyTime: {
    type: String,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name:'',
  framework: 'X86',
  remark: '',
});

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" },
  ],
})
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        treeAddClony({
          pool_id: props.treeItem.id,
          name: formItem.name,
          remark: formItem.remark
        })
        .then(res => {
          if(res.code == 200) {
            ElMessage.success(res.msg)
            emit('returnOK', 'refresh');
          }else{
            ElMessage.error(res.msg)
          }
        })
      }
    })
  }
}
watch(
  ()=> props.addColonyTime,
  (val)=>{
    formItem.isShow = true;
    formItem.framework = 'X86'
    formItem.remark = ''
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
  }
);
</script>
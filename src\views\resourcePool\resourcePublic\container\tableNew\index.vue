<template>
  <el-dialog
    v-model="formItem.isShow"
    title="新建容器"
    width="750"
  >
    <div class="vm-new-area">
      <div class="vm-step-area">
        <el-steps :active="formItem.current" align-center>
          <el-step title="基本信息" :icon="Tickets" />
          <el-step title="Spec" :icon="Discount" />
          <el-step title="Volumes" :icon="ChatLineSquare" />
          <el-step title="Miscellaneous" :icon="CreditCard" />
          <el-step title="Labels" :icon="Paperclip" />
        </el-steps>
      </div>
      <div class="vm-new-content">
        <!-- 基本信息 -->
        <Basic v-show="formItem.current==0"></Basic>
        <Spec v-show="formItem.current==1"></Spec>
        <Volumes v-show="formItem.current==2"></Volumes>
        <Miscellaneous v-show="formItem.current==3"></Miscellaneous>
        <Labels v-show="formItem.current==4"></Labels>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="formItem.current>0" type="primary" @click="formItem.current--">上一步</el-button>
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button v-if="formItem.current<4" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="formItem.current==4" type="primary" @click="confirm" :loading="formItem.loading">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="AddVM">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { Tickets, Discount,ChatLineSquare,CreditCard,Paperclip } from '@element-plus/icons-vue'
import { vmNew } from '/@/api/ResourcePool/vm'; // 接口
const Basic = defineAsyncComponent(() => import('./Basic.vue'));
const Spec = defineAsyncComponent(() => import('./Spec.vue'));
const Volumes = defineAsyncComponent(() => import('./Volumes.vue'));
const Miscellaneous = defineAsyncComponent(() => import('./Miscellaneous.vue'));
const Labels = defineAsyncComponent(() => import('./Labels.vue'));


const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  newTime: {
    type: String,
    required: true
  }
});
interface forminter {
  isShow: boolean,
  loading: boolean,
  current: number,
  besicTime: string,
  besicdata: any,
  specTime: string,
  specdata: any,
}
const formItem = reactive<forminter>({
  isShow: false,
  loading: false,
  current: 0,
  besicTime: '',
  besicdata: {},
  specTime: '',
  specdata: {},
  
});
// 基本信息下一步
const nextStep = () => {
  formItem.current++
  // if(formItem.current == 0) {
  //   formItem.besicTime = ""+new Date()
  // }else if(formItem.current == 1) {
  // }
}
// 基本信息数据
const basicOK = (data: any) => {
  formItem.besicdata = data
  formItem.current++
}
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  formItem.loading = true;
  let data = {
    
  }
  vmNew(data)
  .then(res => {
    formItem.isShow = false;
    emit('returnOK', 'refresh');
  })
  .catch(error => {
    formItem.loading = false;
  })
}
watch(
  ()=> props.newTime,
  (val)=>{
    formItem.isShow = true;
    formItem.loading = false;
    formItem.current = 0
  }
);

</script>
<style lang="scss" scoped>
  .vm-new-area {
    height: 480px;
    .vm-step-area {
      margin-bottom: 10px;
      padding: 0 15%;
      height: 70px;
    }
    .vm-new-content {
      width: 100%;
      height: calc(100% - 70px);
      overflow: auto;

      .vm-new-common {
        width: 100%;
        height: 100%;
        padding: 10px 30px;
        
        .vm-new-leve {
          display: block;
          font-size: 20px;
          margin-bottom: 20px;
          cursor: pointer;
        }
      }
    }
  }
</style>
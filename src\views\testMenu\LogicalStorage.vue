<template>
	<div class="view-container-wrap">
		<el-card>
			<div class="demo-header">
				<TableSearch ref="searchRef" :searchParams="state.searchParams" :search="state.searchColumns" @search="onSearch" @onchange="searchChange" />
				<div class="btn-group">
					<el-button :icon="Search" type="primary" @click="onSearch">查询</el-button>
				</div>
			</div>
			<div class="demo-table">
				<MyTable
					ref="tableRef"
					:pagination="state.pagination"
					:searchParams="state.searchParams"
					rowKey="tenantName"
					:columns="state.columns"
					:request="getTableData"
					@selectionChange="selectionChange"
				>
					<template #level="{ row }">
						<span class="status-warn">{{ row.level }}</span>
						<span>+1233333</span>
					</template>
					<template #operation="{ row }">
            <span class="set-icon-wrap">
						  <SvgIcon name="iconfont-vrtsc icon-bianji" title="编辑" @click="editRow(row)" />
              编辑
            </span>
            <span class="set-icon-wrap">
						  <SvgIcon name="iconfont-vrtsc icon-shanchu1" title="删除" @click="deleteRow(row)" />
              删除
            </span>
						<!-- <SvgIcon name="iconfont-css icon-qianyi" title="删除2" @click="deleteRow(row)" style="color: #fe6902;" /> -->
					</template>
				</MyTable>
			</div>
		</el-card>
	</div>
</template>
<script setup lang="ts" name="LogicalStorage">
  import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
  import { Search } from '@element-plus/icons-vue';
  import { dayjs } from 'element-plus';
  // 引入组件
  const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
  const TableSearch = defineAsyncComponent(() => import('/@/components/table/Search.vue'));
  // 定义变量内容
  const searchRef = ref();
  const tableRef = ref();
  const state = reactive({
    searchParams: {} as EmptyObjectType, // 搜索参数
    searchColumns: [
      { prop: 'level', label: '级别', type: 'input', placeholder: '请输入级别', required: false, isShow: true },
      { prop: 'ip', label: '站点', type: 'select', placeholder: '请选择站点', required: false, isShow: true, options: [], isMultiple: false },
      {
        prop: 'time',
        label: '开始时间',
        type: 'datetimerange',
        placeholder: '请选择',
        required: false,
        isShow: true,
        options: [],
        isMultiple: false,
        clearable: true,
      },
      {
        prop: 'level',
        label: '级别',
        type: 'select',
        placeholder: '请选择级别',
        required: false,
        isShow: true,
        options: [],
        isMultiple: false,
        clearable: true,
      },
    ] as Array<TableSearchType>, // 搜索表单配置
    columns: [
      { type: 'selection', wrap: true },
      { label: '序号', width: '60px', type: 'index', align: 'center', sortable: true, wrap: true },
      { label: '站点', prop: 'ip', sortable: true, align: 'left', wrap: true },
      { label: '开始时间', prop: 'time', sortable: true, align: 'left', wrap: true },
      { label: '级别', tdSlot: 'level', align: 'left', wrap: true },
      { label: '操作', width: 150, tdSlot: 'operation', align: 'left' },
    ] as Array<MyTableColumns>, // 表格表头配置
    pagination: {
      show: true,
    }, // 是否显示分页
    selectList: [] as EmptyArrayType, // 选择的行数据
  });
  const onSearch = () => {
    // 搜索事件
    state.searchParams = Object.assign({}, searchRef.value.form);
    nextTick(() => {
      refreshTable();
    });
  };
  const searchChange = (params: { prop: string; value: string }) => {
    // 搜索组件下拉框改变事件，一般用于数据联动
    console.log(params);
  };
  const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
    // 调用接口
    const list = new Array(page.pageSize).fill({}).map((item, index) => {
      item = {
        ip: '192.168.1.' + index,
        time: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        level: Math.round(Math.random() * 5),
      };
      return item;
    });
    return {
      data: list, // 数据
      total: 123, // 总数
    };
  };
  const selectionChange = (rowList: EmptyArrayType) => {
    // 勾选方法
    state.selectList = rowList;
  };
  const refreshTable = () => {
    // 表格刷新事件
    tableRef.value.handleSearch();
  };
  const editRow = (row: EmptyObjectType) => {
    //编辑方法
  };
  const deleteRow = (row: EmptyObjectType) => {
    //删除方法
  };
  onMounted(() => {
    // 页面加载时
  });
</script>
<style scoped lang="scss">
  .view-container-wrap {
    width: calc(100%);
    height: calc(100%);
    min-width: 1166px;
    min-height: 600px;
    padding: 0 20px 20px 20px;
    position: absolute;
    overflow: auto;
    .el-card {
      flex: 1;
      display: flex;
      height: calc(100%);
      :deep(.el-card__body) {
        width: 100%;
        height: calc(100%);
        display: flex;
        flex-direction: column;
        .demo-header {
          display: flex;
          justify-content: space-between;
          .btn-group {
            display: flex;
            justify-content: right;
          }
        }
        .demo-table {
          // height: calc(100% - 50px);
          flex: 1;
          padding-top: 10px;
          position: relative;
          .el-table {
            flex: 1;
            .status-info {
              color: var(--el-color-info);
            }
            .status-warn {
              color: var(--el-color-warning);
            }
            .status-error {
              color: var(--el-color-error);
            }
          }
        }
      }
    }
  }
</style>
  
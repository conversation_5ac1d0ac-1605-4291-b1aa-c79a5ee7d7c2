import request from '/@/utils/request';
// 镜像 查询
export const imageQuery = () => {
	return request({
		url: '/theapi/v2/images',
		method: 'get',
	});
};
// 镜像 新建
export const imageNew = (data: object) => {
	return request({
		url: '/theapi/v2/images/create',
		method: 'post',
		data,
	});
};
// 镜像 删除
export const imageDelete = (data: object) => {
	return request({
		url: '/theapi/v2/images/delete',
		method: 'delete',
		data,
	});
};

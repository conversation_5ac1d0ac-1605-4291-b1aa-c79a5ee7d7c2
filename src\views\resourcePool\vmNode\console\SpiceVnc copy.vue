<template>
  <div class="spice-console">
    <!-- SPICE连接选择 -->
    <div class="connection-options">
      <el-card class="option-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Monitor /></el-icon>
            <span>SPICE远程控制台</span>
          </div>
        </template>

        <!-- 连接方式选择 -->
        <div class="connection-methods">
          <el-radio-group v-model="state.connectionMethod" @change="handleMethodChange">
            <el-radio value="html5" class="method-radio">
              <div class="method-content">
                <el-icon class="method-icon"><ChromeFilled /></el-icon>
                <div class="method-info">
                  <h4>HTML5客户端</h4>
                  <p>在浏览器中直接连接，无需安装</p>
                </div>
              </div>
            </el-radio>
            <el-radio value="native" class="method-radio">
              <div class="method-content">
                <el-icon class="method-icon"><Download /></el-icon>
                <div class="method-info">
                  <h4>原生客户端</h4>
                  <p>下载客户端程序，性能更佳</p>
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 连接信息 -->
        <div class="connection-info">
          <h4>连接信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">服务器:</span>
              <span class="value">{{ state.host }}</span>
            </div>
            <div class="info-item">
              <span class="label">端口:</span>
              <span class="value">{{ state.port }}</span>
            </div>
            <div class="info-item">
              <span class="label">协议:</span>
              <span class="value">SPICE</span>
            </div>
            <div class="info-item">
              <span class="label">状态:</span>
              <el-tag :type="getStatusType(state.status)">{{ state.status }}</el-tag>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            type="primary"
            @click="connectSpice"
            :loading="state.connecting"
            :disabled="!state.host || !state.port"
          >
            {{ state.connecting ? '连接中...' : '连接SPICE' }}
          </el-button>
          <el-button
            v-if="state.connected"
            @click="disconnectSpice"
          >
            断开连接
          </el-button>
          <el-button
            v-if="state.connectionMethod === 'native'"
            @click="downloadClient"
          >
            下载客户端
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- HTML5 SPICE客户端区域 -->
    <div v-if="state.connectionMethod === 'html5'" class="html5-console">
      <el-card v-if="!state.connected" class="placeholder-card">
        <div class="placeholder-content">
          <el-icon class="placeholder-icon"><Monitor /></el-icon>
          <h3>SPICE HTML5控制台</h3>
          <p>点击"连接SPICE"按钮开始远程连接</p>
        </div>
      </el-card>

      <!-- SPICE HTML5客户端容器 -->
      <div v-if="state.connected" class="spice-container">
        <div class="spice-toolbar">
          <div class="toolbar-left">
            <el-tag type="success">已连接</el-tag>
            <span class="connection-time">{{ state.connectionTime }}</span>
          </div>
          <div class="toolbar-right">
            <el-button size="small" @click="sendCtrlAltDel">Ctrl+Alt+Del</el-button>
            <el-button size="small" @click="toggleFullscreen">全屏</el-button>
            <el-button size="small" @click="disconnectSpice">断开</el-button>
          </div>
        </div>
        <div id="spice-area" class="spice-area" ref="spiceAreaRef"></div>
      </div>
    </div>

    <!-- 原生客户端信息 -->
    <div v-if="state.connectionMethod === 'native'" class="native-client">
      <el-card class="native-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Download /></el-icon>
            <span>原生SPICE客户端</span>
          </div>
        </template>

        <div class="native-content">
          <div class="download-section">
            <h4>下载SPICE客户端</h4>
            <p>选择适合您操作系统的SPICE客户端：</p>
            <div class="download-options">
              <el-button @click="downloadClient('windows')" class="download-btn">
                <el-icon><Platform /></el-icon>
                Windows客户端
              </el-button>
              <el-button @click="downloadClient('macos')" class="download-btn">
                <el-icon><Platform /></el-icon>
                macOS客户端
              </el-button>
              <el-button @click="downloadClient('linux')" class="download-btn">
                <el-icon><Platform /></el-icon>
                Linux客户端
              </el-button>
            </div>
          </div>

          <div class="connection-guide">
            <h4>连接步骤</h4>
            <ol class="guide-steps">
              <li>下载并安装适合您系统的SPICE客户端</li>
              <li>打开SPICE客户端程序</li>
              <li>输入连接信息：
                <div class="connection-details">
                  <code>spice://{{ state.host }}:{{ state.port }}</code>
                </div>
              </li>
              <li>点击连接开始远程会话</li>
            </ol>
          </div>

          <div class="spice-file-section">
            <h4>SPICE连接文件</h4>
            <p>您也可以下载.spice文件，双击直接打开：</p>
            <el-button @click="downloadSpiceFile" type="primary">
              <el-icon><Download /></el-icon>
              下载.spice文件
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 连接状态提示 -->
    <div v-if="state.showStatus" class="status-alert">
      <el-alert
        :title="state.statusMessage"
        :type="state.statusType"
        :closable="true"
        @close="state.showStatus = false"
        show-icon
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, onMounted, nextTick, ref, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Monitor,
  ChromeFilled,
  Download,
  Platform
} from '@element-plus/icons-vue';

// 类型定义
interface SpiceConnectionInfo {
  host: string;
  port: string;
  password?: string;
  tls?: boolean;
}

// 扩展Window类型以支持SPICE
declare global {
  interface Window {
    SpiceMainConn: any;
    spice_connection: any;
  }
}

// 定义变量内容
const state = reactive({
  connectionMethod: 'html5' as 'html5' | 'native',
  host: '',
  port: '',
  password: '',
  connecting: false,
  connected: false,
  status: '未连接',
  connectionTime: '',
  showStatus: false,
  statusMessage: '',
  statusType: 'info' as 'success' | 'warning' | 'info' | 'error',
  spiceClient: null as any,
  connectionTimer: null as any,
});

const spiceAreaRef = ref<HTMLElement>();
const emit = defineEmits(['returnOK']);

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已连接': return 'success';
    case '连接中': return 'warning';
    case '连接失败': return 'danger';
    default: return 'info';
  }
};

// 连接方式改变
const handleMethodChange = (method: string) => {
  if (state.connected) {
    disconnectSpice();
  }
};

// 连接SPICE
const connectSpice = async () => {
  if (state.connectionMethod === 'html5') {
    await connectHTML5Spice();
  } else {
    showNativeClientInfo();
  }
};

// HTML5 SPICE连接
const connectHTML5Spice = async () => {
  try {
    state.connecting = true;
    state.status = '连接中';
    showStatus('正在连接SPICE服务器...', 'info');

    // 检查并加载SPICE HTML5客户端
    await loadSpiceHTML5Client();

    // 建立SPICE连接
    await establishSpiceConnection();

    state.connected = true;
    state.status = '已连接';
    state.connectionTime = new Date().toLocaleTimeString();
    showStatus('SPICE连接成功！', 'success');

    // 启动连接计时器
    startConnectionTimer();

  } catch (error) {
    state.status = '连接失败';
    showStatus('SPICE连接失败: ' + (error instanceof Error ? error.message : String(error)), 'error');
    console.error('SPICE连接错误:', error);
  } finally {
    state.connecting = false;
  }
};

// 加载SPICE HTML5客户端
const loadSpiceHTML5Client = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 如果已经加载，直接返回
    if (window.SpiceMainConn) {
      resolve();
      return;
    }

    // SPICE HTML5客户端脚本列表
    const spiceScripts = [
      '/spice-html5/spicearraybuffer.js',
      '/spice-html5/spiceconn.js',
      '/spice-html5/spicemsg.js',
      '/spice-html5/spicetype.js',
      '/spice-html5/spiceutils.js',
      '/spice-html5/spice.js'
    ];

    let loadedCount = 0;
    const totalScripts = spiceScripts.length;

    // 动态加载脚本
    spiceScripts.forEach((scriptSrc, index) => {
      const script = document.createElement('script');
      script.src = scriptSrc;
      script.async = false; // 保证按顺序加载

      script.onload = () => {
        loadedCount++;
        if (loadedCount === totalScripts) {
          // 等待一小段时间确保所有脚本都已初始化
          setTimeout(() => {
            if (window.SpiceMainConn) {
              resolve();
            } else {
              reject(new Error('SPICE客户端加载失败'));
            }
          }, 100);
        }
      };

      script.onerror = () => {
        reject(new Error(`加载SPICE脚本失败: ${scriptSrc}`));
      };

      document.head.appendChild(script);
    });
  });
};

// 建立SPICE连接
const establishSpiceConnection = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      const spiceArea = spiceAreaRef.value || document.getElementById('spice-area');
      if (!spiceArea) {
        reject(new Error('SPICE显示区域未找到'));
        return;
      }

      // 清空容器
      spiceArea.innerHTML = '';

      // 构建WebSocket URI
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUri = `${protocol}//${state.host}:${state.port}`;

      // 创建SPICE连接
      state.spiceClient = new window.SpiceMainConn({
        uri: wsUri,
        screen_id: spiceArea,
        dump_id: 'debug-div',
        message_id: 'message-div',
        password: state.password || '',
        onerror: (err: any) => {
          console.error('SPICE连接错误:', err);
          reject(new Error('SPICE连接失败'));
        },
        onsuccess: () => {
          console.log('SPICE连接成功');
          resolve();
        },
        ondisconnect: () => {
          console.log('SPICE连接断开');
          handleDisconnect();
        }
      });

    } catch (error) {
      reject(error);
    }
  });
};

// 断开SPICE连接
const disconnectSpice = () => {
  if (state.spiceClient) {
    try {
      state.spiceClient.stop();
    } catch (error) {
      console.error('断开SPICE连接时出错:', error);
    }
    state.spiceClient = null;
  }

  // 清理状态
  state.connected = false;
  state.status = '未连接';
  state.connectionTime = '';

  // 清空SPICE容器
  const spiceArea = spiceAreaRef.value || document.getElementById('spice-area');
  if (spiceArea) {
    spiceArea.innerHTML = '';
  }

  // 停止计时器
  if (state.connectionTimer) {
    clearInterval(state.connectionTimer);
    state.connectionTimer = null;
  }

  showStatus('SPICE连接已断开', 'warning');
};

// 处理连接断开
const handleDisconnect = () => {
  state.connected = false;
  state.status = '连接断开';
  showStatus('SPICE连接意外断开', 'error');
};

// 发送Ctrl+Alt+Del
const sendCtrlAltDel = () => {
  if (state.spiceClient && state.spiceClient.inputs) {
    try {
      // 发送Ctrl+Alt+Del组合键
      state.spiceClient.inputs.sendKey(0x1d, 1); // Ctrl down
      state.spiceClient.inputs.sendKey(0x38, 1); // Alt down
      state.spiceClient.inputs.sendKey(0x53, 1); // Del down

      setTimeout(() => {
        state.spiceClient.inputs.sendKey(0x53, 0); // Del up
        state.spiceClient.inputs.sendKey(0x38, 0); // Alt up
        state.spiceClient.inputs.sendKey(0x1d, 0); // Ctrl up
      }, 100);

      ElMessage.success('已发送Ctrl+Alt+Del');
    } catch (error) {
      ElMessage.error('发送组合键失败');
    }
  }
};

// 切换全屏
const toggleFullscreen = () => {
  const spiceArea = spiceAreaRef.value || document.getElementById('spice-area');
  if (!spiceArea) return;

  if (!document.fullscreenElement) {
    spiceArea.requestFullscreen().catch(err => {
      ElMessage.error('无法进入全屏模式');
    });
  } else {
    document.exitFullscreen();
  }
};

// 显示原生客户端信息
const showNativeClientInfo = () => {
  showStatus('请下载并安装SPICE客户端，然后使用连接信息进行连接', 'info');
};

// 下载客户端
const downloadClient = (platform?: string) => {
  const downloads = {
    windows: 'https://www.spice-space.org/download/windows/spice-guest-tools/spice-guest-tools-latest.exe',
    macos: 'https://www.spice-space.org/download/osx/',
    linux: 'https://www.spice-space.org/download.html'
  };

  let downloadUrl = '';

  if (platform) {
    downloadUrl = downloads[platform as keyof typeof downloads];
  } else {
    // 自动检测操作系统
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) {
      downloadUrl = downloads.windows;
    } else if (userAgent.includes('Mac')) {
      downloadUrl = downloads.macos;
    } else {
      downloadUrl = downloads.linux;
    }
  }

  window.open(downloadUrl, '_blank');
  ElMessage.info('正在打开SPICE客户端下载页面...');
};

// 下载SPICE连接文件
const downloadSpiceFile = () => {
  const spiceConfig = `[virt-viewer]
type=spice
host=${state.host}
port=${state.port}
password=${state.password || ''}
title=SPICE Remote Console
toggle-fullscreen=shift+f11
release-cursor=shift+f12
secure-attention=ctrl+alt+end`;

  const blob = new Blob([spiceConfig], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = `spice-${state.host}-${state.port}.spice`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  ElMessage.success('SPICE连接文件已下载');
};

// 启动连接计时器
const startConnectionTimer = () => {
  const startTime = Date.now();
  state.connectionTimer = setInterval(() => {
    const elapsed = Date.now() - startTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    state.connectionTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, 1000);
};

// 显示状态信息
const showStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  state.statusMessage = message;
  state.statusType = type;
  state.showStatus = true;

  // 自动隐藏成功和信息提示
  if (type === 'success' || type === 'info') {
    setTimeout(() => {
      state.showStatus = false;
    }, 3000);
  }
};

// 打开弹窗
const openDialog = async (connectionInfo: SpiceConnectionInfo) => {
  nextTick(() => {
    state.host = connectionInfo.host;
    state.port = connectionInfo.port;
    state.password = connectionInfo.password || '';
    state.status = '未连接';

    // 重置状态
    if (state.connected) {
      disconnectSpice();
    }
  });
};

// 组件卸载时清理
onUnmounted(() => {
  disconnectSpice();
});

// 暴露变量
defineExpose({
  openDialog,
  connectSpice,
  disconnectSpice,
});
</script>
<style lang="scss" scoped>
.spice-console {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: #f5f7fa;

  .connection-options {
    .option-card {
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #303133;

        .header-icon {
          font-size: 20px;
          color: #409eff;
        }
      }

      .connection-methods {
        margin-bottom: 20px;

        .method-radio {
          display: block;
          margin-bottom: 15px;
          padding: 15px;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
            background-color: #f0f9ff;
          }

          .method-content {
            display: flex;
            align-items: center;
            gap: 12px;

            .method-icon {
              font-size: 24px;
              color: #409eff;
            }

            .method-info {
              h4 {
                margin: 0 0 4px 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
              }

              p {
                margin: 0;
                font-size: 14px;
                color: #606266;
              }
            }
          }
        }
      }

      .connection-info {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 6px;

            .label {
              font-weight: 500;
              color: #606266;
            }

            .value {
              color: #303133;
              font-family: 'Courier New', monospace;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        justify-content: center;
      }
    }
  }

  .html5-console {
    flex: 1;
    display: flex;
    flex-direction: column;

    .placeholder-card {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-content {
        text-align: center;

        .placeholder-icon {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 16px;
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          color: #303133;
        }

        p {
          margin: 0;
          color: #606266;
        }
      }
    }

    .spice-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .spice-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;

        .toolbar-left {
          display: flex;
          align-items: center;
          gap: 12px;

          .connection-time {
            font-family: 'Courier New', monospace;
            color: #606266;
            font-size: 14px;
          }
        }

        .toolbar-right {
          display: flex;
          gap: 8px;
        }
      }

      .spice-area {
        flex: 1;
        background-color: #000;
        position: relative;
        min-height: 400px;

        canvas {
          width: 100% !important;
          height: 100% !important;
          object-fit: contain;
        }
      }
    }
  }

  .native-client {
    .native-card {
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #303133;

        .header-icon {
          font-size: 20px;
          color: #409eff;
        }
      }

      .native-content {
        .download-section {
          margin-bottom: 30px;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 0 0 16px 0;
            color: #606266;
          }

          .download-options {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;

            .download-btn {
              display: flex;
              align-items: center;
              gap: 8px;
            }
          }
        }

        .connection-guide {
          margin-bottom: 30px;

          h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .guide-steps {
            margin: 0;
            padding-left: 20px;

            li {
              margin-bottom: 8px;
              color: #606266;
              line-height: 1.6;

              .connection-details {
                margin-top: 8px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #409eff;

                code {
                  font-family: 'Courier New', monospace;
                  color: #e6a23c;
                  font-weight: 600;
                }
              }
            }
          }
        }

        .spice-file-section {
          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 0 0 16px 0;
            color: #606266;
          }
        }
      }
    }
  }

  .status-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    min-width: 300px;
  }
}

// 全屏样式
:deep(.spice-area:fullscreen) {
  background-color: #000;

  canvas {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: contain;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .spice-console {
    padding: 10px;
    gap: 15px;

    .connection-options .option-card .connection-info .info-grid {
      grid-template-columns: 1fr;
    }

    .native-client .native-content .download-section .download-options {
      flex-direction: column;
    }
  }
}
</style>
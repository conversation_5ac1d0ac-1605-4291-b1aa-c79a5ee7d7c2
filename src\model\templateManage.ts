const templateColumns = [
	{ type: 'selection', wrap: true },
	{ label: '模板名称', prop: 'name' },
	{ label: 'CPU架构', tdSlot: 'cpu_arch', align: 'center' },
	{ label: 'CPU数量', tdSlot: 'vcpu', align: 'center' },
	{ label: '创建时间', prop: 'created_at', align: 'center' },
	{ label: '更新时间', prop: 'updated_at', align: 'center' },
	{ label: '存储类型', tdSlot: 'disk_type_code', align: 'center' },
	{ label: '磁盘', prop: 'disk_name', align: 'center' },
	{ label: '内存', tdSlot: 'memory', align: 'center' },
	{ label: '网络', prop: 'network', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: '120px', wrap: true },
];
const frameworkConvert = (item: string) => {
	switch (item) {
		case 'SW':
			return '申威';
		default:
			return item;
	}
};
const typeConvert = (item: string) => {
	switch (item) {
		case 'local':
			return '本地存储';
		case 'ceph':
			return '分布式存储';
		case 'fc_san':
			return 'FC-SAN';
		case 'nas':
			return 'NAS';
		default:
			return item;
	}
};

const propName = (rule: any, value: any, callback: any) => {
	const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
	if (!regex.test(value)) {
		callback(new Error('2-32 个中文、英文、数字、特殊字符@_.-'));
	} else {
		callback();
	}
};
const propCPU = (rule: any, value: any, callback: any) => {
	const regex = /^[1-9]\d*$/;
	if (!regex.test(value)) {
		callback(new Error('请输入1以上的整数'));
	} else {
		callback();
	}
};
const propMEM = (rule: any, value: any, callback: any) => {
	const regex = /^[1-9]\d*$/;
	if (!regex.test(value)) {
		callback(new Error('请输入1以上的整数'));
	} else {
		callback();
	}
};
export { templateColumns, frameworkConvert, typeConvert, propName, propCPU, propMEM };

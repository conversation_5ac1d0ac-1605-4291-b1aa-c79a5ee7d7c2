<template>
  <el-dialog
    v-model="formItem.isShow"
    title="新建存储池"
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="存储池名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入存储池名称"/>
      </el-form-item>
      <el-form-item label="存储类型">
        <el-select v-model="formItem.type" :disabled="formItem.disabled" style="width: 100%">
          <el-option v-for="item in formItem.typeData" :key="item.code" :label="item.name" :value="item.code" />
				</el-select>
      </el-form-item>
      <el-form-item label="存储目录" prop="dir">
        <el-input v-model="formItem.dir"  placeholder="请输入存储目录"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="StoreNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { storagePollNew, storageTypeQuery } from '/@/api/ResourcePool/storage.ts'; // 接口
import { propName } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  newTime: {
    type: String,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name:'',
  type: 'local',
  dir: '',
  disabled: false,
  typeData: [],
});

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "change" },
  ],
  dir: [
    { required: true, message: '必填项', trigger: 'blur' },
  ]
})
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        storagePollNew({
          name: formItem.name,
          storage_local_dir: formItem.dir,
          host: props.treeItem.ip,
        })
        .then((res:any) => {
          if(res.msg == 'ok') {
            ElMessage.success('新建存储池操作完成');
            emit('returnOK', 'refresh');
          }else {
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}
const typeQuery = ()=>{
  storageTypeQuery().then((res:any)=>{
    formItem.typeData = res
    formItem.type = res[0].code
  })
}
watch(
  ()=> props.newTime,
  (val)=>{
    formItem.isShow = true;
    typeQuery()
    if( props.treeItem.level ==3 ) {
      formItem.disabled = true
    } else {
      formItem.disabled = false
    }
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
  }
);
</script>
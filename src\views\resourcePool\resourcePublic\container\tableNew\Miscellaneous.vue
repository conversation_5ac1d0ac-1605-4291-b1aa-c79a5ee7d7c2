<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="工作目录">
			<el-input v-model="formItem.directory" placeholder="请输入运行命令的工作目录" />
		</el-form-item>
    <el-form-item label="环境变量">
			<el-input v-model="formItem.describe" :rows="4" show-word-limit type="textarea" placeholder="请输入环境变量。例：KEY1=VALUE1,KEY2=VALUE2..."/>
		</el-form-item>
    <el-form-item label="启用交互模式">
			<el-checkbox v-model="formItem.interactive" />
		</el-form-item>
	</el-form>
</template>
<script lang="ts" setup name="AddBasic">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { propCPU, propMem } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  listTime: {
    type: String,
    required: true
  }
});
const formRef = ref<FormInstance>()
// 基本信息
const formItem = reactive({
  directory: '',
  variables: '',
  interactive: true
});
const rulesForm = reactive<FormRules>({
  cpu: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propCPU, trigger: "change" },
  ],
  memory: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propMem, trigger: "change" },
  ],
})
const emit = defineEmits(['listOK']);
watch(
  ()=> props.listTime,
  (val)=>{
    emit('listOK', {
      workdir: formItem.directory,
      environment: formItem.variables,
      interactive: formItem.interactive,
    });
  }
);
</script>
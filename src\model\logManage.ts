// 监控告警-告警管理
const allColumns = [
	{ type: 'selection', wrap: true },
	{ label: '告警名称', prop: 'summary' },
	{ label: '类别', prop: 'category', align: 'center' },
	{ label: '模块', prop: 'module', align: 'center' },
	{ label: '告警时间', prop: 'activeAt', align: 'center' },
	{ label: '告警详情', prop: 'description', align: 'center' },
	{ label: '告警类型', tdSlot: 'severity', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: '120px', wrap: true },
];

// 集群-存储告警
const alarmColumns = [
	{ type: 'selection', wrap: true },
	{ label: '告警名称', prop: 'summary' },
	{ label: '告警时间', prop: 'activeAt', align: 'center' },
	{ label: '告警详情', prop: 'description', align: 'center' },
	{ label: '告警类型', tdSlot: 'severity', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: '120px', wrap: true },
];
// 告警规则
const ruleColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name' },
	{ label: '类别', tdSlot: 'job', align: 'center' },
	{ label: '严重告警', tdSlot: 'critical_value', align: 'center' },
	{ label: '重要告警', tdSlot: 'major_value', align: 'center' },
	{ label: '次要告警', tdSlot: 'warning_value', align: 'center' },
	{ label: '提示告警', tdSlot: 'info_value', align: 'center' },
	{ label: '持续时间', tdSlot: 'for_interval', align: 'center' },
	{ label: '状态', tdSlot: 'status', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: '120px', wrap: true },
];
// 日志
const logColumns = [
	{ label: '时间', prop: 'date' },
	// { label: '时间', tdSlot: 'date', align: 'center' },
	{ label: '类别', prop: 'servername', align: 'center' },
	{ label: '用户', prop: 'system', align: 'center' },
	{ label: '角色', tdSlot: 'role', align: 'center' },
	{ label: '操作类型', prop: 'clientip', align: 'center' },
	{ label: '描述', prop: 'text', align: 'center' },
	{ label: '结果', tdSlot: 'loglevel', align: 'center', width: '120px' },
];
// 类型转换
const typeConversion = (type: string, severity: string) => {
	let color = '#000';
	let text = '-';
	switch (severity) {
		case 'critical':
			color = '#ff7842';
			text = '严重';
			break;
		case 'major':
			color = '#ffa12d';
			text = '重要';
			break;
		case 'warning':
			color = '#fff82d';
			text = '次要';
			break;
		case 'info':
			color = '#656eea';
			text = '提示';
			break;
		case 'page':
			color = '#da4c18';
			text = '紧急通知';
			break;
		case 'none':
			color = '#ccc';
			text = '-';
			break;
		default:
			color = '#ccc';
			text = '-';
	}
	if (type == 'color') {
		return color;
	} else {
		return text;
	}
};

// 日志搜索
const logSearch = (isShow: boolean) => {
	const shijian = {
		prop: 'times',
		label: '时间',
		type: 'datetimerange',
		placeholder: '请选择',
		required: false,
		isShow: true,
		options: [],
		isMultiple: false,
		clearable: true,
	};
	const zhanghao = isShow
		? {
				prop: 'username',
				label: '用户',
				type: 'input',
				placeholder: '请输入',
				required: false,
				isShow: true,
		  }
		: null;
	const leibie = isShow
		? {
				prop: 'category',
				label: '类别',
				type: 'select',
				placeholder: '请选择',
				required: false,
				isShow: true,
				options: [],
				isMultiple: false,
				clearable: true,
		  }
		: null;
	const jiaose = isShow
		? {
				prop: 'role',
				label: '角色',
				type: 'select',
				placeholder: '请选择',
				required: false,
				isShow: true,
				options: [],
				isMultiple: false,
				clearable: true,
		  }
		: null;
	const leixing = {
		prop: 'action',
		label: '类型',
		type: 'select',
		placeholder: '请选择',
		required: false,
		isShow: true,
		options: [],
		isMultiple: false,
		clearable: true,
	};
	const jieguo = {
		prop: 'result',
		label: '结果',
		type: 'select',
		placeholder: '请选择',
		required: false,
		isShow: true,
		options: [],
		isMultiple: false,
		clearable: true,
	};
	return [shijian, zhanghao, leibie, jiaose, leixing, jieguo].filter(Boolean);
};
// 默认时间
const defaultTime = (time: number) => {
	let current = new Date(); // 获取当前时间
	let start = new Date(current.getTime() + time * 60 * 60 * 1000); // 获取N小时前的时间
	let year = start.getFullYear(); // 获取年
	let month = ('0' + (start.getMonth() + 1)).slice(-2); // 获取月-月份从0开始，所以要加1
	let day = ('0' + start.getDate()).slice(-2); // 获取日
	let hours = ('0' + start.getHours()).slice(-2); // 获取时
	let minutes = ('0' + start.getMinutes()).slice(-2); // 获取分
	let seconds = ('0' + start.getSeconds()).slice(-2); // 获取秒

	// 输出一小时前的时间结果
	return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds; // 起始时间
};
// cookie查询
const cookieList = (coo: string) => {
	let list = coo.split('; ');
	let role = '';
	list.forEach((item) => {
		let itemArr = item.split('=');
		if (itemArr[0] == 'role') {
			role = itemArr[1];
		}
	});
	let roleData = new Array();
	if (role == 'sysadm' || role == 'adtadm') {
		roleData = [
			{ label: '系统管理员', value: 'sysadm' },
			{ label: '审计员', value: 'adtadm' },
			{ label: '安全员', value: 'secadm' },
			{ label: '操作员', value: 'operator' },
		];
	} else if (role == 'secadm') {
		roleData = [
			{ label: '审计员', value: 'adtadm' },
			{ label: '操作员', value: 'operator' },
		];
	} else if (role == 'operator') {
		roleData = [];
	}
	return roleData;
};
export { allColumns, alarmColumns, ruleColumns, logColumns, typeConversion, logSearch, defaultTime, cookieList };

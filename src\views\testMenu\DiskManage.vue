<template>
	<div class="content-wrap layout-padding">
		<el-card>
			<div class="search-group">
				<el-form :inline="true" :model="formData" ref="formRef">
					<el-row>
						<el-col :span="18">
							<el-row :gutter="10">
								<el-col :span="8">
									<el-form-item label="任务状态" prop="step" class="search-row-span">
										<el-select v-model="formData.step" placeholder="请选择任务状态" clearable class="search-row-span">
											<el-option label="完成" :value="0" />
											<el-option label="创建中" :value="1" />
											<el-option label="介质准备" :value="4" />
											<el-option label="分配迁移器" :value="6" />
											<el-option label="执行中" :value="8" />
											<el-option label="失败" :value="9" />
											<el-option label="取消" :value="10" />
											<el-option label="手动取消" :value="11" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="任务类型" prop="taskType" class="search-row-span">
										<el-select v-model="formData.taskType" placeholder="请选择任务类型" clearable class="search-row-span">
											<el-option label="数据归档" :value="1" />
											<el-option label="客户端归档(D2D)" :value="13" />
											<el-option label="客户端归档(D2T)" :value="14" />
											<el-option label="数据恢复" :value="6" />
											<el-option label="数据清理" :value="7" />
											<el-option label="磁带复制" :value="8" />
											<el-option label="磁带格式化" :value="9" />
											<el-option label="ORACLE备份" :value="10" />
											<el-option label="ORACLE挂载" :value="11" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="8">
									<el-form-item label="任务号" prop="taskId" class="search-row-span">
										<el-input v-model="formData.taskId" placeholder="请输入任务号" clearable />
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
						<el-col :span="5" :offset="1">
							<el-form-item>
								<el-button type="primary" v-antiShake="onSubmit" :icon="Search">查询</el-button>
								<el-button type="success" @click="exportData()" :icon="Download">导出</el-button>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
			<div class="table-content">
				<el-table :data="tableData" height="100%" style="width: 100%" v-loading="loading">
					<el-table-column prop="date" label="时间" width="180" />
					<el-table-column prop="name" label="名字" width="180" />
					<el-table-column prop="address" label="地址" />
				</el-table>
			</div>
			<div class="pagination-wrap">
				<el-pagination
				  :pager-count="paginationData.pageNumber"
					:page-size="paginationData.pageSize"
					:page-sizes="[10, 20, 50, 100]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="paginationData.total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts" name="DiskManage">
	import table2excel from 'js-table2excel';
	import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
	import { ElMessageBox, ElMessage, dayjs } from 'element-plus';
	import { exportToExcel, ExportColumn } from '../../utils/exportExcels';
	import { Plus, Minus, Refresh, Edit, Link, Delete, Search, CopyDocument, Lock, Download } from '@element-plus/icons-vue'
	// 定义变量内容
	const loading = ref(true)

	const formData = reactive({ // form表单
		step: '',
		taskId: '',
		taskType: '',
	})
	const paginationData = reactive({
		pageNumber: 5,
		pageSize: 10,
		total: 12
	})
	const onSubmit = () => {// 搜索事件
	}
	interface Person {
		date: string;
		name: string;
		address: string;
	}
	const tableData = [
		{
			date: '2016-05-03',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-02',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-06',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-07',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-01',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-08',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-06',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
		{
			date: '2016-05-07',
			name: 'Tom',
			address: 'No. 189, Grove St, Los Angeles',
		},
	]
	const exportData = ()=> { //导出数据
		const columns: ExportColumn[] = [
			{ header: '名字', key: 'name', width: 120 },
			{ header: '时间', key: 'date', width: 150 },
			{ header: '地址', key: 'address', width: 260 },
			{ header: '测试数据', key: 'test', width: 260 },
		];
		const datas: Person[]  = tableData
		const filename = `数据导出- ${new Date().toLocaleString()}.xlsx`
		exportToExcel(columns, datas, filename)
	}	
	const handleSizeChange = (val: number) => {
		console.log(`${val} items per page`)
	}
	const handleCurrentChange = (val: number) => {
		console.log(`current page: ${val}`)
	}
	// 页面加载时
	onMounted(() => {
		setTimeout(() => {
			loading.value = !loading.value
		}, 1000)
	});
</script>
<style scoped lang="scss">
	.content-wrap {
		padding-top: 0 !important;
		width: 100%;
		height: 100%;
		.container-header {
			display: flex;
			justify-content: space-between;
			height: 60px;
			line-height: 60px;
			.el-button {
				width: 40px;
				height: 40px;
				:deep(.el-icon) {
						font-size: 20px;
						font-weight: bold;
				}
			}
		}
		.el-card {
			flex: 1;
			display: flex;
			--el-card-padding: 30px 30px 10px 30px;
			:deep(.el-card__body) {
				display: flex;
				flex-direction: column;
				flex: 1;
				overflow: auto;
				.toolip-box {
					display: flex;
					justify-content: space-between;
					.btn-group {
						display: flex;
						justify-content: right;
					}
				}
				.table-content {
					flex: 1;
					padding-top: 10px;
					overflow: hidden;
				}
			}
			.pagination-wrap {
				width: 100%;
				height: 50px;
				display: flex;
				justify-content: flex-end;
				align-items: center;
			}
		}
		:deep(th) {
				background: var(--el-table-header-th);
			}
		:deep(.el-scrollbar__view) {
			width: 100%;
		}
		:deep(.el-scrollbar) {
			--el-scrollbar-bg-color: var(--el-color-primary);
			--el-scrollbar-hover-bg-color: var(--el-color-primary);

			.el-scrollbar__thumb {
				opacity: 1;
			}
		}
		:deep(.el-table__body) {
			width: 100%;
			--el-table-row-hover-bg-color: transparent;
			.hover-row {
				background: linear-gradient(to right, var(--next-color-hover-tr1) 0%, var(--next-color-hover-tr2) 50%, var(--next-color-hover-tr3) 100%);
			}
			tr {
				&:hover {
					background: linear-gradient(to right, var(--next-color-hover-tr1) 0%, var(--next-color-hover-tr2) 50%, var(--next-color-hover-tr3) 100%);
				}
			}
			.el-table-column--selection {
				.cell {
					display: block;
				}
			}
		}
		:deep(.el-table__cell) {
			height: 56px;
			border-right: none;
		}
		:deep(.el-table__header) {
			.el-table__cell {
				height: 44px;
				background: var(--next-bg-main-color);
				color: var(--el-text-color-primary);
			}
		}
	}
</style>

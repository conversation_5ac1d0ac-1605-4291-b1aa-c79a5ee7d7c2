/*
 * @see https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_code_values
 */
export const code_to_scancode = [];
code_to_scancode["Escape"]                    = 0x01;
code_to_scancode["Digit1"]                    = 0x02;
code_to_scancode["Digit2"]                    = 0x03;
code_to_scancode["Digit3"]                    = 0x04;
code_to_scancode["Digit4"]                    = 0x05;
code_to_scancode["Digit5"]                    = 0x06;
code_to_scancode["Digit6"]                    = 0x07;
code_to_scancode["Digit7"]                    = 0x08;
code_to_scancode["Digit8"]                    = 0x09;
code_to_scancode["Digit9"]                    = 0x0A;
code_to_scancode["Digit0"]                    = 0x0B;
code_to_scancode["Minus"]                     = 0x0C;
code_to_scancode["Equal"]                     = 0x0D;
code_to_scancode["Backspace"]                 = 0x0E;
code_to_scancode["Tab"]                       = 0x0F;
code_to_scancode["KeyQ"]                      = 0x10;
code_to_scancode["KeyW"]                      = 0x11;
code_to_scancode["KeyE"]                      = 0x12;
code_to_scancode["KeyR"]                      = 0x13;
code_to_scancode["KeyT"]                      = 0x14;
code_to_scancode["KeyY"]                      = 0x15;
code_to_scancode["KeyU"]                      = 0x16;
code_to_scancode["KeyI"]                      = 0x17;
code_to_scancode["KeyO"]                      = 0x18;
code_to_scancode["KeyP"]                      = 0x19;
code_to_scancode["BracketLeft"]               = 0x1A;
code_to_scancode["BracketRight"]              = 0x1B;
code_to_scancode["Enter"]                     = 0x1C;
code_to_scancode["ControlLeft"]               = 0x1D;
code_to_scancode["KeyA"]                      = 0x1E;
code_to_scancode["KeyS"]                      = 0x1F;
code_to_scancode["KeyD"]                      = 0x20;
code_to_scancode["KeyF"]                      = 0x21;
code_to_scancode["KeyG"]                      = 0x22;
code_to_scancode["KeyH"]                      = 0x23;
code_to_scancode["KeyJ"]                      = 0x24;
code_to_scancode["KeyK"]                      = 0x25;
code_to_scancode["KeyL"]                      = 0x26;
code_to_scancode["Semicolon"]                 = 0x27;
code_to_scancode["Quote"]                     = 0x28;
code_to_scancode["Backquote"]                 = 0x29;
code_to_scancode["ShiftLeft"]                 = 0x2A;
code_to_scancode["Backslash"]                 = 0x2B;
code_to_scancode["KeyZ"]                      = 0x2C;
code_to_scancode["KeyX"]                      = 0x2D;
code_to_scancode["KeyC"]                      = 0x2E;
code_to_scancode["KeyV"]                      = 0x2F;
code_to_scancode["KeyB"]                      = 0x30;
code_to_scancode["KeyN"]                      = 0x31;
code_to_scancode["KeyM"]                      = 0x32;
code_to_scancode["Comma"]                     = 0x33;
code_to_scancode["Period"]                    = 0x34;
code_to_scancode["Slash"]                     = 0x35;
code_to_scancode["ShiftRight"]                = 0x36;
code_to_scancode["NumpadMultiply"]            = 0x37;
code_to_scancode["AltLeft"]                   = 0x38;
code_to_scancode["Space"]                     = 0x39;
code_to_scancode["CapsLock"]                  = 0x3A;
code_to_scancode["F1"]                        = 0x3B;
code_to_scancode["F2"]                        = 0x3C;
code_to_scancode["F3"]                        = 0x3D;
code_to_scancode["F4"]                        = 0x3E;
code_to_scancode["F5"]                        = 0x3F;
code_to_scancode["F6"]                        = 0x40;
code_to_scancode["F7"]                        = 0x41;
code_to_scancode["F8"]                        = 0x42;
code_to_scancode["F9"]                        = 0x43;
code_to_scancode["F10"]                       = 0x44;
code_to_scancode["Pause"]                     = 0x45;
code_to_scancode["ScrollLock"]                = 0x46;
code_to_scancode["Numpad7"]                   = 0x47;
code_to_scancode["Numpad8"]                   = 0x48;
code_to_scancode["Numpad9"]                   = 0x49;
code_to_scancode["NumpadSubtract"]            = 0x4A;
code_to_scancode["Numpad4"]                   = 0x4B;
code_to_scancode["Numpad5"]                   = 0x4C;
code_to_scancode["Numpad6"]                   = 0x4D;
code_to_scancode["NumpadAdd"]                 = 0x4E;
code_to_scancode["Numpad1"]                   = 0x4F;
code_to_scancode["Numpad2"]                   = 0x50;
code_to_scancode["Numpad3"]                   = 0x51;
code_to_scancode["Numpad0"]                   = 0x52;
code_to_scancode["NumpadDecimal"]             = 0x53;
code_to_scancode["PrintScreen"]               = 0x54;
code_to_scancode["IntlBackslash"]             = 0x56;
code_to_scancode["F11"]                       = 0x57;
code_to_scancode["F12"]                       = 0x58;
code_to_scancode["NumpadEqual"]               = 0x59;
code_to_scancode["F13"]                       = 0x64;
code_to_scancode["F14"]                       = 0x65;
code_to_scancode["F15"]                       = 0x66;
code_to_scancode["F16"]                       = 0x67;
code_to_scancode["F17"]                       = 0x68;
code_to_scancode["F18"]                       = 0x69;
code_to_scancode["F19"]                       = 0x6A;
code_to_scancode["F20"]                       = 0x6B;
code_to_scancode["F21"]                       = 0x6C;
code_to_scancode["F22"]                       = 0x6D;
code_to_scancode["F23"]                       = 0x6E;
code_to_scancode["KanaMode"]                  = 0x70;
code_to_scancode["IntlRo"]                    = 0x73;
code_to_scancode["F24"]                       = 0x76;
code_to_scancode["Convert"]                   = 0x79;
code_to_scancode["NonConvert"]                = 0x7B;
code_to_scancode["IntlYen"]                   = 0x7D;
code_to_scancode["NumpadComma"]               = 0x7E;
code_to_scancode["MediaTrackPrevious"]        = 0xE0 | (0x10 << 8);
code_to_scancode["MediaTrackNext"]            = 0xE0 | (0x19 << 8);
code_to_scancode["NumpadEnter"]               = 0xE0 | (0x1C << 8);
code_to_scancode["ControlRight"]              = 0xE0 | (0x1D << 8);
code_to_scancode["AudioVolumeMute"]           = 0xE0 | (0x20 << 8);
code_to_scancode["LaunchApp2"]                = 0xE0 | (0x21 << 8);
code_to_scancode["MediaPlayPause"]            = 0xE0 | (0x22 << 8);
code_to_scancode["MediaStop"]                 = 0xE0 | (0x24 << 8);
code_to_scancode["VolumeDown"]                = 0xE0 | (0x2E << 8);
code_to_scancode["VolumeUp"]                  = 0xE0 | (0x30 << 8);
code_to_scancode["BrowserHome"]               = 0xE0 | (0x32 << 8);
code_to_scancode["NumpadDivide"]              = 0xE0 | (0x35 << 8);
code_to_scancode["PrintScreen"]               = 0xE0 | (0x37 << 8);
code_to_scancode["AltRight"]                  = 0xE0 | (0x38 << 8);
code_to_scancode["NumLock"]                   = 0xE0 | (0x45 << 8);
code_to_scancode["Pause"]                     = 0xE0 | (0x46 << 8);
code_to_scancode["Home"]                      = 0xE0 | (0x47 << 8);
code_to_scancode["ArrowUp"]                   = 0xE0 | (0x48 << 8);
code_to_scancode["PageUp"]                    = 0xE0 | (0x49 << 8);
code_to_scancode["ArrowLeft"]                 = 0xE0 | (0x4B << 8);
code_to_scancode["ArrowRight"]                = 0xE0 | (0x4D << 8);
code_to_scancode["End"]                       = 0xE0 | (0x4F << 8);
code_to_scancode["ArrowDown"]                 = 0xE0 | (0x50 << 8);
code_to_scancode["PageDown"]                  = 0xE0 | (0x51 << 8);
code_to_scancode["Insert"]                    = 0xE0 | (0x52 << 8);
code_to_scancode["Delete"]                    = 0xE0 | (0x53 << 8);
code_to_scancode["MetaLeft"]                  = 0xE0 | (0x5B << 8);
code_to_scancode["MetaRight"]                 = 0xE0 | (0x5C << 8);
code_to_scancode["ContextMenu"]               = 0xE0 | (0x5D << 8);
code_to_scancode["Power"]                     = 0xE0 | (0x5E << 8);
code_to_scancode["BrowserSearch"]             = 0xE0 | (0x65 << 8);
code_to_scancode["BrowserFavorites"]          = 0xE0 | (0x66 << 8);
code_to_scancode["BrowserRefresh"]            = 0xE0 | (0x67 << 8);
code_to_scancode["BrowserStop"]               = 0xE0 | (0x68 << 8);
code_to_scancode["BrowserForward"]            = 0xE0 | (0x69 << 8);
code_to_scancode["BrowserBack"]               = 0xE0 | (0x6A << 8);
code_to_scancode["LaunchApp1"]                = 0xE0 | (0x6B << 8);
code_to_scancode["LaunchMail"]                = 0xE0 | (0x6C << 8);
code_to_scancode["MediaSelect"]               = 0xE0 | (0x6D << 8);

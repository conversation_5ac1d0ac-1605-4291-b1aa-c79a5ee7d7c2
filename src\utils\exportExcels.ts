import * as XLSX from 'xlsx';

export interface ExportColumn {
  header?: string; //头部
  key: string;  //头部对应的值的key名字
  width?: number; //列宽
}

// 导出table表格的数据，根据实际的需求来修改参数来达到自己的要求
export function exportToExcel<T>(
  columns: ExportColumn[] = [],
  data: T[],
  filename: string = `数据导出- ${new Date().toLocaleString()}.xlsx`,
): void {
  // 创建一个数组，数组的第一行是表头
  const transformedData = data.map(item => {
    const newItem: any = {};
    columns.forEach(({ key, header }) => {
      newItem[header || key] = (item as any)[key];
    });
    return newItem;
  });

  // 根据 transformedData 生成工作表
  const worksheet = XLSX.utils.json_to_sheet(transformedData);

  // 设置列宽
  if (columns.length > 0) {
    worksheet['!cols'] = columns.map(col => ({
      wpx: col.width || 100 // 如果未设置宽度，默认使用 100px
    }));
  }

  // 创建一个新的工作簿
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

  // 导出Excel文件
  XLSX.writeFile(workbook, filename);
}

<template>
	<div class="tabs-item-area">
		<div class="tabs-item-btn">
			<el-button type="primary" v-show="statusOnOff(state.status)" round>{{ state.status !== '关机' ? '关机' : '开机' }}</el-button>
			<el-button type="primary" v-show="statusPowerSupply(state.status)" round>{{
				state.status !== '关闭电源' ? '关闭电源' : '开启电源 '
			}}</el-button>
			<el-button type="primary" v-show="statusPause(state.status)" round>{{ state.status !== '暂停' ? '暂停' : '恢复' }}</el-button>
			<el-button type="primary" v-show="statusHibernate(state.status)" round>{{ state.status == '休眠' ? '恢复' : '休眠' }}</el-button>
			<el-button type="primary" v-show="statusRestart(state.status)" round>重启</el-button>
			<el-button type="primary" round>编辑</el-button>
			<el-button type="primary" v-show="statusDelete(state.status)" round @click="deleteClick">删除</el-button>
			<el-button type="primary" round @click="consoleClick">控制台</el-button>
			<el-button type="primary" round>快照管理</el-button>
			<el-dropdown trigger="click" @command="groupOperation">
				<el-button type="primary" round style="margin-left: 10px">
					更多<el-icon class="el-icon--right"><ArrowDownBold /></el-icon>
				</el-button>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item command="wqkl">完全克隆</el-dropdown-item>
						<el-dropdown-item command="ljkl">链接克隆</el-dropdown-item>
						<el-dropdown-item command="qy">迁移</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<span @click="statusQuery">当前状态：{{ state.status }}--点击更换</span>
		</div>
		<div class="route-list">
			<div v-for="item in state.routeList" :class="{ 'route-item': true, 'is-active': isActive(item) }" :key="item" @click="tagClick(item)">
				<span
					><span>{{ item }}</span></span
				>
			</div>
		</div>
		<div class="tabs-item-center">
			<VirtualMachineSummary v-if="state.acive == '概要'" :treeItem="props.treeItem" :acive="state.acive"></VirtualMachineSummary>
			<Backups v-if="state.acive == '虚拟机备份'" :treeItem="props.treeItem" :acive="state.acive"></Backups>
			<OperationLog v-if="state.acive == '运行日志'" :treeItem="props.treeItem" :acive="state.acive"></OperationLog>
			<MigrationLog v-if="state.acive == '虚拟机迁移日志'" :treeItem="props.treeItem" :acive="state.acive"></MigrationLog>
			<Monitor v-if="state.acive == '性能监控'" :treeItem="props.treeItem" :acive="state.acive"></Monitor>
			<Alarm v-if="state.acive == '告警'" :treeItem="props.treeItem" :acive="state.acive"></Alarm>
			<Task v-if="state.acive == '任务'" :treeItem="props.treeItem" :acive="state.acive"></Task>
			<Console v-if="state.acive == '控制台'" :treeItem="props.treeItem" :acive="state.acive"></Console>
		</div>
    <VmDelete :deleteTime="state.deleteTime" :treeItem="props.treeItem" @deleteOK="deleteOK"></VmDelete>
	</div>
</template>
<script setup lang="ts" name="PoolNode">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { dayjs, ElMessage } from 'element-plus';
import { vmOverview } from '/@/api/ResourcePool'; // 接口
import { recycleVmDelete,vmMoveinRecycle } from '/@/api/ResourcePool/vm'; // 接口

const VirtualMachineSummary = defineAsyncComponent(() => import('./VirtualMachineSummary.vue'));
const Backups = defineAsyncComponent(() => import('./backups/index.vue'));
const OperationLog = defineAsyncComponent(() => import('./operationLog/index.vue'));
const MigrationLog = defineAsyncComponent(() => import('./migrationLog/index.vue'));
const Monitor = defineAsyncComponent(() => import('./monitor/index.vue'));
const Alarm = defineAsyncComponent(() => import('./alarm/index.vue'));
const Task = defineAsyncComponent(() => import('./task/index.vue'));
const Console = defineAsyncComponent(() => import('./console/index.vue'));

const VmDelete = defineAsyncComponent(() => import('./VmDelete.vue'));

const props = defineProps({
	treeItem: {
		type: Object,
		required: true,
	},
});
const state = reactive({
	routeList: ['概要', '虚拟机备份', '运行日志', '虚拟机迁移日志', '性能监控', '告警', '任务','控制台'],
	acive: '概要',
	status: '',
	deleteTime: '',
});
const statusQuery = () => {
	vmOverview(props.treeItem.id).then((res) => {
		let libs = ['开机', '关机', '暂停', '关闭电源','休眠'];
		state.status = libs[Math.round(Math.random() * 4)];
	});
};
const tagClick = (v: string) => {
	state.acive = v;
};
const isActive = (v: string) => {
	if (state.acive === v) {
		return true;
	} else {
		return false;
	}
};
const statusON = (item: string) => {
	// 关闭电源 、 关机
	// 删除可用
};
// 状态 删除判断
const statusDelete = (item: string) => {
	if (item == '关闭电源' || item == '关机') {
		return true;
	} else {
		return false;
	}
};
// 状态 重启判断
const statusRestart = (item: string) => {
	if (item == '开机' || item == '暂停' || item == '休眠') {
		return true;
	} else {
		return false;
	}
};
// 状态 休眠判断
const statusHibernate = (item: string) => {
	if (item == '开机' || item == '暂停' || item == '休眠') {
		return true;
	} else {
		return false;
	}
};
// 状态 暂停判断
const statusPause = (item: string) => {
	if (item == '开机' || item == '暂停' || item == '恢复') {
		return true;
	} else {
		return false;
	}
};
// 状态 电源判断
const statusPowerSupply = (item: string) => {
	if (item == '开机' || item == '暂停' || item == '休眠' || item == '关机' || item == '关闭电源') {
		return true;
	} else {
		return false;
	}
};
// 状态 开关机判断
const statusOnOff = (item: string) => {
	if (item == '重启' || item == '关闭电源') {
		return false;
	} else {
		return true;
	}
};
// 表格群操作
const groupOperation = (item: string) => {
	switch (item) {
		case 'wqkl':
			console.log('完全克隆');
			break;
		case 'ljkl':
			console.log('链接克隆');
			break;
		case 'qy':
			console.log('迁移');
			break;
		default:
			console.log('其他');
	}
};
const emit = defineEmits(['returnOK']);
const returnOK = (item: string) => {
	emit('returnOK', item);
};
const listQuery = () => {
	let status = ['开机', '暂停', '休眠', '关机', '关闭电源', '重启'];
	state.status = status[Math.round(Math.random() * 5)];
};
// 删除
const deleteClick = () => {
  state.deleteTime = '虚拟机/'+new Date()
};
const consoleClick = () => {
  console.log('控制台',props.treeItem);
}
// 删除返回
const deleteOK = (item:string)=>{
  if(item == 'delete'){
    recycleVmDelete({
      names: [props.treeItem.name],
      ids: [props.treeItem.id],
    })
    .then(res => {
      if(res.msg == 'ok'){
        ElMessage.success('彻底删除虚拟机操作完成');
				emit('returnOK', 'delete');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    vmMoveinRecycle({
      names: [props.treeItem.name],
      ids: [props.treeItem.id],
    })
    .then(res => {
      if(res.msg == 'ok'){
        ElMessage.success('删除虚拟机移入回收站操作完成');
        emit('returnOK', 'delete');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }
}
onMounted(() => {
	listQuery();
});
watch(
	() => props.treeItem,
	(val) => {
		state.acive = '概要';
		listQuery();
	}
);
</script>
<style lang="scss" scoped>
.tabs-item-area {
	width: calc(100%);
	height: calc(100%);
	.route-list {
		width: calc(100% - 40px);
		height: 55px;
		// background: var(--el-fill-color-blank);
		background: #faf7f7;
		border-radius: 26px;
		margin: 10px;
		padding: 0 20px;
		display: flex;
		flex-wrap: wrap;
		align-items: center;

		.route-item {
			position: relative;
			padding: 0 20px;
			font-size: 14px;
			line-height: 50px;
			cursor: pointer;
			margin: 0 10px;
			color: var(--el-color-title);
			border-radius: 3px;
			display: flex;
			height: 75%;
			align-items: center;

			&:hover {
				background: var(--el-color-primary-light-9);
				font-weight: bold;
				color: var(--el-color-primary);
				&::before {
					content: ' ';
					position: absolute;
					width: 4px;
					height: 18px;
					top: 50%;
					transform: translateY(-50%);
					background: var(--el-color-primary);
					left: 0;
				}
			}
		}

		.is-active {
			// background: var(--el-color-primary-light-9);
			background: #fff9f5;
			font-weight: bold;
			color: var(--el-color-primary);
			&::before {
				content: ' ';
				position: absolute;
				width: 4px;
				height: 18px;
				top: 50%;
				transform: translateY(-50%);
				background: var(--el-color-primary);
				left: 0;
			}
		}
	}
	.tabs-item-center {
		padding: 10px;
		border-radius: 10px;
		width: calc(100%);
		height: calc(100% - 110px);
		background: var(--el-fill-color-blank);
	}
}
</style>

<template>
<div>
	<el-form ref="formRef" label-position="left" label-width="150" :model="formModel">
		<div v-for="(item, index) in driveData" :key="'drive' + index">
			<el-form-item>
				<template #label>
					<div class="vm-new-label">
						<span>{{ index == 0 ? '光驱' : '光驱' + index }}</span>
					</div>
				</template>
				<el-input v-model="item.drive" disabled>
					<template #append>
						<!-- <el-button :icon="Search" @click="searchClick1(index)"/> -->
						<el-button :icon="Search" @click="searchClick(index)"/>
						<el-button v-if="item.remove" @click="removeDrive(index)" :icon="Delete" style="margin-left: 20px" />
					</template>
				</el-input>
			</el-form-item>
			<!-- <el-form-item label="网卡多队列" v-show="item.drive !== ''">
				<el-input v-model="item.drivestorage" disabled />
			</el-form-item>-->
		</div>
	</el-form>
	<SelectDrive ref="driveRef" @drive-return="driveReturn" />
	<SelectExisting ref="existingRef" @existing-return="existingReturn" />
</div>
</template>
<script lang="ts" setup name="AddDrive">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';
const SelectDrive = defineAsyncComponent(() => import('./SelectDrive.vue'));
const SelectExisting = defineAsyncComponent(() => import('./SelectExisting.vue'));

const props = defineProps({
	treeItem: {
    type: Object,
    required: true
  },
	driveAdd: {
		type: Number,
		required: true,
	},
});
const formData = reactive({
	driveIndex: 0,
})
const formModel = ref({}); // 可以动态填充你的表单数据
const formRef = ref<FormInstance>();
const emit = defineEmits(['driveOK']);
// 硬件信息-光驱
let driveData = reactive([
  {
    remove: false,
    drive: '',
    drivestorage: '',
  }
])

// 选择光驱
const driveRef = ref();
const searchClick1 = (item: any) => {
	driveRef.value.driveDialog()
	formData.driveIndex = item
};
// 选择光驱返回
const driveReturn = (item: any) => {
	driveData[formData.driveIndex].drive = item.tableName
	driveData[formData.driveIndex].drivestorage = item.tableID
};
const existingRef = ref();
const searchClick = (item: any) => {
	existingRef.value.existingDialog(props.treeItem)
	formData.driveIndex = item
};
// 现有磁盘选择选中
const existingReturn = (item: any)=>{
  // driveData[formData.driveIndex].existingPoolName = item.poolName
  driveData[formData.driveIndex].drive = item.tableName
  driveData[formData.driveIndex].drivestorage = item.tablePath
  // driveData[formData.driveIndex].existingType = item.tableType
  // driveData[formData.driveIndex].disk = item.tableSize.match(/\d+/g)[0]
  // driveData[formData.driveIndex].unit = item.tableSize.match(/[a-zA-Z]+/g)[0];
}
watch(
	() => props.driveAdd,
	(val) => {
		driveData.push({
      remove: true,
      drive: '',
      drivestorage: '',
    })
	}
);
// 删除光驱
const removeDrive = (item: any)=>{
  driveData.splice(item,1)
}

</script>
<style lang="scss" scoped>
.vm-new-label {
	display: flex;
	align-items: center;
	cursor: pointer;
	> span {
		padding-left: 10px;
	}
}
</style>
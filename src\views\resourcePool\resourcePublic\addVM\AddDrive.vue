<template>
<div>
	<el-form ref="formRef" label-position="left" label-width="150" :model="formModel">
		<div v-for="(item, index) in driveData" :key="'drive' + index">
			<el-form-item>
				<template #label>
					<div class="vm-new-label">
						<span>{{ index == 0 ? '光驱' : '光驱' + index }}</span>
					</div>
				</template>
				<el-input v-model="item.drive" disabled>
					<template #append>
						<el-button :icon="Search" @click="selectDrive(index)"/>
						<el-button v-if="item.remove" @click="removeDrive(index)" :icon="Delete" style="margin-left: 20px" />
					</template>
				</el-input>
			</el-form-item>
			<!-- <el-form-item label="网卡多队列" v-show="item.drive !== ''">
				<el-input v-model="item.drivestorage" disabled />
			</el-form-item>-->
			<el-form-item></el-form-item> 
		</div>
	</el-form>
	<SelectDrive :driveDialog="formData.driveDialog" @drive-return="driveReturn"></SelectDrive>
</div>
</template>
<script lang="ts" setup name="AddDrive">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';
const SelectDrive = defineAsyncComponent(() => import('./SelectDrive.vue'));

const props = defineProps({
	driveAdd: {
		type: Number,
		required: true,
	},
});
const formData = reactive({
	driveDialog: '',
	driveIndex: 0,
})
const formModel = ref({}); // 可以动态填充你的表单数据
const formRef = ref<FormInstance>();
const emit = defineEmits(['driveOK']);
// 硬件信息-光驱
let driveData = reactive([
  {
    remove: false,
    drive: '',
    drivestorage: '',
  }
])

// 选择光驱
const selectDrive = (item: any) => {
	formData.driveDialog = " " +new Date()
	formData.driveIndex = item
};
// 选择光驱返回
const driveReturn = (item: any) => {
	driveData[formData.driveIndex].drive = item.tableName
	driveData[formData.driveIndex].drivestorage = item.tableID
};
watch(
	() => props.driveAdd,
	(val) => {
		driveData.push({
      remove: true,
      drive: '',
      drivestorage: '',
    })
	}
);
// 删除光驱
const removeDrive = (item: any)=>{
  driveData.splice(item,1)
}

</script>
<style lang="scss" scoped>
.vm-new-label {
	display: flex;
	align-items: center;
	cursor: pointer;
	> span {
		padding-left: 10px;
	}
}
</style>
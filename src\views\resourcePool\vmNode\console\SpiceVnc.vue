<template>
  <div>
    spice
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 定义变量内容
const state = reactive({
  
});
const emit = defineEmits(['returnOK']);
// 打开弹窗
const openDialog = async (port: string) => {
	nextTick(() => {
    
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>

</style>
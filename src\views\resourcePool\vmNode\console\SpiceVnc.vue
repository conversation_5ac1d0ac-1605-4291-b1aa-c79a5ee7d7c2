<template>
  <div class="spice-native-client">
    <!-- 标题栏 -->
    <div class="header-section">
      <div class="header-info">
        <el-icon class="header-icon"><Monitor /></el-icon>
        <h2>SPICE远程桌面</h2>
        <el-tag :type="getStatusType(state.status)" size="small">{{ state.status }}</el-tag>
      </div>
      <div class="connection-info">
        <span class="info-item">{{ state.host }}:{{ state.port }}</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- SPICE纯客户端选项 -->
      <el-card class="client-card">
        <template #header>
          <div class="card-header">
            <el-icon><Desktop /></el-icon>
            <span>SPICE纯客户端连接</span>
          </div>
        </template>

        <div class="client-options">
          <!-- 快速连接 -->
          <div class="quick-connect">
            <h3>🚀 快速连接</h3>
            <p>下载配置文件，双击即可连接</p>
            <el-button type="primary" size="large" @click="downloadSpiceFile">
              <el-icon><Download /></el-icon>
              下载 .spice 连接文件
            </el-button>
          </div>

          <!-- 手动连接 -->
          <div class="manual-connect">
            <h3>⚙️ 手动连接</h3>
            <div class="connection-details">
              <div class="detail-row">
                <span class="label">协议:</span>
                <code>spice://</code>
              </div>
              <div class="detail-row">
                <span class="label">服务器:</span>
                <code>{{ state.host }}</code>
              </div>
              <div class="detail-row">
                <span class="label">端口:</span>
                <code>{{ state.port }}</code>
              </div>
              <div class="detail-row">
                <span class="label">完整地址:</span>
                <el-input
                  v-model="state.fullAddress"
                  readonly
                  class="address-input"
                >
                  <template #append>
                    <el-button @click="copyAddress">复制</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>

          <!-- 客户端下载 -->
          <div class="client-download">
            <h3>📥 下载SPICE客户端</h3>
            <p>选择适合您操作系统的SPICE客户端</p>
            <div class="download-buttons">
              <el-button @click="downloadClient('windows')" class="download-btn">
                <el-icon><Platform /></el-icon>
                Windows
              </el-button>
              <el-button @click="downloadClient('macos')" class="download-btn">
                <el-icon><Platform /></el-icon>
                macOS
              </el-button>
              <el-button @click="downloadClient('linux')" class="download-btn">
                <el-icon><Platform /></el-icon>
                Linux
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 连接指南 -->
      <el-card class="guide-card">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>连接指南</span>
          </div>
        </template>

        <el-tabs v-model="state.activeTab" class="guide-tabs">
          <el-tab-pane label="快速开始" name="quick">
            <div class="guide-content">
              <h4>🎯 三步快速连接</h4>
              <ol class="step-list">
                <li>
                  <strong>下载客户端</strong>
                  <p>选择上方对应您操作系统的SPICE客户端并安装</p>
                </li>
                <li>
                  <strong>下载配置文件</strong>
                  <p>点击"下载 .spice 连接文件"按钮</p>
                </li>
                <li>
                  <strong>双击连接</strong>
                  <p>双击下载的 .spice 文件即可自动连接</p>
                </li>
              </ol>
            </div>
          </el-tab-pane>

          <el-tab-pane label="手动连接" name="manual">
            <div class="guide-content">
              <h4>🔧 手动配置连接</h4>
              <ol class="step-list">
                <li>
                  <strong>打开SPICE客户端</strong>
                  <p>启动已安装的SPICE客户端程序</p>
                </li>
                <li>
                  <strong>输入连接信息</strong>
                  <p>服务器: <code>{{ state.host }}</code></p>
                  <p>端口: <code>{{ state.port }}</code></p>
                  <p>或直接输入: <code>{{ state.fullAddress }}</code></p>
                </li>
                <li>
                  <strong>开始连接</strong>
                  <p>点击连接按钮开始远程会话</p>
                </li>
              </ol>
            </div>
          </el-tab-pane>

          <el-tab-pane label="故障排除" name="troubleshoot">
            <div class="guide-content">
              <h4>🛠️ 常见问题解决</h4>
              <div class="troubleshoot-list">
                <div class="issue-item">
                  <h5>❌ 连接被拒绝</h5>
                  <ul>
                    <li>检查服务器地址和端口是否正确</li>
                    <li>确认SPICE服务正在运行</li>
                    <li>检查防火墙设置</li>
                  </ul>
                </div>
                <div class="issue-item">
                  <h5>❌ 客户端无法启动</h5>
                  <ul>
                    <li>确认已正确安装SPICE客户端</li>
                    <li>检查操作系统兼容性</li>
                    <li>尝试以管理员权限运行</li>
                  </ul>
                </div>
                <div class="issue-item">
                  <h5>❌ 性能问题</h5>
                  <ul>
                    <li>检查网络连接质量</li>
                    <li>调整客户端显示设置</li>
                    <li>关闭不必要的视觉效果</li>
                  </ul>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 状态提示 -->
    <div v-if="state.showStatus" class="status-toast">
      <el-alert
        :title="state.statusMessage"
        :type="state.statusType"
        :closable="true"
        @close="state.showStatus = false"
        show-icon
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 定义变量内容
const state = reactive({
  
});
const emit = defineEmits(['returnOK']);
// 打开弹窗
const openDialog = async (port: string) => {
	nextTick(() => {
    console.log(port)
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>

</style>
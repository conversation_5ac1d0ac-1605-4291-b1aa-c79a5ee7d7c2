<template>
  <div class="spice-iframe-container">
    <iframe
      ref="spiceIframeRef"
      :src="state.iframeSrc"
      frameborder="0"
      class="spice-iframe"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
import { reactive, nextTick, ref } from 'vue';
import { ElMessage } from 'element-plus';
// 定义变量内容
const state = reactive({
  iframeSrc: ''
});

const spiceIframeRef = ref<HTMLIFrameElement>();
const emit = defineEmits(['returnOK']);
// 打开弹窗并设置连接信息
const openDialog = async (website: string, port: string) => {
  nextTick(() => {
    console.log('======',website,'----',port)
    state.iframeSrc='/spice-html5/spice.html'
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.spice-iframe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  .spice-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: #f5f7fa;
  }
}
</style>
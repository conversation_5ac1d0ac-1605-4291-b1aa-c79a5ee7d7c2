<template>
  <div class="spice-console">
    <!-- 连接状态栏 -->
    <div class="connection-status-bar">
      <div class="status-info">
        <el-icon class="status-icon"><Monitor /></el-icon>
        <span class="status-text">SPICE HTML5控制台</span>
        <el-tag :type="getStatusType(state.status)" size="small">{{ state.status }}</el-tag>
      </div>
      <div class="connection-details">
        <span class="detail-item">{{ state.host }}:{{ state.port }}</span>
        <span v-if="state.connected" class="detail-item">{{ state.connectionTime }}</span>
      </div>
    </div>

    <!-- SPICE控制台容器 -->
    <div class="spice-container">
      <!-- 连接中状态 -->
      <div v-if="state.connecting" class="connecting-overlay">
        <div class="connecting-content">
          <el-icon class="connecting-icon"><Loading /></el-icon>
          <h3>正在连接SPICE服务器...</h3>
          <p>{{ state.host }}:{{ state.port }}</p>
          <div class="debug-info">
            <p>正在加载SPICE HTML5客户端...</p>
            <p>尝试建立WebSocket连接...</p>
          </div>
        </div>
      </div>

      <!-- 未连接状态 -->
      <div v-else-if="!state.connected" class="placeholder-overlay">
        <div class="placeholder-content">
          <el-icon class="placeholder-icon"><Monitor /></el-icon>
          <h3>SPICE远程控制台</h3>
          <p>正在自动连接到远程服务器...</p>
        </div>
      </div>

      <!-- SPICE工具栏 -->
      <div v-if="state.connected" class="spice-toolbar">
        <div class="toolbar-left">
          <el-button size="small" @click="sendCtrlAltDel" type="primary">
            <el-icon><Key /></el-icon>
            Ctrl+Alt+Del
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button size="small" @click="toggleFullscreen">
            <el-icon><FullScreen /></el-icon>
            全屏
          </el-button>
          <el-button size="small" @click="disconnectSpice" type="danger">
            <el-icon><Close /></el-icon>
            断开
          </el-button>
        </div>
      </div>

      <!-- SPICE显示区域 -->
      <div id="spice-area" class="spice-area" ref="spiceAreaRef"></div>
    </div>

    <!-- 状态提示 -->
    <div v-if="state.showStatus" class="status-toast">
      <el-alert
        :title="state.statusMessage"
        :type="state.statusType"
        :closable="true"
        @close="state.showStatus = false"
        show-icon
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, nextTick, ref, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Monitor,
  Loading,
  Key,
  FullScreen,
  Close
} from '@element-plus/icons-vue';

// 扩展Window类型以支持SPICE
declare global {
  interface Window {
    SpiceMainConn: any;
  }
}

// 定义变量内容
const state = reactive({
  host: '',
  port: '',
  connecting: false,
  connected: false,
  status: '未连接',
  connectionTime: '',
  showStatus: false,
  statusMessage: '',
  statusType: 'info' as 'success' | 'warning' | 'info' | 'error',
  spiceClient: null as any,
  connectionTimer: null as any,
});

const spiceAreaRef = ref<HTMLElement>();
const emit = defineEmits(['returnOK']);

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已连接': return 'success';
    case '连接中': return 'warning';
    case '连接失败': return 'danger';
    default: return 'info';
  }
};

// 自动连接SPICE
const autoConnectSpice = async () => {
  try {
    state.connecting = true;
    state.status = '连接中';
    showStatus('正在连接SPICE服务器...', 'info');

    // 检查并加载SPICE HTML5客户端
    await loadSpiceHTML5Client();

    // 建立SPICE连接
    await establishSpiceConnection();

    state.connected = true;
    state.status = '已连接';
    state.connectionTime = new Date().toLocaleTimeString();
    showStatus('SPICE连接成功！', 'success');

    // 启动连接计时器
    startConnectionTimer();

  } catch (error) {
    state.status = '连接失败';
    showStatus('SPICE连接失败: ' + (error instanceof Error ? error.message : String(error)), 'error');
    console.error('SPICE连接错误:', error);
  } finally {
    state.connecting = false;
  }
};

// 加载SPICE HTML5客户端
const loadSpiceHTML5Client = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 如果已经加载，直接返回
    if (window.SpiceMainConn) {
      resolve();
      return;
    }

    // SPICE HTML5客户端脚本列表 - 使用相对路径或固定路径
    const spiceScripts = [
      '/spice-html5/spicearraybuffer.js',
      '/spice-html5/spiceconn.js',
      '/spice-html5/spicemsg.js',
      '/spice-html5/spicetype.js',
      '/spice-html5/spiceutils.js',
      '/spice-html5/spice.js'
    ];

    let loadedCount = 0;
    const totalScripts = spiceScripts.length;

    // 动态加载脚本
    spiceScripts.forEach((scriptSrc) => {
      const script = document.createElement('script');
      script.src = scriptSrc;
      script.async = false; // 保证按顺序加载

      script.onload = () => {
        loadedCount++;
        console.log(`已加载SPICE脚本: ${scriptSrc}`);

        if (loadedCount === totalScripts) {
          // 等待一小段时间确保所有脚本都已初始化
          setTimeout(() => {
            if (window.SpiceMainConn) {
              console.log('SPICE HTML5客户端加载完成');
              resolve();
            } else {
              reject(new Error('SPICE客户端初始化失败'));
            }
          }, 200);
        }
      };

      script.onerror = () => {
        console.error(`加载SPICE脚本失败: ${scriptSrc}`);
        reject(new Error(`加载SPICE脚本失败: ${scriptSrc}`));
      };

      document.head.appendChild(script);
    });
  });
};

// 建立SPICE连接
const establishSpiceConnection = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      const spiceArea = spiceAreaRef.value || document.getElementById('spice-area');
      if (!spiceArea) {
        reject(new Error('SPICE显示区域未找到'));
        return;
      }

      // 清空容器
      spiceArea.innerHTML = '';

      // 构建WebSocket URI - 直接使用传入的端口
      // 尝试多种可能的WebSocket路径
      const possiblePaths = [
        `ws://${state.host}:${state.port}`,           // 直接连接
        `ws://${state.host}:${state.port}/websockify`, // 带websockify路径
        `ws://${state.host}:${state.port}/spice`,      // 带spice路径
      ];

      let currentPathIndex = 0;

      const tryConnection = () => {
        if (currentPathIndex >= possiblePaths.length) {
          reject(new Error('所有连接路径都失败'));
          return;
        }

        const wsUri = possiblePaths[currentPathIndex];
        console.log(`尝试连接SPICE WebSocket (${currentPathIndex + 1}/${possiblePaths.length}):`, wsUri);

        // 创建SPICE连接
        try {
          state.spiceClient = new window.SpiceMainConn({
            uri: wsUri,
            screen_id: spiceArea,
            dump_id: 'debug-div',
            message_id: 'message-div',
            password: '',
            onerror: (err: any) => {
              console.error(`SPICE连接错误 (路径 ${currentPathIndex + 1}):`, err);
              currentPathIndex++;
              setTimeout(tryConnection, 1000); // 延迟1秒后尝试下一个路径
            },
            onsuccess: () => {
              console.log('SPICE连接成功，使用路径:', wsUri);
              resolve();
            },
            ondisconnect: () => {
              console.log('SPICE连接断开');
              handleDisconnect();
            }
          });
        } catch (error) {
          console.error(`创建SPICE连接失败 (路径 ${currentPathIndex + 1}):`, error);
          currentPathIndex++;
          setTimeout(tryConnection, 1000);
        }
      };

      // 开始尝试连接
      tryConnection();

    } catch (error) {
      console.error('建立SPICE连接时出错:', error);
      reject(error);
    }
  });
};

// 断开SPICE连接
const disconnectSpice = () => {
  if (state.spiceClient) {
    try {
      state.spiceClient.stop();
    } catch (error) {
      console.error('断开SPICE连接时出错:', error);
    }
    state.spiceClient = null;
  }

  // 清理状态
  state.connected = false;
  state.status = '未连接';
  state.connectionTime = '';

  // 清空SPICE容器
  const spiceArea = spiceAreaRef.value || document.getElementById('spice-area');
  if (spiceArea) {
    spiceArea.innerHTML = '';
  }

  // 停止计时器
  if (state.connectionTimer) {
    clearInterval(state.connectionTimer);
    state.connectionTimer = null;
  }

  showStatus('SPICE连接已断开', 'warning');
};

// 处理连接断开
const handleDisconnect = () => {
  state.connected = false;
  state.status = '连接断开';
  showStatus('SPICE连接意外断开', 'error');
};

// 发送Ctrl+Alt+Del
const sendCtrlAltDel = () => {
  if (state.spiceClient && state.spiceClient.inputs) {
    try {
      // 发送Ctrl+Alt+Del组合键
      state.spiceClient.inputs.sendKey(0x1d, 1); // Ctrl down
      state.spiceClient.inputs.sendKey(0x38, 1); // Alt down
      state.spiceClient.inputs.sendKey(0x53, 1); // Del down

      setTimeout(() => {
        state.spiceClient.inputs.sendKey(0x53, 0); // Del up
        state.spiceClient.inputs.sendKey(0x38, 0); // Alt up
        state.spiceClient.inputs.sendKey(0x1d, 0); // Ctrl up
      }, 100);

      ElMessage.success('已发送Ctrl+Alt+Del');
    } catch (error) {
      ElMessage.error('发送组合键失败');
    }
  }
};

// 切换全屏
const toggleFullscreen = () => {
  const spiceArea = spiceAreaRef.value || document.getElementById('spice-area');
  if (!spiceArea) return;

  if (!document.fullscreenElement) {
    spiceArea.requestFullscreen().catch(() => {
      ElMessage.error('无法进入全屏模式');
    });
  } else {
    document.exitFullscreen();
  }
};

// 启动连接计时器
const startConnectionTimer = () => {
  const startTime = Date.now();
  state.connectionTimer = setInterval(() => {
    const elapsed = Date.now() - startTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    state.connectionTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, 1000);
};

// 显示状态信息
const showStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  state.statusMessage = message;
  state.statusType = type;
  state.showStatus = true;

  // 自动隐藏成功和信息提示
  if (type === 'success' || type === 'info') {
    setTimeout(() => {
      state.showStatus = false;
    }, 3000);
  }
};

// 解析HTTP URL并提取主机和端口
const parseHttpUrl = (httpUrl: string) => {
  try {
    // 如果是完整的URL格式
    if (httpUrl.startsWith('http://') || httpUrl.startsWith('https://')) {
      const url = new URL(httpUrl);
      return {
        host: url.hostname,
        port: url.port || (url.protocol === 'https:' ? '443' : '80')
      };
    }

    // 如果是 host:port 格式
    const parts = httpUrl.split(':');
    if (parts.length >= 2) {
      return {
        host: parts[0].trim(),
        port: parts[1].trim()
      };
    }

    // 如果只有主机名，使用默认端口
    if (parts.length === 1 && parts[0].trim()) {
      return {
        host: parts[0].trim(),
        port: '5900' // SPICE默认端口
      };
    }

    throw new Error('无效的URL格式');
  } catch (error) {
    console.error('解析URL失败:', error);
    throw new Error('无效的连接地址格式');
  }
};

// 打开弹窗并自动连接
const openDialog = async (httpUrl: string) => {
  nextTick(async () => {
    try {
      console.log('接收到HTTP URL:', httpUrl);

      // 解析HTTP URL
      const { host, port } = parseHttpUrl(httpUrl);

      state.host = host;
      state.port = port;
      state.status = '准备连接';

      console.log(`解析得到 - 主机: ${host}, 端口: ${port}`);

      // 重置状态
      if (state.connected) {
        disconnectSpice();
      }

      // 延迟一下再自动连接，让界面先显示
      setTimeout(() => {
        autoConnectSpice();
      }, 500);

    } catch (error) {
      console.error('解析HTTP URL失败:', error);
      showStatus('无效的连接地址', 'error');
    }
  });
};

// 组件卸载时清理
onUnmounted(() => {
  disconnectSpice();
});

// 暴露变量
defineExpose({
  openDialog,
  disconnectSpice,
});
</script>
<style lang="scss" scoped>
.spice-console {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .connection-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .status-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-icon {
        font-size: 20px;
        color: #409eff;
      }

      .status-text {
        font-weight: 600;
        color: #303133;
      }
    }

    .connection-details {
      display: flex;
      align-items: center;
      gap: 16px;

      .detail-item {
        font-family: 'Courier New', monospace;
        color: #606266;
        font-size: 14px;
        background-color: #f8f9fa;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }

  .spice-container {
    flex: 1;
    position: relative;
    background-color: #000;
    overflow: hidden;

    .connecting-overlay,
    .placeholder-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #2c2c2c;
      z-index: 10;

      .connecting-content,
      .placeholder-content {
        text-align: center;
        color: #fff;

        .connecting-icon,
        .placeholder-icon {
          font-size: 48px;
          color: #409eff;
          margin-bottom: 16px;
          animation: pulse 2s infinite;
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          color: #fff;
        }

        p {
          margin: 0;
          color: #ccc;
          font-family: 'Courier New', monospace;
        }

        .debug-info {
          margin-top: 16px;
          padding: 12px;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 4px;
          border-left: 3px solid #409eff;

          p {
            margin: 4px 0;
            font-size: 12px;
            color: #409eff;
          }
        }
      }
    }

    .spice-toolbar {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
      z-index: 20;
      transition: opacity 0.3s;

      &:hover {
        opacity: 1;
      }

      .toolbar-left,
      .toolbar-right {
        display: flex;
        gap: 8px;
      }
    }

    .spice-area {
      width: 100%;
      height: 100%;
      background-color: #000;
      position: relative;

      canvas {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain;
        display: block;
      }
    }
  }

  .status-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    min-width: 300px;
  }
}

// 动画效果
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 全屏样式
:deep(.spice-area:fullscreen) {
  background-color: #000;

  canvas {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: contain;
  }

  .spice-toolbar {
    opacity: 0;

    &:hover {
      opacity: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .spice-console {
    .connection-status-bar {
      flex-direction: column;
      gap: 8px;
      padding: 8px 12px;

      .connection-details {
        gap: 8px;
      }
    }

    .spice-container .spice-toolbar {
      .toolbar-left,
      .toolbar-right {
        gap: 4px;
      }
    }
  }
}
</style>
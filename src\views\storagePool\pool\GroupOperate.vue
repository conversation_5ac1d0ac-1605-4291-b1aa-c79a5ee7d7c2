<template>
	<el-dialog v-model="formItem.isShow" :title="formItem.title" width="600">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="分组名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入分组名称"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { secureGroupNew,secureGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  title: '',
	name: '',
  id: '',
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
});
const emit = defineEmits(['groupOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        formItem.isShow = false;
        emit('groupOK', 'refresh');
        // if(formItem.title == '新建安全组') {
        //   secureGroupNew({
        //     name: formItem.name,
        //   }).then((res) => {
        //     if(res.msg == 'ok') {
        //       formItem.isShow = false;
        //       ElMessage.success(formItem.title+'操作完成');
        //       emit('groupOK', 'refresh');
        //     }else {
        //       ElMessage.error(res.msg);
        //     }
        //   });
        // }else {
        //   secureGroupEdit({
        //     name: formItem.name,
        //   }).then((res) => {
        //     if(res.msg == 'ok') {
        //       formItem.isShow = false;
        //       ElMessage.success(formItem.title+'操作完成');
        //       emit('groupOK', 'refresh');
        //     }else {
        //       ElMessage.error(res.msg);
        //     }
        //   });
        // }
			}
		});
	}
};

// 打开弹窗
const openDialog = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
    if (row.type=='new') {
      if (ruleFormRef.value) {
        ruleFormRef.value.resetFields();
      }
      formItem.title = '新建分组'
    }else {
      formItem.title = '编辑分组'
      formItem.name = row.name
      formItem.id = row.id
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
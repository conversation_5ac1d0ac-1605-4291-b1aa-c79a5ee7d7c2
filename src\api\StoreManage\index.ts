import request from '/@/utils/request';
// 接入IPsan
export const ipAccess = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/docking',
		method: 'post',
		data,
	});
};
// IPsan 表格增、查、改、删
export const ipTableCreate = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/create',
		method: 'post',
		data,
	});
};
export const ipTableQuery = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/list',
		method: 'post',
		data,
	});
};
export const ipTableEdit = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/edit',
		method: 'post',
		data,
	});
};
export const ipTableDelete = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/deleted',
		method: 'delete',
		data,
	});
};
// 分布式交换机
export const ipTableSwitch = () => {
	return request({
		url: '/theapi/*****',
		method: 'get',
	});
};
// IPsan 扫描
export const ipTableScan = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/scan',
		method: 'post',
		data,
	});
};
// IPsan IQN Target表查询
export const ipTableTarget = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/target/list',
		method: 'post',
		data,
	});
};
// IPsan IQN Target表登录
export const ipTargetLogin = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/target/login',
		method: 'post',
		data,
	});
};
// IPsan IQN Target表删除
export const ipTargetDeleted = (data: object) => {
	return request({
		url: '/theapi/v5/ipsan/target/deleted',
		method: 'delete',
		data,
	});
};
// FCsan
export const fcTableQuery = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'post',
		data,
	});
};
export const fcTableDelete = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'delete',
		data,
	});
};
// NAS
export const nasTableQuery = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'post',
		data,
	});
};
export const nasTableDelete = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'delete',
		data,
	});
};
// NVME
export const nvmeTableQuery = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'post',
		data,
	});
};
export const nvmeTableDelete = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'delete',
		data,
	});
};
// 分布式存储
export const distributedTableQuery = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'post',
		data,
	});
};
export const distributedTableDelete = (data: object) => {
	return request({
		url: '/theapi/v5/***',
		method: 'delete',
		data,
	});
};

// 存储 查询
export const storeQuery = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/list',
		method: 'post',
		data,
	});
};
// 存储 对接
export const storeDocking = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/add',
		method: 'post',
		data,
	});
};
// 存储资源 表查询
export const storageResourcesQuery = (data: object) => {
	return request({
		url: '********',
		method: 'post',
		data,
	});
};
// 存储 删除
export const storeDelete = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/delete',
		method: 'delete',
		data,
	});
};

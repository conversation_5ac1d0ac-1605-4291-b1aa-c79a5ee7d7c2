<template>
	<el-dialog v-model="formItem.isShow" title="执行命令" width="600">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title"
				><span style="color: green">{{ formItem.name }}</span> 执行命令</span
			>
		</template>
		<el-form :model="formItem" label-width="auto" class="demo-ruleForm" status-icon>
			<el-form-item label="命令">
				<div class="output-input">
					<el-input v-model="formItem.command" placeholder="请输入要执行的命令" @keyup.enter="confirm" />
					<el-button type="primary" style="margin-left: 10px" @click="confirm">发送</el-button>
				</div>
			</el-form-item>
			<h3>输出</h3>
			<div class="output-area">
				<p v-for="(item, index) in formItem.outputData" :key="index">{{ item }}</p>
			</div>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="ColonyEdit">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { containerInstruction } from '/@/api/ResourcePool/container'; // 接口
const formItem = reactive({
	isShow: false,
	id: '',
	name: '',
	command: '',
	outputData: [],
});
const confirm = () => {
  if(formItem.command! == '') {
    containerInstruction({
      id: formItem.id,
      name: formItem.name,
      command: formItem.command,
    }).then((res) => {
      if (res.data.msg == 'ok') {
        formItem.outputData = res.data.output.split('\n').filter((item: string) => item !== '');
        formItem.command = ''
      } else {
        ElMessage.error(res.data.msg);
      }
    });
  }
};
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		formItem.isShow = true;
		formItem.name = row.name;
		formItem.id = row.id;
		formItem.command = '';
		formItem.outputData = [];
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.output-input {
	width: 100%;
	display: flex;
}
h3 {
	text-align: center;
	padding: 5px 0;
}
.output-area {
	border: 1px solid #7c7b7b;
	color: #d7d7d7;
	width: 100%;
	height: 300px;
	overflow: auto;
	padding: 0 10px;
	background: #3a3a3a;
	border-radius: 5px;
}
</style>
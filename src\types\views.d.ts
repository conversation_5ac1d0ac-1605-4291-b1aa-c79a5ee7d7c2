/**
 * views personal
 */

declare type MyTableColumns = {
	label?: string; // 对应 el-table-column 的 label 表头
	prop?: string; // 对应 el-table-column 的 prop 字段
	type?: string; // 对应 el-table-column 的 type 表示类型， 可选 selection/index/expand
	width?: string | number; // 对应 el-table-column 的 width 列宽
	minWidth?: string | number; // 对应 el-table-column 的 min-width 最小列宽
	align?: string; // 对应 el-table-column 的 align 对齐方式 left/center/right
	fixed?: string; // 对应 el-table-column 的 fixed
	sortable?: boolean; // 对应 el-table-column 的 sortable
	filters?: EmptyArrayType; // 对应 el-table-column 的 filters
	expand?: string; // 如果type是expand时，可以通过此属性配置一个插槽名称，并且是作用域插槽，可以接收 scope 数据
	tdSlot?: string; // 单元格要自定义内容时，可以通过此属性配置一个插槽名称，并且是作用域插槽，可以接收 scope 数据
	labelSlot?: string; // 表头要自定义内容时，可以通过此属性配置一个插槽名称，并且是作用域插槽，可以接收 scope 数据
	wrap?: boolean; // 超出显示tooip
};

// 搜索组件
declare type TableSearchType = {
	label: string; // 标题
	prop: string; // 属性名称
	placeholder: string; // 提示内容
	required: boolean; // 是否验证
	type: string; // 表单类型
	options?: SelectOptionType[]; // 下拉框值
	isShow: boolean; // 是否显示
	isMultiple?: boolean; // 是否多选
	clearable?: boolean; // 是否显示清除按钮
};
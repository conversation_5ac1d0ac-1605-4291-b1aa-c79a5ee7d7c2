<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="挂载盘大小" prop="size">
			<el-input v-model="formItem.size" type="number" placeholder="请输入挂载盘大小(GB)" />
		</el-form-item>
    <el-form-item label="挂载目录">
			<el-input v-model="formItem.destination" placeholder="请输入指定容器上卷的挂在点目录" />
		</el-form-item>
    <el-form-item>
			<el-button type="info" ghost @click="addClick" :disabled="state.disabled">添加卷</el-button>
		</el-form-item>
    <div class="table-area">
      <my-table
        ref="tableRef"
        :pagination="state.pagination"
        :columns="state.columns"
        :request="getTableData"
      >
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button type="primary" @click="deletClick(row)">删除</el-button>
        </template>
      </my-table>
    </div>
	</el-form>
</template>
<script lang="ts" setup name="AddBasic">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { propCPU, propMem } from '/@/model/resource.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const props = defineProps({
  listTime: {
    type: String,
    required: true
  }
});
const formRef = ref<FormInstance>()
// 基本信息
const state = reactive({
  columns: [
    { label: '挂载盘大小', prop: 'size' },
	  { label: '挂载目录', prop: 'destination' },
	  { label: '操作', tdSlot: 'operation', width: '90px' },
  ], // 表格表头配置
  datas: [{}],
	pagination: {
		show: false,
	}, // 是否显示分页
  disabled: true,
})
state.datas = []
const formItem = reactive({
  size: '',
  destination: '',
});
const rulesForm = reactive<FormRules>({
  cpu: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propCPU, trigger: "change" },
  ],
  memory: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propMem, trigger: "change" },
  ],
})
const addClick = () => {
  if(formItem.size!==''&&formItem.destination!=='') {
    state.datas.push({
      size: formItem.size,
      destination: formItem.destination,
      source: '',
    })
    formItem.size = '';
    formItem.destination = '';
    // refresh();
  }
}
const deletClick = (row: any) => {
  state.datas.splice(row._index,1)
}
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return {
		data: state.datas, // 数据
		total: state.datas.length, // 总数
	};
}
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};

const emit = defineEmits(['listOK']);
watch(
  ()=> props.listTime,
  (val)=>{
    emit('listOK', {
      mounts: state.datas
    });
  }
);
watch(
  ()=> formItem.size,
  (val)=>{
    if(val=='' || formItem.destination=='') {
      state.disabled = true
    }else {
      state.disabled = false
    }
  }
);
watch(
  ()=> formItem.destination,
  (val)=>{
    if(val=='' || formItem.size=='') {
      state.disabled = true
    }else {
      state.disabled = false
    }
  }
);
</script>
<style lang="scss" scoped>
  .table-area {
    width: calc(100%);
		height: calc(260px);
		position: relative;
  }
</style>
<template>
	<el-dialog v-model="formItem.isShow" title="新建磁盘" width="500" :close-on-click-modal="false" :show-close='false'>
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="磁盘名称" prop="name">
				<el-input v-model="formItem.name" :disabled="formItem.disabled" placeholder="请输入磁盘名称" />
			</el-form-item>
			<el-form-item label="创建方式" prop="way">
				<el-radio-group v-model="formItem.way">
					<el-radio value="new" :disabled="formItem.disabled">普通创建</el-radio>
					<el-radio value="disk" :disabled="formItem.disabled">上传本地磁盘</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="formItem.way == 'new'" label="存储格式" prop="format">
				<el-select v-model="formItem.format" style="width: 100%">
					<el-option v-for="item in formItem.formatData" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item v-if="formItem.way == 'new'" label="磁盘容量" prop="disk">
				<el-input v-model="formItem.disk" type="number">
					<template #append>
						<el-select v-model="formItem.unit" style="width: 80px">
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item v-if="formItem.way == 'new'" label="置备类型" prop="type">
				<el-select v-model="formItem.type" style="width: 100%">
					<el-option label="精简置备" :value="1" />
					<el-option label="厚置备延迟置零" :value="2" />
					<el-option label="厚置备置零" :value="3" />
				</el-select>
			</el-form-item>
			<el-form-item label="备注">
				<el-input v-model="formItem.remark" :disabled="formItem.disabled" :rows="2" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息" />
			</el-form-item>
			<el-form-item v-if="formItem.way == 'disk'" label="上传磁盘" prop="file">
				<input type="file" @change="handleFileChange" :disabled="formItem.disabled"  />
			</el-form-item>
      <el-progress v-if="formItem.progressAll!==0" :percentage="parseFloat((formItem.progressUsed/formItem.progressAll*100).toFixed(1))" :status="formItem.status" />
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm" :disabled="formItem.disabled">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="DiskNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, UploadInstance, UploadProps, FormInstance, FormRules, UploadRawFile } from 'element-plus';
import { ElMessage, genFileId } from 'element-plus';
import { diskFormat, diskNew, diskSlice, diskUpload } from '/@/api/ResourcePool/storage.ts'; // 接口
import { propName, propNumber } from '/@/model/resource.ts'; // 表列、正则
import pLimit from 'p-limit';
import { fi } from 'element-plus/es/locale';

const props = defineProps({
	tableRow: {
		type: Object,
		required: true,
	},
	newTime: {
		type: String,
		required: true,
	},
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	name: '',
	way: 'new',
	format: '',
	formatData: [],
	disk: '1',
	unit: 'MB',
	type: 1,
	remark: '',
	file: '',
	fileSize: 0,
	progressAll: 0,
	progressUsed: 0,
	status: '',
	disabled: false
});
const propFile = (rule: any, value: any, callback: any) => {
	if (value == '') {
		callback(new Error('请选择文件'));
		// } else if (sameType() != formItem.imgType) {
		// 	callback(new Error('文件类型与镜像类型不一致'));
		// } else {
	} else {
		callback();
	}
};
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'change' },
	],
	way: [{ required: true, message: '必选项', trigger: 'blur' }],
	format: [{ required: true, message: '必选项', trigger: 'blur' }],
	disk: [
		{ required: true, message: '必选项', trigger: 'blur' },
		{ validator: propNumber, trigger: 'change' },
	],
	type: [{ required: true, message: '必选项', trigger: 'blur' }],
	file: [
		{ required: true, message: '必选项' },
		{ validator: propFile, trigger: 'change' },
	],
});
// 查询存储格式
const formatQuery = () => {
	diskFormat().then((res: any) => {
		formItem.formatData = res;
		formItem.format = res[0].id;
	});
};
const handleFileChange = (event: any) => {
	formItem.file = event.target.files[0];
};
const uploadFile = async (file: any) => {
	const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
	let chunks = Math.ceil(file.size / chunkSize);
	formItem.progressAll = chunks;
	formItem.fileSize = file.size;
	
	const limit = pLimit(5); // 设置最大并发数为5

	// 创建分片数组
	const chunkPromises = [];
	for (let i = 0; i < chunks; i++) {
		let start = i * chunkSize;
		let end = Math.min(file.size, start + chunkSize);
		let chunk = file.slice(start, end);
		chunkPromises.push({ chunk, index: i });
	}

	try {
		// 创建一个队列用于并发上传
    for (const { chunk, index } of chunkPromises) {
      // 使用 limit 保证并发控制
      await limit(() => uploadChunk(chunk, index, chunks, file.name));
    }
		// 并发上传分片
		// await Promise.all(chunkPromises.map(({ chunk, index }) => limit(() => uploadChunk(chunk, index, chunks, file.name))));
		// 通知完成
		await notifyComplete(chunks, file.name);
	} catch (error) {
		ElMessage.error('上传本地磁盘失败');
	}
};

const uploadChunk = async (chunk: string, index: number, total: number, filename: string) => {
	let formData = new FormData();
	formData.append('chunk', chunk);
	formData.append('index', index.toString());
	formData.append('total', total.toString());
	formData.append('filename', filename);
	formData.append('storage_pool_id', props.tableRow.id);
	try {
		await diskSlice(formData);
	  formItem.progressUsed++
	} catch (error) {
		formItem.status = 'error'
		formItem.disabled = false;
		throw error;
	}
};
const notifyComplete = async (total: number, filename: string) => {
	try {
		await diskUpload({name: formItem.name,total:total, filename:filename,storage_pool_id: props.tableRow.id,remark: formItem.remark,size:formItem.fileSize});
		formItem.status = 'success'
		setTimeout(() => {
			ElMessage.success('新建磁盘-上传本地磁盘操作完成');
			formItem.isShow = false;
			emit('returnOK', 'refresh');
		}, 1000);
	} catch (error) {
		// console.error('通知完成失败：', error);
		formItem.status = 'exception'
	}
};
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			let size = 0
			if (formItem.unit == 'MB') {
			  size = parseInt(formItem.disk)*1024*1024
			}else if(formItem.unit == 'GB') {
			  size = parseInt(formItem.disk)*1024*1024*1024
			}else if(formItem.unit == 'TB') {
			  size = parseInt(formItem.disk)*1024*1024*1024*1024
			}
			if (val) {
				if (formItem.way == 'new') {
					formItem.isShow = false;
					diskNew({
						// host: '***********',
						storage_device_id: props.tableRow.storage_device_id,
						storage_pool_id: props.tableRow.id,
						name: formItem.name,
						path: '', // 默认是存储池路径
						encrypt: 0, // 加密
						volume_type:  formItem.format, // 存储卷类型
						join_type: 3, // 加入类型
						capacity: size,
						preallocation: formItem.type, // 置备类型
						remark: formItem.remark,
					})
					.then((res:any) => {
						emit('returnOK', 'refresh');
					})
				}else {
					if (formItem.file) {
						formItem.disabled = true;
						uploadFile(formItem.file);
					}
				}
			}
		});
	}
};
watch(
	() => props.newTime,
	(val) => {
		formItem.isShow = true;
		formItem.disabled = false;
		formItem.unit = 'MB';
		formItem.remark = '';
    formItem.progressAll = 0;
    formItem.progressUsed = 0;
		formItem.status = '';
		formatQuery();
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
</script>
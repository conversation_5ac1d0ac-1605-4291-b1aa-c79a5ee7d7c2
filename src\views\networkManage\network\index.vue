<template>
	<div class="storage-area layout-padding">
    <el-card>
      <div class="storag-btn-area">
        <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh">刷新</el-button>
            <el-button type="primary" plain @click="newClick">新建网络</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
          </div>
          <div>
            <el-input v-show="false" v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :searchParams="state.searchParams"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
            <!-- 展开行内容 -->
            <template #expandContent="{ row }">
              <div>
                <p>ID: {{ row.id }}</p>
              </div>
            </template>
            <!-- 子网 -->
            <template #cidr="{ row }">
              <span>{{ row.cidr.toString() }}</span>
						</template>
            <!-- VLAN ID -->
						<template #vlanid="{ row }">
              <span>{{ row.vlanid?row.vlanid:'-' }}</span>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-dropdown @command="commandItem($event,row)">
                <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="bjwl">编辑网络</el-dropdown-item>
                    <el-dropdown-item command="tjzw">添加子网</el-dropdown-item>
                    <el-dropdown-item command="bjzw">编辑子网</el-dropdown-item>
                    <el-dropdown-item command="sczw">删除子网</el-dropdown-item>
                    <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
    <TableNew :newTime="state.newTime" @returnOK="returnOK"></TableNew>
    <TableEdit :editTime="state.editTime" :tableRow="state.tableRow" @returnOK="returnOK"></TableEdit>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
    <SubnetNew :newTime="state.newSubnetTime" @returnOK="returnOK"></SubnetNew>
    <SubnetEdit :editTime="state.editSubnetTime" :tableRow="state.tableRow" @returnOK="returnOK"></SubnetEdit>
    <SubnetDelete :editTime="state.deleteSubnetTime" :tableRow="state.tableRow" @returnOK="returnOK"></SubnetDelete>
  </div>
</template>
<script setup lang="ts" name="ClusterAlarm">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { tableColumns } from '/@/model/network.ts'; // 表列、正则
import { netQuery,netDelete } from '/@/api/Network'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'))
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));
const SubnetNew = defineAsyncComponent(() => import('./SubnetNew.vue'))
const SubnetEdit = defineAsyncComponent(() => import('./SubnetEdit.vue'))
const SubnetDelete = defineAsyncComponent(() => import('./SubnetDelete.vue'))

import { useRouter } from 'vue-router';
import { tr } from 'element-plus/es/locale';
const router = useRouter();
// 定义变量内容
const state = reactive({
  columns: tableColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  tableRow: {},
  newTime: '',
  editTime: '',
  deleteTime: '',
  newSubnetTime: '',
  editSubnetTime: '',
  deleteSubnetTime: '',

});

interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(true){
    let libs = ['测试','临时','NEW','其它']
    const list = new Array(page.pageSize).fill({}).map((item, index) => {
			item = {
        name: '网络: '+libs[Math.round(Math.random() * 3)],
        cidr: ['192.168.1.1','192.168.10.10'],
        status: index==0?'list':'active',
        network_type: 'vlan',
        vlanid:  index==0?null:Math.round(Math.random() * 1000),
				id:  'lists'+index,
			};
			return item;
		});

		return {
			data: list, // 数据
			total: 100, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    netQuery().then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 表操作列
const commandItem = (item: string,row:any)=>{
  state.tableRow = row
  switch (item) {
    case 'bjwl':
      state.editTime = ''+new Date()
      break;
    case 'tjzw':
      state.newSubnetTime = ''+new Date()
      break;
    case 'bjzw':
      state.editSubnetTime = ''+new Date()
      break;
    case 'sczw':
      state.deleteSubnetTime = ''+new Date()
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 新建
const newClick = () => {
  state.newTime = ''+new Date()
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '网络/'+new Date()    
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    netDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除镜像操作完成');
      }else {
        ElMessage.error('删除镜像操作失败');
      }
    })
  }else {
    refresh()
  }
}

onMounted(() => {
})
</script>
<style scoped lang="scss">
.storage-area {
  padding-top: 0 !important;
	width: calc(100%);
	height: calc(100%);
  .storag-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
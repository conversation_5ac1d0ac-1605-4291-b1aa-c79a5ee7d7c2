<template>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    title="选择存储池"
  >
    <el-input v-model="state.tableSearch" placeholder="请输入搜索内容">
      <template #append>
        <el-button :icon="Search" @click="refresh"></el-button>
      </template>
    </el-input>
    <div class="storage-pool-dialog">
      <my-table
        ref="tableRef"
        :pagination="state.pagination"
        :columns="state.columns"
        :request="getTableData"
      >
        <!-- 已用 -->
        <template #radio="{ row }">
          <el-radio-group v-model="state.tableID"  @change="radioClick(row)">
            <el-radio :value="row.id"></el-radio>
          </el-radio-group>
        </template>
        <!-- 已用 -->
        <template #allocation="{ row }">
          <span>{{ byteUnitConversion(row.allocation) }}</span>
        </template>
        <!-- 总量 -->
        <template #capacity="{ row }">
          <span>{{ byteUnitConversion(row.capacity) }}</span>
        </template>
      </my-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="SelectStoragePool">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { storagePollQuery } from '/@/api/ResourcePool/storage.ts'; // 接口
import { storageVMColumns,byteUnitConversion } from '/@/model/storage.ts';
import { Search } from '@element-plus/icons-vue'
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  storageTime: {
    type: String,
    required: true
  }
});
const state = reactive({
  isShow: false,
  columns: storageVMColumns,
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableID: '',
  tableName: '',
  tablePath: '',
});
const getTableData = ( params: EmptyObjectType,page: EmptyObjectType) => {
  return new Promise(async(resolve)=>{
    await storagePollQuery({
      _id: props.treeItem.id,
      type: 'host',
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页显示条数
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch // 收索条件
    }).then((res:any)=>{
      state.tableID = res.total*1>0?res.data[0].id:''
      state.tableName = res.total*1>0?res.data[0].name:''
      state.tablePath = res.total*1>0?res.data[0].storage_local_dir:''
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {})
  })
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 单选磁盘
const radioClick=(row:any)=>{
  state.tableID = row.id
  state.tableName = row.name
  state.tablePath = row.storage_local_dir
}
const emit = defineEmits(['storageReturn']);
const confirm =()=>{
  state.isShow= false
  emit('storageReturn', {tablePath:state.tablePath,tableName:state.tableName});
}

watch(
  ()=> props.storageTime,
  (val)=>{
    state.isShow = true;
  }
);
</script>
<style lang="scss" scoped>
  .storage-pool-dialog {
    height: 500px;
    position: relative;
  }
</style>
<template>
	<el-dialog v-model="formItem.isShow" append-to-body class="dialog-1000">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title">{{ formItem.title }} 存储设备详情</span>
		</template>
		<div class="dialog-area">
			<div class="tabs-btn-area">
				<div>
					<el-button type="primary" plain @click="refresh">扫描</el-button>
					<el-button type="primary" plain @click="newClick">添加存储目录</el-button>
				</div>
				<div>
					<el-input v-model="formItem.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
						<template #append>
							<el-button :icon="Search" @click="refresh"></el-button>
						</template>
					</el-input>
				</div>
			</div>
			<div class="table-area">
				<my-table
					ref="tableRef"
					:pagination="formItem.pagination"
					:columns="formItem.columns"
					:request="getTableData"
				>
          <template #operation="{ row }">
            <el-button type="danger" plain @click="deleteClick([row])">删除</el-button>
					</template>
				</my-table>
			</div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
			</div>
		</template>
    <TableNew ref="newRef" @returnOK="returnOK"></TableNew>
	  <TableDelete :names='formDelet.tableNames' :deleteTime='formItem.deleteTime' @returnOK="returnOK"></TableDelete>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { storageResourcesQuery } from '/@/api/StoreManage/index.ts'; // 接口
import { nasDetailsColumns } from '/@/model/storeManage.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	title: '',
	isShow: false,
	columns: nasDetailsColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
// 打开弹窗
const openDialog = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
		formItem.title = row.name;
	});
};
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 获取表数据
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	formItem.tableSelect = [];
	if (true) {
		return {
			data: [{ name: '测试1' }], // 数据
			total: 1, // 总数
		};
	}
	return new Promise(async (resolve) => {
		storageResourcesQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: formItem.tableSearch, // 搜索条件
		})
			.then((res: any) => {
				resolve({
					data: res.data, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {
				resolve({
					data: [], // 数据
					total: 0, // 总数
				});
			});
	});
};
// 新建
const newRef = ref();
const newClick = () => {
  newRef.value.openDialog()
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    formItem.deleteTime = '存储设备/'+new Date()
  }
}
// 表格选中变化
const selectChange = (row: any) => {
	formItem.tableSelect = row;
};
const emit = defineEmits(['returnOK']);
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    // diskDelete({
    //   names: formDelet.tableNames,
    //   ids: formDelet.tableIDs,
    // })
    // .then((res:any) => {
    //   if(res.code == 200){
    //     refresh()
    //     ElMessage.success(res.msg);
    //   }else {
    //     ElMessage.error(res.msg);
    //   }
    // })
  }else {
    refresh()
  }
}
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.dialog-area {
	height: 650px;
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.table-area {
		position: relative;
		height: calc(100% - 50px);
		width: 100%;
	}
}
</style>
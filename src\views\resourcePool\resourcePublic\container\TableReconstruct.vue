<template>
  <el-dialog
    v-model="formItem.isShow"
    title="重建容器"
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="镜像" prop="image">
        <el-input v-model="formItem.image" placeholder="请输入镜像"/>
      </el-form-item>
      <!-- <el-form-item label="Image Driver">
        <el-select v-model="formItem.driver" style="width: 100%">
          <el-option label="Docker Hub" value="docker" />
          <el-option label="Glance" value="glance" disabled />
        </el-select>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="ColonyEdit">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { containerOperate } from '/@/api/ResourcePool/container'; // 接口
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name: '',
  id: '',
  image: '',
  driver: 'docker',
});

const rules = reactive<FormRules>({
  image: [{ required: true, message: "必填项", trigger: "change" }],
  image_driver: [{ required: true, message: "必填项", trigger: "change" }],
})

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        containerOperate({
          id: formItem.id,
          name: formItem.name,
          action: 'rebuild',
          image: formItem.image,
          image_driver: formItem.driver,
        })
        .then(res => {
          ElMessage.success('重建容器操作已完成')
          emit('returnOK', 'refresh');
        })
        .catch(err => {
          ElMessage.error('重建容器操作失败')
        })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		formItem.isShow = true;
    formItem.id = row.id
    formItem.name = row.name
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
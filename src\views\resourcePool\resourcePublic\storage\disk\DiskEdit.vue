<template>
  <el-dialog
    v-model="formItem.isShow"
    title="编辑磁盘"
    append-to-body
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="磁盘名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入磁盘名称"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="DiskEdit">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { diskEdit } from '/@/api/ResourcePool/storage.ts'; // 接口
import { propName } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  tableRow: {
    type: Object,
    required: true
  },
  editTime: {
    type: String,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name:'',
});
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "change" },
  ],
})
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        diskEdit({
          name: props.tableRow.name,
          new_name: formItem.name,
          id: props.tableRow.id
          // storage_pool_id: props.tableRow.storage_pool_id
        })
        .then((res:any) => {
          if(res.msg == 'ok') {
            ElMessage.success('编辑磁盘操作完成');
            emit('returnOK', 'refresh');
          }else {
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}
watch(
  ()=> props.editTime,
  (val)=>{
    formItem.isShow = true;
    formItem.name = props.tableRow.name
  }
);
</script>
<template>
	<el-dialog class="dialog-900" v-model="state.isShow" append-to-body title="创建分布式交换机">
		<div class="paging-dialog-area">
			<div class="paging-step-area">
				<el-steps :active="state.current" align-center>
					<el-step title="基本信息" :icon="Tickets" />
					<el-step title="网络配置" :icon="Setting" />
					<el-step title="默认端口组" :icon="DocumentCopy" />
					<el-step title="汇总信息" :icon="DocumentCopy" />
				</el-steps>
				<!-- <el-steps :active="state.current" simple>
          <el-step title="基本信息" :icon="Tickets" />
          <el-step title="硬件信息" :icon="Setting" />
          <el-step title="汇总信息" :icon="DocumentCopy" />
        </el-steps> -->
			</div>
      <div class="paging-basic-content">
        <!-- 基本信息 -->
        <div class="paging-new-common" v-show="state.current == 0">
          <AddBasic v-if="state.isShow" :times="state.besicTime" @basicOK="basicOK"></AddBasic>
        </div>
        <!-- 网络配置 -->
        <div class="paging-new-common" v-show="state.current == 1">
          <AddNetwork v-if="state.isShow" :besicData="state.besicData" :times="state.netTime" @netOK="netOK"></AddNetwork>
        </div>
        <!-- 默认端口组 -->
        <div class="paging-new-common" v-show="state.current == 2">
          <AddPort v-if="state.isShow" :times="state.portTime" @portOK="portOK"></AddPort>
        </div>
        <!-- 信息汇总 -->
        <div class="paging-new-common summary-area" v-show="state.current == 3">
          <h2>基本信息</h2>
          <div>
            <ul>
              <!-- <li><span>虚拟机名称</span><span>{{state.besicdata.name}}</span></li> -->
            </ul>
          </div>
          <h2>网络配置</h2>
          <div>
            <ul>
              <!-- <li><span>虚拟机名称</span><span>{{state.besicdata.name}}</span></li> -->
            </ul>
          </div>
          <h2>默认端口</h2>
          <div>
            <ul>
              <!-- <li><span>虚拟机名称</span><span>{{state.besicdata.name}}</span></li> -->
            </ul>
          </div>
        </div>
      </div>
		</div>
		<template #footer>
			<el-button v-if="state.current > 0" type="primary" @click="state.current--">上一步</el-button>
			<el-button @click="state.isShow = false">取消</el-button>
			<el-button v-if="state.current < 3" type="primary" @click="nextStep">下一步</el-button>
			<el-button v-if="state.current == 3" type="primary" @click="confirm" :loading="state.loading">确认</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { Tickets, Setting, DocumentCopy,Search,Delete } from '@element-plus/icons-vue'
import { vmNew } from '/@/api/ResourcePool/vm'; // 接口
const AddBasic = defineAsyncComponent(() => import('./AddBasic.vue'));
const AddNetwork = defineAsyncComponent(() => import('./AddNetwork.vue'));
const AddPort = defineAsyncComponent(() => import('./AddPort.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  newTime: {
    type: String,
    required: true
  }
});
// 定义变量内容
const state = reactive({
  isShow: false,
  loading: false,
  current: 0,
  besicTime: '',
  besicData: {},
  netTime: '',
  netData: {},
  portTime: '',
  portData: {},
});
// 基本信息数据
const basicOK = (data: any) => {
  state.besicData = data
  state.current++
}
// 网络配置
const netOK = (data: any) => {
  state.netData = data
  state.current++
}
// 默认端口组
const portOK = (data: any) => {
  state.portData = data
  state.current++
}
// 下一步
const nextStep = () => {
  if (state.current == 0) {
    state.besicTime = ""+new Date()
  }else if (state.current == 1) {
    state.netTime = ""+new Date()
  }else if (state.current == 2) {
    state.portTime = ""+new Date()
  }
}

// 确认
const confirm =()=>{

}
const emit = defineEmits(['returnOK']);
watch(
  ()=> props.newTime,
  (val)=>{
    state.isShow = true;
    state.loading = false;
    state.current = 0
  }
);
</script>
<style lang="scss" scoped>
  .paging-dialog-area {
    height: 600px;
    .paging-step-area {
      padding: 0 15%;
      height: 70px;
    }
    .paging-basic-content {
      width: 100%;
      height: calc(100% - 70px);
      overflow: auto;
      .paging-new-common {
        width: 100%;
        height: 100%;
        padding: 10px 30px;
      }
      .summary-area {
        padding-left: 30%;
        h2 {
          font-weight: 600;
          padding: 10px 0;
        }
        ul {
          padding: 10px; 
          margin: 0;
          list-style: none;
          li {
            padding: 5px 0; 
            margin: 0;
            list-style: none;
            h3 {
              display: inline-block;
              font-weight: 600;
              width: 200px;
            }
            span:first-child {
              display: inline-block;
              width: 200px;
            }
            .summary_notes {
              display: inline-block;
              width: 400px;
            }
          }
        }
      }
    }
  }

</style>
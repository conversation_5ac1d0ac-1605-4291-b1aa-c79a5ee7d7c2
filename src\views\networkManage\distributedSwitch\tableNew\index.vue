<template>
	<el-dialog class="dialog-900" v-model="state.isShow" append-to-body title="创建分布式交换机">
		<div class="paging-dialog-area">
			<div class="paging-step-area">
				<el-steps :active="state.current" align-center>
					<el-step title="基本信息" :icon="Tickets" />
					<el-step title="网络配置" :icon="Setting" />
					<el-step title="默认端口组" :icon="MessageBox" />
					<el-step title="汇总信息" :icon="DocumentCopy" />
				</el-steps>
				<!-- <el-steps :active="state.current" simple>
          <el-step title="基本信息" :icon="Tickets" />
					<el-step title="网络配置" :icon="Setting" />
					<el-step title="默认端口组" :icon="MessageBox" />
					<el-step title="汇总信息" :icon="DocumentCopy" />
        </el-steps> -->
			</div>
      <div class="paging-basic-content">
        <!-- 基本信息 -->
        <div class="paging-new-common" v-show="state.current == 0">
          <AddBasic v-if="state.isShow" :times="state.besicTime" @basicOK="basicOK"></AddBasic>
        </div>
        <!-- 网络配置 -->
        <div class="paging-new-common" v-show="state.current == 1">
          <AddNetwork v-if="state.isShow" :besicData="state.besicData" :times="state.netTime" @netOK="netOK"></AddNetwork>
        </div>
        <!-- 默认端口组 -->
        <div class="paging-new-common" v-show="state.current == 2">
          <AddPort v-if="state.isShow" :times="state.portTime" @portOK="portOK"></AddPort>
        </div>
        <!-- 信息汇总 -->
        <div class="paging-new-common summary-area" v-show="state.current == 3">
          <h2>基本信息</h2>
          <div>
            <ul>
              <li><span>分布式交换机名称</span><span>{{ state.besicData.name || '-' }}</span></li>
              <li><span>选择的物理网卡</span><span>{{ state.besicData.card?.length || 0 }} 个</span></li>
              <li v-if="state.besicData.treeTable?.length > 0">
                <h3>物理机配置</h3>
                <div class="host-config-list">
                  <div v-for="(host, index) in state.besicData.treeTable" :key="index" class="host-item">
                    <div class="host-name">{{ host.hostName }}</div>
                    <div class="host-nics">
                      网卡: {{ host.netName?.map(nic => nic.name).join(', ') || '-' }}
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <h2>网络配置</h2>
          <div>
            <ul>
              <li><span>网络类型</span><span>{{ getNetworkTypeText(state.netData.type) }}</span></li>
              <li v-if="state.netData.tableData?.length > 0">
                <h3>网络配置详情</h3>
                <div class="network-config-list">
                  <div v-for="(config, index) in state.netData.tableData" :key="index" class="config-item">
                    <div class="config-row">
                      <span class="config-label">物理机:</span>
                      <span>{{ config.hostName }}</span>
                    </div>
                    <div class="config-row">
                      <span class="config-label">网卡:</span>
                      <span>{{ config.netName?.map(nic => nic.name).join(', ') || '-' }}</span>
                    </div>
                    <div class="config-row">
                      <span class="config-label">链路聚合模式:</span>
                      <span>{{ getLinkModeText(config.link) }}</span>
                    </div>
                    <div v-if="config.ip" class="config-row">
                      <span class="config-label">IP地址:</span>
                      <span>{{ config.ip }}</span>
                    </div>
                    <div v-if="config.mask" class="config-row">
                      <span class="config-label">子网掩码:</span>
                      <span>{{ config.mask }}</span>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <h2>默认端口组</h2>
          <div>
            <ul>
              <li><span>端口组名称</span><span>{{ state.portData.portName || '-' }}</span></li>
              <li><span>端口组VLAN</span><span>{{ state.portData.portVlan || '-' }}</span></li>
            </ul>
          </div>
        </div>
      </div>
		</div>
		<template #footer>
			<el-button v-if="state.current > 0" type="primary" @click="state.current--">上一步</el-button>
			<el-button @click="state.isShow = false">取消</el-button>
			<el-button v-if="state.current < 3" type="primary" @click="nextStep">下一步</el-button>
			<el-button v-if="state.current == 3" type="primary" @click="confirm" :loading="state.loading">确认</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Tickets, Setting, DocumentCopy,MessageBox } from '@element-plus/icons-vue'
const AddBasic = defineAsyncComponent(() => import('./AddBasic.vue'));
const AddNetwork = defineAsyncComponent(() => import('./AddNetwork.vue'));
const AddPort = defineAsyncComponent(() => import('./AddPort.vue'));
import { ElMessage } from 'element-plus';
import { distributedSwitchNew } from '/@/api/Network'; // 接口

// 定义变量内容
const state = reactive({
  isShow: false,
  loading: false,
  current: 0,
  besicTime: '',
  besicData: {
    name: '',
  },
  netTime: '',
  netData: {
    type: '',
    tableData: [],
  },
  portTime: '',
  portData: {
    portName: '',
    portVlan: '',
  },
});
// 基本信息数据
const basicOK = (data: any) => {
  state.besicData = data
  state.current++
}
// 网络配置
const netOK = (data: any) => {
  state.netData = data
  state.current++
}
// 默认端口组
const portOK = (data: any) => {
  state.portData = data
  state.current++
}
// 下一步
const nextStep = () => {
  if (state.current == 0) {
    state.besicTime = ""+new Date()
  }else if (state.current == 1) {
    state.netTime = ""+new Date()
  }else if (state.current == 2) {
    state.portTime = ""+new Date()
  }
}


// 辅助函数：获取网络类型文本
const getNetworkTypeText = (type: string) => {
  switch (type) {
    case 'yw': return '业务网络';
    case 'gl': return '管理网络';
    case 'cc': return '存储网络';
    default: return type || '-';
  }
};

// 辅助函数：获取链路聚合模式文本
const getLinkModeText = (mode: string) => {
  switch (mode) {
    case 'jt': return '静态';
    case 'dt': return '动态';
    default: return mode || '-';
  }
};

const emit = defineEmits(['returnOK']);
// 确认
const confirm =()=>{
  distributedSwitchNew({
    name: state.besicData.name,
    net_type: state.netData.type,
    net_data: state.netData.tableData,
    port_group_name: state.portData.portName,
    vlan_id: state.portData.portVlan,
  })
  .then(res => {
    if(res.code == 200) {
      state.isShow = false;
      emit('returnOK', 'refresh');
      ElMessage.success('新建分布式交换机操作已完成');
    }else {
      ElMessage.error(res.msg);
    }
  })
  .catch(err => {
    ElMessage.error(err.msg);
  })
  emit('returnOK', 'refresh');
}
const openDialog = () => {
  state.isShow = true;
  state.loading = false;
  state.current = 0
  nextTick(() => {
  })
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
  .paging-dialog-area {
    height: 600px;
    .paging-step-area {
      padding: 0 15%;
      height: 70px;
    }
    .paging-basic-content {
      width: 100%;
      height: calc(100% - 70px);
      overflow: auto;
      .paging-new-common {
        width: 100%;
        height: 100%;
        padding: 10px 30px;
      }
      .summary-area {
        padding: 0 50px;
        h2 {
          color: #409eff;
          border-bottom: 1px solid #409eff;
          font-weight: 600;
          padding: 10px 0;
          margin-bottom: 20px;
        }
        h3 {
          color: #606266;
          font-size: 14px;
          font-weight: 600;
          margin: 15px 0 10px 0;
        }
        ul {
          padding: 10px;
          margin: 0;
          list-style: none;
          li {
            padding: 5px 0;
            margin: 0;
            list-style: none;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            h3 {
              display: inline-block;
              font-weight: 600;
              width: 200px;
            }
            span:first-child {
              display: inline-block;
              width: 200px;
              font-weight: bold;
              color: #606266;
            }
            span:last-child {
              color: #303133;
            }
            .summary_notes {
              display: inline-block;
              width: 400px;
            }
          }
        }

        // 物理机配置列表样式
        .host-config-list {
          margin-top: 10px;

          .host-item {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;

            .host-name {
              font-weight: 600;
              color: #495057;
              margin-bottom: 6px;
            }

            .host-nics {
              font-size: 13px;
              color: #6c757d;
            }
          }
        }

        // 网络配置列表样式
        .network-config-list {
          margin-top: 10px;

          .config-item {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;

            .config-row {
              display: flex;
              margin-bottom: 4px;

              &:last-child {
                margin-bottom: 0;
              }

              .config-label {
                font-weight: 600;
                color: #495057;
                min-width: 100px;
                margin-right: 10px;
              }

              span:last-child {
                color: #6c757d;
                flex: 1;
              }
            }
          }
        }
      }
    }
  }

</style>
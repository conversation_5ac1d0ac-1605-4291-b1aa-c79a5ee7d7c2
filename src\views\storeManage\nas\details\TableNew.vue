<template>
	<el-dialog v-model="formItem.isShow" width="600">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title">添加存储目录</span>
		</template>
    <el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储目录" prop="name">
				<el-input v-model="formItem.name" placeholder="示例：/var/list" />
			</el-form-item>
      <el-form-item label="关联主机" >
				<el-button @click="connectClick">关联主机</el-button>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { portGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  name: '',
});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'blur' },
	],
});
// 打开弹窗
const openDialog = async () => {
	formItem.isShow = true;
	nextTick(() => {
		if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
	});
};
// 关联主机
const connectClick = ()=>{
}

const emit = defineEmits(['returnOK']);
const confirm = () => {
  if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        // formItem.isShow = false;
        // userNew({
				// 	username: formItem.username,
				// 	password: formItem.password,
        //   name: formItem.name,
				// 	role: formItem.role,
				// 	expiredday: timeFormat(formItem.time),
				// }).then((res) => {
				// 	if(res.msg == 'ok') {
        // 		ElMessage.success('新建用户操作完成');
				// 		emit('returnOK', 'refresh');
				// 	}else {
        // 		ElMessage.error(res.msg);
				// 	}
				// });
      }
    })
  }
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">

</style>
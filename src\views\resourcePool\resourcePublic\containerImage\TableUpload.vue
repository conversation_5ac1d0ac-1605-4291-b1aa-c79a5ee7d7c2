<template>
  <el-dialog
    v-model="formItem.isShow"
    title="拉取容器镜像"
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="目标主机" prop="host">
        <el-input v-model="formItem.host"  placeholder="请输入集群名称"/>
      </el-form-item>
      <el-form-item label="镜像仓库" prop="repo">
        <el-input v-model="formItem.repo"  placeholder="请输入仓库，如：tianwen1:5000/ubuntu"/>
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-input v-model="formItem.tag"  placeholder="请输入标签，如：20.05"/>
      </el-form-item>
      <el-form-item label="上传磁盘" prop="fileName">
				<el-upload
					ref="upload"
					class="upload-demo"
					action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
					:limit="1"
					:on-exceed="handleExceed"
					:auto-upload="false"
					data:
				>
					<template #trigger>
						<el-button type="primary">select file</el-button>
					</template>
					<el-button class="ml-3" type="success" @click="submitUpload">
						upload to server
					</el-button>
				</el-upload>
			</el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { containerEdit } from '/@/api/ResourcePool/container'; // 接口
import { diskSlice,diskUpload } from '/@/api/ResourcePool/storage.ts'; // 接口
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  host: '',
  repo: '',
  tag: '',
  file: null,
  storageType: '',
	net: '',
	diskMode: 'existing',
	
});
const rules = reactive<FormRules>({
  host: [{ required: true, message: '必选项',trigger: "blur" }],
  repo: [{ required: true, message: '必填项',trigger: "blur" }],
  tag: [{ required: true, message: '必填项',trigger: "blur" }],
  fileName: [{ required: true, message: '必选项',trigger: "blur" }],
})
const upload = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
				upload.value!.submit()
        // containerEdit({
        //   host: formItem.host,
        //   repo: formItem.repo,
        //   tag: formItem.tag,
        // })
        // .then(res => {
        //   ElMessage.success('拉取容器镜像操作已完成')
        //   emit('returnOK', 'refresh');
        // })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (treeItem: any) => {
	formItem.isShow = true;
	nextTick(() => {
    formItem.host = treeItem.ip
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
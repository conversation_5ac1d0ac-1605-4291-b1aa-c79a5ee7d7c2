<template>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    title="选择网卡"
    class="dialog-1000"
  >
    <div class="storage-pool-dialog">
      <my-table
        ref="tableRef"
        :pagination="state.pagination"
        :columns="state.columns"
        :request="getTableData"
      >
        <!-- 单选 -->
        <template #radio="{ row }">
          <el-radio-group v-model="state.tableID" @change="radioClick(row)">
            <el-radio :value="row.id"></el-radio>
          </el-radio-group>
        </template>
        <!-- 已用 -->
        <template #name="{ row }">
          <span>{{row.name}}</span>
        </template>
      </my-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="SelectDrive">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { storagePollQuery } from '/@/api/ResourcePool/storage.ts'; // 接口
import { driveData } from '/@/model/vm.ts';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const state = reactive({
  isShow: false,
  columns: driveData,
  pagination: {
		show: true,
	}, // 是否显示分页
  tableID: '',
  tableName: '',
});
const getTableData=async( page: EmptyObjectType)=>{
  return new Promise(async(resolve)=>{
    let list =[{name:'iso1',id:'ssdfa'},{name:'iso2',id:'sssssdfa'}]
      resolve({
        data:list, // 数据
        total: 2 // 总数
      }) 
      state.tableID = list[0].id
      state.tableName = list[0].name
      
    // await storagePollQuery({
    //   page: page.pageNum, // 当前页
    //   pagecount: page.pageSize, // 每页条
    //   search_str: '', // 搜索
    //   order_type: page.order, // 排序规则
    //   order_by: page.sort, // 排序列
    // }).then(res=>{
    //   resolve({
    //     data: res.result, // 数据
    //     total: res.result.length // 总数
    //   }) 
    // }).catch(err => {})
  })
}
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 单选磁盘
const radioClick=(row:any)=>{
  state.tableID = row.id
  state.tableName = row.name
}
const emit = defineEmits(['driveReturn']);
const confirm =()=>{
  state.isShow= false
  emit('driveReturn', {tableID:state.tableID,tableName:state.tableName});
}
const driveDialog = (row: any) => {
  state.isShow = true;
  nextTick(() => {
    refresh()
  })
};
// 暴露变量
defineExpose({
	driveDialog,
});
</script>
<style lang="scss" scoped>
  .storage-pool-dialog {
    height: 500px;
    position: relative;
  }
</style>
<template>
  <el-dialog
    v-model="formItem.isShow"
    title="拉取容器镜像"
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="容器名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入集群名称"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { containerEdit } from '/@/api/ResourcePool/container'; // 接口
import { propName } from '/@/model/resource.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  host: '',
  name: '',
});
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" },
  ],
})

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        containerEdit({
          name: formItem.name,
          host: formItem.host,
        })
        .then(res => {
          ElMessage.success('拉取容器镜像操作已完成')
          emit('returnOK', 'refresh');
        })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (treeItem: any) => {
	formItem.isShow = true;
	nextTick(() => {
    formItem.host = treeItem.ip
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
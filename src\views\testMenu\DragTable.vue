<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="tree-area"  @drop="handleDrop" @dragover.prevent>
							<el-tree
								ref="treeRef"
								:highlight-current="true"
			 					:default-expand-all="true"
			 					:expand-on-click-node="false"
								:data="state.treeData"
								draggable
								:allow-drop="allowDrop"
								:props="defaultProps"
								class="tree-container"
							/>
						</div>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<div class="store-area">
						<div class="page-btn-area">
							<div class="tabs-btn-area"></div>
							<div class="tabs-table-area">
								<el-table ref="tableRef" :data="state.tableData" border>
									<el-table-column prop="id" label="ID" width="80"></el-table-column>
									<el-table-column prop="name" label="名称">
                    <template #default="{ row }">
                      <div class="draggable-row" draggable="true" @dragstart="handleDragStart(row)">
                        {{ row.name }}
                      </div>
                    </template>
                  </el-table-column>
								</el-table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</el-card>
	</div>
</template>
<script setup lang="ts">
import { reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { Ref } from 'vue';
import { dayjs, ElMessage, ElTable } from 'element-plus';
import Sortable from 'sortablejs';
const tableRef: Ref<InstanceType<typeof ElTable> | null> = ref(null);
const treeRef = ref(null);

// 定义变量内容
const state = reactive({
	tableData: [
		{ id: 'aaa', name: '节点 1' },
		{ id: 'bbb', name: '节点 2' },
		{ id: 'ccc', name: '节点 3' },
	],
	treeData: [
		{
			id: '1-0',
			label: '根节点',
			children: [
				{ id: '2-0', label: '二级0', children: [{ id: '3-0', label: '三级0' }] },
				{ id: '2-1', label: '三级1' },
				{ id: '2-2', label: '三级2' },
			],
		},
	],
});
const defaultProps = { children: 'children', label: 'label' };
// 允许树接受所有拖拽项
const allowDrop = () => {
	return true;
};
const draggedData = ref(null); 
// 开始拖拽时，存储拖拽的数据
const handleDragStart = (event: DragEvent, row: any) => {
  draggedData.value = row;
  event.dataTransfer?.setData("text/plain", JSON.stringify(row)); // 设置拖拽数据
  console.log("开始拖拽：", row);
};
// 处理放入 `el-tree` 事件
const handleDrop = (event: DragEvent) => {
  console.log("放入 el-tree", event);
  event.preventDefault();

  const data = event.dataTransfer?.getData("text/plain");
  if (!data) return;

  const draggedItem = JSON.parse(data);
  console.log("解析的拖拽数据:", draggedItem);

  // 找到拖拽目标的 `el-tree` 节点
  const treeNode = event.target?.closest(".el-tree-node__content");
  if (!treeNode) {
    ElMessage.warning("请拖拽到有效的树节点上");
    return;
  }

  // 获取 `el-tree` 组件实例
  const treeInstance = treeRef.value as any;
  const node = treeInstance.getNode(treeNode);

  if (!node) return;
  console.log("拖拽到树节点：", node.data);

  // 将表格数据添加到树的子节点
  node.data.children = node.data.children || [];
  node.data.children.push({
    id: draggedItem.id,
    label: draggedItem.name,
    children: [],
  });

  ElMessage.success("成功添加到 " + node.data.label);
};
// 初始化表格拖拽
onMounted(() => {
	nextTick(() => {
		if (!tableRef.value) {
			return;
		}
		const tableEl = tableRef.value.$el.querySelector('.el-table__body-wrapper tbody');
		Sortable.create(tableEl, {
			group: 'dragTable', // 设置拖拽组
			animation: 150,
			ghostClass: 'sortable-ghost',
			onEnd(evt: any) {
				console.log('拖拽表格项完成：', state.tableData[evt.oldIndex]);
			},
		});
	});
});
</script>
<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 220px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.tree-area {
				width: 100%;
				height: calc(100% - 50px);
				overflow: auto;
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 240px);
			height: 100%;
			.store-area {
				padding-top: 15px;
				width: calc(100%);
				height: calc(100%);
				.page-btn-area {
					width: 100%;
					height: 100%;
					.tabs-btn-area {
						height: 50px;
						display: flex;
						justify-content: space-between;
					}
					.tabs-table-area {
						width: calc(100%);
						height: calc(100% - 50px);
						position: relative;
					}
				}
			}
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>

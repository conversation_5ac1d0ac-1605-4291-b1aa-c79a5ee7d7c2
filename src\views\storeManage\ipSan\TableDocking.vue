<template>
	<el-dialog v-model="formItem.isShow" width="600">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title">{{ formItem.title === 'new'?'接入':'修改' }} IPsan存储资源</span>
		</template>
    <el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储设备名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入端口组名称" />
			</el-form-item>
      <el-form-item label="存储 I P" prop="ip">
				<el-input v-model="formItem.ip" placeholder="请输入存储 IP" />
			</el-form-item>
      <el-form-item label="存储端口" prop="port">
        <el-input v-model="formItem.port" :min="1" type="number" placeholder="请输入端口" />
			</el-form-item>
			<el-form-item label="CHAP认证">
				<el-checkbox v-model="formItem.share" label="启用CHAP认证" />
			</el-form-item>
      <el-form-item label="用户" prop="user" v-if="formItem.share">
				<el-input v-model="formItem.user" placeholder="请输入用户" />
			</el-form-item>
      <el-form-item label="密码" prop="password" v-if="formItem.share">
        <el-input v-model="formItem.password" show-password placeholder="请输入密码" />
			</el-form-item>
      <el-form-item label="关联主机" v-if="formItem.title === 'new'">
				<span @click="clickTree">已选 （ {{ formItem.selectNodes.length }} ）</span>
			</el-form-item>
		</el-form>
		<ZtreePublick v-if="formItem.title === 'new'" :type='formItem.type' :zNodes='formItem.zNodes'  @returnOK="returnOK"></ZtreePublick>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { ipAccess } from '/@/api/StoreManage'; // 接口
import { propName,propIP,propPort } from '/@/model/network.ts'; // 表列、正则
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	title: '',
  name: '',
  ip: '',
  port: '3260',
  share: false,
  user: '',
  password: '',
  selectNodes: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0', status:'up' }],
});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'blur' },
	],
	ip: [
    { required: true, message: '必填项', trigger: 'blur' },
		{ validator: propIP, trigger: 'blur' },
  ],
	port: [
    { required: true, message: '必填项', trigger: 'blur' },
		{ validator: propPort, trigger: 'blur' }
  ],
	user: [{ required: true, message: '必选项', trigger: 'blur' }],
	password: [{ required: true, message: '必选项', trigger: 'blur' }],
});
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0', status:'-' },
		{ id: '2', name: '主机池1', pid: '1', status:'-' },
		{ id: '3', name: '集群1', pid: '2', status:'-' },
		{ id: '7', name: '集群2', pid: '2', status:'-' },
		{ id: '4', name: '主机1', pid: '3', status:'-' },
		{ id: '5', name: '主机2', pid: '3', status:'-' },
		{ id: '6', name: '主机3', pid: '3', status:'-' },
		{ id: '8', name: '主机4', pid: '7', status:'-' },
		{ id: '9', name: '主机6', pid: '7', status:'-' },
		// { id: '4', name: '主机1', pid: '3', status:'up' },
		// { id: '5', name: '主机2', pid: '3', status:'down' },
		// { id: '6', name: '主机3', pid: '3', status:'up' },
		// { id: '8', name: '主机4', pid: '7', status:'down' },
		// { id: '9', name: '主机6', pid: '7', status:'info' },
	];
};
const clickTree =()=>{
	console.log('点击了');
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0', status:'-' },
		{ id: '2', name: '主机池1', pid: '1', status:'-' },
		{ id: '3', name: '集群1', pid: '2', status:'-' },
		{ id: '7', name: '集群2', pid: '2', status:'-' },
		{ id: '4', name: '主机1', pid: '3', status:'down' },
		{ id: '5', name: '主机2', pid: '3', status:'down' },
		{ id: '6', name: '主机3', pid: '3', status:'down' },
		{ id: '8', name: '主机4', pid: '7', status:'down' },
		{ id: '9', name: '主机6', pid: '7', status:'down' },
	];
}
// 打开弹窗
const openDialog = async (type: string,row: any) => {
	formItem.isShow = true;
	formItem.title = type
	nextTick(() => {
		if(type == 'new') {
			if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
				ruleFormRef.value.resetFields();
			}
			setTimeout(() => {
				formItem.type = 'IPsan-接入'+new Date()
				treeData()
			}, 200);
		}else {
			formItem.name = row.name
			formItem.ip = row.ip.split(':')[0]
			formItem.port = row.ip.split(':')[1]
			formItem.share = row.share
		}
	});
};
const returnOK = (val: any) => {
	console.log('返回的树节点', val);
	formItem.selectNodes = val.filter((node: any) => node.level === 3);
}

const emit = defineEmits(['returnOK']);
const confirm = () => {
  if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        formItem.isShow = false;
        ipAccess({
					name: formItem.name,
					ip: formItem.ip + ':' + formItem.port,
					share: formItem.share,
					user: formItem.user,
					password: formItem.password,
					selectNodes: formItem.selectNodes,
				}).then((res) => {
					if(res.msg == 'ok') {
        		ElMessage.success('接入存储设备已完成');
						emit('returnOK', 'refresh');
					}else {
        		ElMessage.error(res.msg);
					}
				});
      }
    })
  }
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">

</style>
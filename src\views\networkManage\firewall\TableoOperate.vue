<template>
	<el-dialog v-model="formItem.isShow" :title="formItem.title" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="规则组名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入规则组名称"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { secureGroupNew,secureGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  title: '',
	name: '',
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
});
const emit = defineEmits(['returnOK']);
const confirm = () => {
  formItem.isShow = false;
  return emit('returnOK', {name:formItem.name,type:'new'});
	// if (ruleFormRef.value) {
	// 	// 确保 ruleFormRef 已初始化
	// 	ruleFormRef.value.validate((val) => {
	// 		if (val) {
  //       if(formItem.title == '新建规则组') {
  //         secureGroupNew({
  //           name: formItem.name,
  //         }).then((res) => {
  //           if(res.msg == 'ok') {
  //             formItem.isShow = false;
  //             ElMessage.success(formItem.title+'操作完成');
  //             emit('returnOK', 'refresh');
  //           }else {
  //             ElMessage.error(res.msg);
  //           }
  //         });
  //       }else {
  //         secureGroupEdit({
  //           name: formItem.name,
  //         }).then((res) => {
  //           if(res.msg == 'ok') {
  //             formItem.isShow = false;
  //             ElMessage.success(formItem.title+'操作完成');
  //             emit('returnOK', 'refresh');
  //           }else {
  //             ElMessage.error(res.msg);
  //           }
  //         });
  //       }
	// 		}
	// 	});
	// }
};
// 打开弹窗
const openDialog = async (type: string,row:any) => {
  formItem.isShow = true;
	nextTick(() => {
    formItem.title = type == 'new' ? '新建规则组' : '编辑规则组'
    if(type == 'new') {
      if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
        ruleFormRef.value.resetFields();
      }
    }else {
      formItem.name = row.name
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<template>
	<el-config-provider :size="getGlobalComponentSize" :locale="getGlobalI18n">
		<router-view v-show="setLockScreen" />
		<LockScreen v-if="themeConfig.isLockScreen" />
		<Setings ref="setingsRef" v-show="setLockScreen" />
		<CloseFull v-if="!themeConfig.isLockScreen" />
	</el-config-provider>
</template>

<script setup lang="ts" name="app">
	import { defineAsyncComponent, computed, ref, onBeforeMount, onMounted, onUnmounted, nextTick, watch } from 'vue';
	import { useRoute } from 'vue-router';
	import { useI18n } from 'vue-i18n';
	import { storeToRefs } from 'pinia';
	import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
	import { useThemeConfig } from '/@/stores/themeConfig';
	import other from '/@/utils/other';
	import { Local, Session } from '/@/utils/storage';
	import mittBus from '/@/utils/mitt';
	import { notFoundAndNoPower, staticRoutes } from '/@/router/route';

	// 引入组件
	const LockScreen = defineAsyncComponent(() => import('/@/layout/lockScreen/index.vue'));
	const Setings = defineAsyncComponent(() => import('/@/layout/navBars/topBar/skin.vue'));
	const CloseFull = defineAsyncComponent(() => import('/@/layout/navBars/topBar/closeFull.vue'));

	// 定义变量内容
	const { messages, locale } = useI18n();
	const setingsRef = ref();
	const route = useRoute();
	const stores = useTagsViewRoutes();
	const storesThemeConfig = useThemeConfig();
	const { themeConfig } = storeToRefs(storesThemeConfig);
	const routepathList = [...notFoundAndNoPower, ...staticRoutes].map(item => {
		return item.path;
	});

	// 设置锁屏时组件显示隐藏
	const setLockScreen = computed(() => {
		if (routepathList.includes(route.path)) {
			themeConfig.value.isDrawer = false;
			themeConfig.value.lockScreenTime = 30;
			Local.set('themeConfig', themeConfig.value);
			// 清除缓存/token等
		}
		// 防止锁屏后，刷新出现不相关界面
		return themeConfig.value.isLockScreen ? themeConfig.value.lockScreenTime > 1 : themeConfig.value.lockScreenTime >= 0;
	});
	// 获取全局组件大小
	const getGlobalComponentSize = computed(() => {
		return other.globalComponentSize();
	});
	// 获取全局 i18n
	const getGlobalI18n = computed(() => {
		return messages.value[locale.value];
	});

	const setRootFontsize = () => {
		// 设置根字体大小
		// 为解决在高分辨率下，第一次进界面是默认的问题

		// 如果第一次进界面dpi不是1的话，修改zoom
		// document.body.style.cssText += `zoom:${1 / window.devicePixelRatio};`; 

		const client = document.body.getBoundingClientRect();
		document.getElementsByTagName('html')[0].style.fontSize = client.width / 10 + 'px';
	};
	// 设置初始化，防止刷新时恢复默认
	onBeforeMount(() => {
		// // 设置批量第三方 icon 图标
		// setIntroduction.cssCdn();
		// // 设置批量第三方 js
		// setIntroduction.jsCdn();
	});
	// 页面加载时
	onMounted(() => {
		nextTick(() => {
			// 监听换肤弹窗点击打开
			mittBus.on('openSkin', () => {
				setingsRef.value.openDrawer();
			});
			// 获取缓存中的布局配置
			if (Local.get('themeConfig')) {
				storesThemeConfig.setThemeConfig({ themeConfig: Local.get('themeConfig') });
				document.documentElement.style.cssText = Local.get('themeConfigStyle');
			}
			// 获取缓存中的全屏配置
			if (Session.get('isTagsViewCurrenFull')) {
				stores.setCurrenFullscreen(Session.get('isTagsViewCurrenFull'));
			}
			setRootFontsize();
		});
	});
	// 页面销毁时，关闭监听布局配置/i18n监听
	onUnmounted(() => {
		mittBus.off('openSkin', () => { });
	});
	// 监听路由的变化，设置网站标题
	watch(
		() => route.path,
		() => {
			other.useTitle();
		},
		{
			deep: true,
		}
	);
</script>
<style  scoped lang="scss">
	::v-deep .el-button {
		span {
			font-size: 14px;
		}
	}
	::v-deep .el-input__inner {
		font-size: 14px;
	}
	::v-deep .set-icon-wrap {
		color: #606266;
		margin-right: 10px;
		cursor: pointer;
	}
</style>

<template>
	<div>
	<el-dialog v-model="formItem.isShow" :title="formItem.title" width="600">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="模板名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入模板名称" />
			</el-form-item>
			<el-form-item label="CPU架构" prop="framework">
				<el-select v-model="formItem.framework" style="width: 100%">
          <el-option label="X86" value="X86" />
          <el-option label="ARM" value="ARM" />
          <el-option label="MIPS" value="MIPS" />
          <el-option label="loongarch64" value="loongarch64" />
          <el-option label="SW" value="申威" />
        </el-select>
      </el-form-item>
			<el-form-item label="CPU数量" prop="cpu">
				<el-input v-model="formItem.cpu" :max="48" :min="2" type="number"><template #append>核</template></el-input>
			</el-form-item>
			<el-form-item label="内存" prop="memory">
				<el-input v-model="formItem.memory" type="number">
					<template #append>
						<el-select v-model="formItem.memoryUnit" style="width: 80px">
							<el-option label="KB" value="KB" />
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="存储类型" prop="storageType">
				<el-select v-model="formItem.storageType" :disabled="formItem.disabled" style="width: 100%">
          <el-option v-for="item in formItem.typeData" :key="item.code" :label="item.name" :value="item.code" />
				</el-select>
			</el-form-item>
			<el-form-item label="网卡选择" prop="net">
				<el-input v-model="formItem.net" disabled>
					<template #append>
						<el-button @click="formItem.netDialog = ''+new Date()" :icon="Search" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="磁盘模式">
				<el-radio-group v-model="formItem.diskMode">
					<el-radio value="existing">现有磁盘</el-radio>
					<el-radio value="upload">上传磁盘</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="磁盘" prop="diskName" v-if="formItem.diskMode == 'existing'">
				<el-input v-model="formItem.diskName" disabled placeholder="请选择磁盘">
					<template #append>
						<el-button @click="formItem.existingTime = ''+new Date()" :icon="Search" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="上传磁盘" prop="fileName" v-if="formItem.diskMode == 'upload'">
				<el-upload
					ref="uploadRef"
					class="upload-demo"
					:action="uploadAction"
					:limit="1"
					:on-exceed="handleExceed"
					:auto-upload="false"
					:before-upload="beforeUpload"
					:http-request="customUpload"
					:on-change="onChange"
					:on-remove="onRemove"
					:disabled="formItem.disabled"
					drag
				>
					<el-icon class="el-icon--upload"><UploadFilled /></el-icon>
					<div class="el-upload__text">
						将文件拖到此处，或<em>点击上传</em>
					</div>
				</el-upload>
				<!-- 上传进度 -->
				<div v-if="formItem.progressAll > 0" class="upload-progress">
					<el-progress
						:percentage="parseFloat((formItem.progressUsed/formItem.progressAll*100).toFixed(1))"
						:status="formItem.status"
						:stroke-width="8"
					/>
				</div>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
	<SelectExisting :existingTime="formItem.existingTime" :treeItem="props.treeItem" @existing-return="existingReturn"></SelectExisting>
	<SelectNet :netDialog="formItem.netDialog" :treeItem="props.treeItem" @net-return="netReturn"></SelectNet>

	</div>
</template>

<script lang="ts" setup name="UserNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type {
  FormInstance,
  FormRules,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadRequestOptions,
  UploadFile
} from 'element-plus';
import { Search, UploadFilled } from '@element-plus/icons-vue';
import { genFileId } from 'element-plus';
import pLimit from 'p-limit';
import { ElMessage } from 'element-plus';
import { propName, propCPU, propMEM } from '/@/model/templateManage.ts'; // 表列、正则
import { storageTypeQuery } from '/@/api/ResourcePool/storage.js'; // 接口
import { templateNew, templateEdit } from '/@/api/TemplateAPI'; // 接口
import { diskSlice, diskUpload } from '/@/api/ResourcePool/storage.ts'; // 接口

const SelectExisting = defineAsyncComponent(() => import('/@/views/resourcePool/resourcePublic/addVM/SelectExisting.vue'));
const SelectNet = defineAsyncComponent(() => import('/@/views/resourcePool/resourcePublic/addVM/SelectNet.vue'));

const props = defineProps({
	treeItem: {
    type: Object,
    required: true
  },
	newTime: {
		type: String,
		required: true,
	},
  editTime: {
		type: String,
		required: true,
	},
  tableRow: {
    type: Object,
    required: true,
  }
});
const ruleFormRef = ref<FormInstance>();
const uploadRef = ref<UploadInstance>();

const formItem = reactive({
	isShow: false,
  title: '新建模板',
	name: '',
	framework: 'X86',
	cpu: '2',
	memory: '2',
	memoryUnit: 'MB',
	typeData: [],
	storageType: '',
	net: '',
	diskMode: 'existing',
	fileName: null as File | null,
	diskName: '',
	diskPath: '',
	diskType: '',

	existingTime: '',
	netDialog: '',

	progressAll: 0,
	progressUsed: 0,
	status: '' as 'success' | 'exception' | '',
	fileSize: 0,
	disabled: false,
});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'bulk' },
	],
	framework: [{ required: true, message: '必选项', trigger: 'bulk' }],
	cpu: [
		{ required: true, message: '必选项' },
		{ validator: propCPU, trigger: 'bulk' },
	],
	memory: [
		{ required: true, message: '必选项' },
		{ validator: propMEM, trigger: 'bulk' },
	],
	fileName: [{ required: true, message: '必选项', trigger: 'bulk' }],
	storageType: [{ required: true, message: '必选项', trigger: 'bulk' }],
	diskName: [{ required: true, message: '必选项', trigger: 'bulk' }],
	net: [{ required: true, message: '必选项', trigger: 'bulk' }],

});
const emit = defineEmits(['returnOK']);

// 上传相关配置
const uploadAction = '/upload/v5/upload/chunk'; // 这个不会被使用，因为我们用自定义上传

// 查询存储类型
const storageTypeData = ()=>{
  storageTypeQuery().then((res:any)=>{
    formItem.typeData = res
    formItem.storageType = res[0].code
  })
}

// 处理文件超出限制
const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

// 上传前的钩子
const beforeUpload = (file: File) => {
  formItem.fileName = file;
  formItem.fileSize = file.size;

  // 触发表单验证
  if (ruleFormRef.value) {
    ruleFormRef.value.validateField('fileName');
  }

  return false; // 阻止自动上传，我们手动控制
};

// 文件状态改变时的钩子
const onChange = (uploadFile: UploadFile) => {
  if (uploadFile.raw) {
    formItem.fileName = uploadFile.raw;
    formItem.fileSize = uploadFile.raw.size;
  }
};

// 文件移除时的钩子
const onRemove = () => {
  formItem.fileName = null;
  formItem.fileSize = 0;
  formItem.progressAll = 0;
  formItem.progressUsed = 0;
  formItem.status = '';

  if (ruleFormRef.value) {
    ruleFormRef.value.validateField('fileName');
  }
};

// 自定义上传函数（这个不会被调用，因为我们手动控制上传）
const customUpload = (options: UploadRequestOptions) => {
  // 这里不做任何操作，因为我们在 confirm 中手动处理上传
  return Promise.resolve();
};
// 执行分片上传
const uploadFile = async (file: File) => {
	const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
	let chunks = Math.ceil(file.size / chunkSize);
	formItem.progressAll = chunks;
	formItem.progressUsed = 0;
	formItem.status = '';
	formItem.disabled = true;

	const limit = pLimit(3); // 设置最大并发数为3

	try {
		// 串行上传分片
		for (let i = 0; i < chunks; i++) {
			let start = i * chunkSize;
			let end = Math.min(file.size, start + chunkSize);
			let chunk = file.slice(start, end);

			await limit(() => uploadChunk(chunk, i, chunks, file.name));
		}

		// 通知完成
		await notifyComplete(chunks, file.name);
	} catch (error) {
		ElMessage.error('上传本地磁盘失败');
		formItem.status = 'exception';
		formItem.disabled = false;
	}
};

// 上传单个分片
const uploadChunk = async (chunk: Blob, index: number, total: number, filename: string) => {
	let formData = new FormData();
	formData.append('chunk', chunk);
	formData.append('index', index.toString());
	formData.append('total', total.toString());
	formData.append('filename', filename);
	formData.append('storage_pool_id', props.tableRow.id);

	try {
		await diskSlice(formData);
		formItem.progressUsed++;
	} catch (error) {
		formItem.status = 'exception';
		formItem.disabled = false;
		throw error;
	}
};

// 通知上传完成
const notifyComplete = async (total: number, filename: string) => {
	try {

		await diskUpload({
			name: formItem.name,
			total: total,
			filename: filename,
			storage_pool_id: props.tableRow.id,
			size: formItem.fileSize,
			remark: `模板磁盘: ${formItem.name}`
		});

		formItem.status = 'success';

		setTimeout(() => {
			ElMessage.success('新建模板-上传本地磁盘操作完成');
			formItem.isShow = false;
			emit('returnOK', 'refresh');
			resetUploadForm();
		}, 1500);
	} catch (error) {
		formItem.status = 'exception';
		formItem.disabled = false;
		ElMessage.error('上传完成通知失败');
	}
};

// 重置上传表单
const resetUploadForm = () => {
	formItem.fileName = null;
	formItem.fileSize = 0;
	formItem.progressAll = 0;
	formItem.progressUsed = 0;
	formItem.status = '';
	formItem.disabled = false;

	// 清空上传组件
	if (uploadRef.value) {
		uploadRef.value.clearFiles();
	}
};
// 磁盘返回
const existingReturn = (item: any) => {
	formItem.diskName = item.tableName
	formItem.diskPath = item.tablePath
	formItem.diskType = item.tableType
	// formItem.disk = val
}
// 网卡返回
const netReturn = (item: any) => {
	formItem.net = item.tableName
	// formItem.net = item
}
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        if(formItem.title == '新建模板') {
					if (formItem.diskMode == 'existing') {
						formItem.isShow = false;
						templateNew({
							name: formItem.name,
							cpu_arch: formItem.framework,
							vcpu: formItem.cpu,
							memory: formItem.memory,
							memory_unit: formItem.memoryUnit,
							disk_type_code: formItem.storageType,
							disk_name: formItem.diskName,
							disk_path: formItem.diskPath,
							disk_type: formItem.diskType,
							network: formItem.net
						}).then((res) => {
							if(res.msg == 'ok') {
								ElMessage.success('新建模板操作完成');
								emit('returnOK', 'refresh');
							}else {
								ElMessage.error(res.msg);
							}
						});
					}else {
						if (formItem.fileName) {
							uploadFile(formItem.fileName);
						} else {
							ElMessage.warning('请选择要上传的文件');
						}
					}
        }else {
          templateEdit({
            id: props.tableRow.id,
            name: formItem.name,
            cpu_arch: formItem.framework,
            vcpu: formItem.cpu,
            memory: formItem.memory,
            memory_unit: formItem.memoryUnit,
            disk_type_code: formItem.storageType,
            disk_name: formItem.diskName,
            disk_path: formItem.diskPath,
            disk_type: formItem.diskType,
            network: formItem.net
          }).then((res) => {
            if(res.msg == 'ok') {
              ElMessage.success('修改模板操作完成');
              emit('returnOK', 'refresh');
            }else {
              ElMessage.error(res.msg);
            }
          });
        }
			}
		});
	}
};
watch(
	() => props.newTime,
	() => {
		formItem.isShow = true;
		formItem.title = '新建模板';
		formItem.diskMode = 'existing';
		storageTypeData()
		resetUploadForm();
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
watch(
	() => props.editTime,
	() => {
		storageTypeData()
		formItem.isShow = true;
		formItem.title = '编辑模板';
    formItem.name = props.tableRow.name
    formItem.framework = props.tableRow.cpu_arch
    formItem.cpu = props.tableRow.vcpu
    formItem.memory = props.tableRow.memory
    formItem.memoryUnit = props.tableRow.memory_unit
    formItem.storageType = props.tableRow.disk_type_code
    formItem.diskName = props.tableRow.disk_name
    formItem.diskPath = props.tableRow.disk_path
    formItem.diskType = props.tableRow.disk_type
    formItem.net = props.tableRow.network
		resetUploadForm();
	}
);
</script>
<style lang="scss" scoped>
.upload-demo {
  width: 100%;
}

.upload-progress {
  margin-top: 10px;
	width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 90px;
	padding: 0;

  .el-icon--upload {
    font-size: 40px;
    color: #c0c4cc;
    margin-bottom: 0px;
		padding: 0;
  }

  .el-upload__text {
    color: #606266;
    font-size: 14px;

    em {
      color: #409eff;
      font-style: normal;
    }
  }
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}
</style>
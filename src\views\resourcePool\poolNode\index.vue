<template>
<div class="tabs-item-area">
  <div class="tabs-item-btn">
    <el-button type="primary" @click="addColonyClick" :icon="CirclePlus">添加集群</el-button>
    <!-- <el-button type="info" @click="editClick" :icon="Edit">修改主机池</el-button>
    <el-button type="primary" @click="deleteClick" :icon="Delete">删除主机池</el-button> -->
    <!-- <el-button type="primary" round @click="addHostClick">添加宿主机</el-button> -->
    <el-dropdown class="dropdown-bth" @command="handleCommand">
      <el-button type="info">
        <!-- <el-icon><MoreFilled /></el-icon><el-icon class="el-icon--right"><arrow-down /></el-icon> -->
        <!-- 更多<el-icon><MoreFilled /></el-icon> -->
        更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="xg">修改主机池</el-dropdown-item>
          <el-dropdown-item command="sc" style="color:red" divided>删除主机池</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
	<div class="route-list">
			<div v-for="item in state.routeList" :class="{ 'route-item': true, 'is-active': isActive(item) }" :key="item" @click="tagClick(item)">
				<span><span>{{ item }}</span></span>
			</div>
	</div>
	<div class="tabs-item-center">
    <PoolSummary v-if="state.acive == '概要'" :treeItem="props.treeItem" :acive="state.acive"></PoolSummary>
    <Colony v-if="state.acive == '集群'" :treeItem="props.treeItem" :acive="state.acive"></Colony>
    <Host v-if="state.acive == '宿主机'" :treeItem="props.treeItem" :acive="state.acive"></Host>
    <VM v-if="state.acive == '虚拟机'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></VM>
    <Storage v-if="state.acive == '存储池'" :treeItem="props.treeItem" :acive="state.acive"></Storage>
    <Shuttering v-if="state.acive == '模板'" :treeItem="props.treeItem" :acive="state.acive"></Shuttering>
    <RecycleBin v-if="state.acive == '回收站'" :treeItem="props.treeItem" :acive="state.acive" @returnOK="returnOK"></RecycleBin>
	</div>
  <PoolEdit :editTime="state.editTime" :treeItem="props.treeItem" @returnOK="returnOK"></PoolEdit>
  <PoolDelete :deleteTime="state.deleteTime" :treeItem="props.treeItem" @returnOK="returnOK"></PoolDelete>
  <AddClony :addColonyTime="state.addColonyTime" :treeItem="props.treeItem" @returnOK="returnOK"></AddClony>
  <!-- <AddHost :addHostTime="state.addHostTime" :treeItem="props.treeItem" @returnOK="returnOK"></AddHost> -->
</div>
</template>
<script setup lang="ts" name="PoolNode">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref } from 'vue';
import { CirclePlus,Edit,Delete } from '@element-plus/icons-vue'; // ICON
const PoolSummary = defineAsyncComponent(() => import('./PoolSummary.vue'));
const Colony = defineAsyncComponent(() => import('../resourcePublic/colony/Colony.vue'));
const Host = defineAsyncComponent(() => import('../resourcePublic/host/Host.vue'));
const VM = defineAsyncComponent(() => import('../resourcePublic/vm/VM.vue'));
const Storage = defineAsyncComponent(() => import('../resourcePublic/storage/Storage.vue'));
const Shuttering = defineAsyncComponent(() => import('../resourcePublic/shuttering/index.vue'));
const RecycleBin = defineAsyncComponent(() => import('../resourcePublic/recycleBin/index.vue'));

const PoolEdit = defineAsyncComponent(() => import('./PoolEdit.vue'));
const PoolDelete = defineAsyncComponent(() => import('./PoolDelete.vue'));
const AddClony = defineAsyncComponent(() => import('./AddClony.vue'));
// const AddHost = defineAsyncComponent(() => import('./AddHost.vue'));

const handleCommand = (command: string | number | object) => {
  switch (command) {
    case 'xg':
      editClick()
      break;
    case 'sc':
      deleteClick()
      break;
  }
}
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  }
});
const state = reactive({
  routeList: ['概要','集群','宿主机','虚拟机','存储池','模板','回收站'],
  acive: '概要',
  editTime: '',
  deleteTime: '',
  addColonyTime: '',
  // addHostTime: '',
});
const tagClick=(v: string) => {
  state.acive = v
}
const isActive = (v: string) => {
	if(state.acive ===v) {
    return true
  } else {
    return false
  }
};
const editClick=()=>{
  state.editTime = ""+new Date()
}
const deleteClick=()=>{
  state.deleteTime = ""+new Date()
}
const addColonyClick=()=>{
  state.addColonyTime = ""+new Date()
}
// const addHostClick=()=>{
//   state.addHostTime = ""+new Date()
// }
const emit = defineEmits(['returnOK']);
const returnOK = (item: string)=>{
  emit('returnOK', item);
}
</script>
<style lang="scss" scoped>
.tabs-item-area {
  width: calc(100%);
	height: calc(100%);
  .dropdown-bth {
    margin: 0 15px;
  }
  .route-list {
    width: calc(100% - 40px);
    height: 55px;
    // background: var(--el-fill-color-blank);
    background: #faf7f7;
    border-radius: 26px;
    margin: 10px;
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .route-item {
      position: relative;
      padding: 0 20px;
      font-size: 14px;
      line-height: 50px;
      cursor: pointer;
      margin: 0 10px;
      color: var(--el-color-title);
      border-radius: 3px;
      display: flex;
      height: 75%;
      align-items: center;

      &:hover {
        background: var(--el-color-primary-light-9);
        font-weight: bold;
        color: var(--el-color-primary);
        &::before {
          content: ' ';
          position: absolute;
          width: 4px;
          height: 18px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--el-color-primary);
          left: 0;
        }
      }
    }

    .is-active {
      // background: var(--el-color-primary-light-9);
      background: #fff9f5;
      font-weight: bold;
      color: var(--el-color-primary);
      &::before {
        content: ' ';
        position: absolute;
        width: 4px;
        height: 18px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--el-color-primary);
        left: 0;
      }
    }
  }
  .tabs-item-center {
    padding: 10px;
    border-radius: 10px;
    width: calc(100%);
    height: calc(100% - 110px);
    background: var(--el-fill-color-blank);
  }

}
</style>

<template>
	<div class="system-area layout-padding">
		<el-card>
      <div class="password-content-area">
        <el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
          <el-form-item label="登录账号" prop="username">
            <el-input v-model="formItem.username" size="large" disabled placeholder="请输入登录账号" />
          </el-form-item>
          <el-form-item label="旧密码" prop="loginPW">
            <el-input v-model="formItem.loginPW" size="large" show-password placeholder="请输入登录密码" />
          </el-form-item>
          <el-form-item label="新密码" prop="password">
            <el-input v-model="formItem.password" size="large" show-password placeholder="请输入新密码" />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPW">
            <el-input v-model="formItem.confirmPW" size="large" show-password placeholder="请重新输入新密码" />
          </el-form-item>
          <div class="button-area">
            <el-button type="warning" @click="resetClick">重置输入</el-button>
            <el-button type="primary" @click="confirmClick">确认修改</el-button>
            <el-button type="primary" @click="listClick">list</el-button>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts" name="Password">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessageBox,ElMessage } from 'element-plus';
import { useUserInfo } from '/@/stores/userInfo';
const userInfos = useUserInfo();
import { passwordChanges } from '/@/api/login'; // 接口
import { useRouter } from 'vue-router';
import { Session, Local } from '/@/utils/storage';
const router = useRouter();

const formItem = reactive({
  username: '',
  loginPW: '',
  password: '',
  confirmPW: '',
});
const propPwd = (rule: any, value: string, callback: any) => {
  if (value=='') {
    return callback(new Error('密码必须包含字母、数字、特殊符号（@_.）'));
  } else {
    return callback();
  }
};
const propCpwd = (rule: any, value: string, callback: any) => {
  if (value !== formItem.password) {
    return callback(new Error('两次输入的密码不一致'));
  } else {
    return callback();
  }
};
const rules = reactive<FormRules>({
	username: [{ required: true, message: '必填项', trigger: 'blur' }],
  loginPW: [{ required: true, message: '必填项', trigger: 'blur' }],
	password: [
		{ required: true, message: '必填项' },
		{ validator: propPwd, trigger: 'blur' },
	],
  confirmPW: [
    { required: true, message: '必填项' },
		{ validator: propCpwd, trigger: 'blur' },
  ]
})
const ruleFormRef = ref<FormInstance>();
const confirmClick = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(val=>{
      if (val) {
        ElMessageBox.confirm(`修改密码后将重新登录，是否继续？`, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          passwordChanges({
            username: formItem.username,
            password: formItem.loginPW,
            new_password: formItem.password
          }).then(res=>{
            if(res.msg == 'ok') {
              Session.clear();// 清除缓存/token等
              window.location.reload();// 使用 reload 时，不需要调用 resetRoute() 重置路由
              ElMessage.success('登录账号 '+formItem.username+' 密码修改完成，请重新登录。');
            }else{
              ElMessage.error('登录账号 '+formItem.username+res.msg)
            }
          })
        }).catch(() => {});
      }
    })
  }
}
const resetClick = () => {
  ruleFormRef.value?.resetFields();
  formItem.username = userInfos.userInfos.userName
}
import { permission } from '/@/stores/permission';
const permisData = permission();
const listClick = () => {
  // userInfos.userInfos.userName = 'aaaaa'
  // formItem.username = userInfos.userInfos.userName
  permisData.gailan =!permisData.gailan
  permisData.ziyuanchi =!permisData.ziyuanchi
}
onMounted(() => {
  formItem.username = userInfos.userInfos.userName
})
</script>
<style scoped lang="scss">
.system-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  position: relative;
  .password-content-area {
    width: 500px;
    height: 400px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); 
    .el-form-item--default {
      margin-bottom: 40px;
    }
    .button-area {
      // padding-top: 10px;
      width: 100%;
      display: flex;
      justify-content: space-evenly;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>

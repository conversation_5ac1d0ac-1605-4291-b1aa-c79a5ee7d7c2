<template>
	<div class="monitoring-area layout-padding">
    <el-card>
      <div class="log-general-area">
        <div class="tabs-btn-area">
          <el-button type="primary" plain @click="ignoreClick(state.tableSelect)">忽略</el-button>
          <el-button type="primary" plain @click="refresh">刷新</el-button>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<!-- 类型 -->
						<template #severity="{ row }">
							<span :style="{color:typeConversion('color',row.severity)}">{{typeConversion('text',row.severity)}}</span>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
            	<el-button type="primary" @click="ignoreClick([row])">忽略</el-button>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
		<Ignore :ignoreRow='state.ignoreRow' :ignoreTime='state.ignoreTime' ignoreType='集群' @returnOK="returnOK"></Ignore>
  </div>
</template>
<script setup lang="ts" name="ClusterAlarm">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { alarmColumns,typeConversion } from '/@/model/logManage.ts'; // 表列、正则
import { clusterAlarmQuery,clusterAlarmIgnore } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const Ignore = defineAsyncComponent(() => import('../generalPublic/Ignore.vue'));

// 定义变量内容
const state = reactive({
  columns: alarmColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSelect: [],
  ignoreRow: [],
  ignoreTime: '',
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(!true) {
      return new Promise(async(resolve)=>{
      clusterAlarmQuery().then((res:any)=>{
        resolve({
          data: res.data, // 数据
          total: res.total*1 // 总数
        })
      }).catch((err:any) => {
        resolve({
          data: [], // 数据
          total: 0 // 总数
        })
      })
    })
  }else {
    let list =[
      {
        "summary": "节点失联 (instance *************:9182)",
        "fingerprint": "17384282d8c6f233",
        "alertname": "节点失联",
        "instance": "*************:9182",
        "severity": "info",
        "description": "节点失联.\\n  VALUE = 0\\n  LABELS = map[__name__:up address_pool:flat instance:*************:9182 instance_flavor:0bbf770e-42c5-4f3a-95da-a24e558fc577 instance_id:07341d9f-4c49-4062-b966-db7f04c264ca instance_name:win2019_AD instance_status:SHUTOFF job:openstackwindows private_ip:************* project_id:27acb4dc568c4768ab387772b2151bea tag_os_type:windows user_id:c472431b2d214e95a16e35d85f0aab81]",
        "activeAt": "2025-02-11 09:31:08"
      },
      {
        "summary": "节点失联 (instance *************:9100)",
        "fingerprint": "4552acab80f90037",
        "alertname": "节点失联",
        "instance": "*************:9100",
        "severity": "info",
        "description": "节点失联.\\n  VALUE = 0\\n  LABELS = map[__name__:up address_pool:flat instance:*************:9100 instance_flavor:0bbf770e-42c5-4f3a-95da-a24e558fc577 instance_id:39552556-5fce-422a-b57b-4b5f265a0c95 instance_name:桌面_1M7NT instance_status:SHUTOFF job:openstacklinux private_ip:************* project_id:27acb4dc568c4768ab387772b2151bea tag_os_type:linux user_id:c472431b2d214e95a16e35d85f0aab81]",
        "activeAt": "2025-02-11 09:31:08"
      },
      {
        "summary": "节点失联 (instance *************:9182)",
        "fingerprint": "b66e48ccd745aef8",
        "alertname": "节点失联",
        "instance": "*************:9182",
        "severity": "info",
        "description": "节点失联.\\n  VALUE = 0\\n  LABELS = map[__name__:up address_pool:flat instance:*************:9182 instance_flavor:73abc605-c1a8-4a55-81e3-a25a69c93427 instance_id:6e1b7d3d-d985-4600-ab0e-8bebff85cd9b instance_name:win2019-修复 instance_status:SHUTOFF job:openstackwindows private_ip:************* project_id:27acb4dc568c4768ab387772b2151bea tag_os_type:windows user_id:c472431b2d214e95a16e35d85f0aab81]",
        "activeAt": "2025-02-11 09:32:08"
      },
      {
        "summary": "节点失联 (instance *************:9182)",
        "fingerprint": "c075aea007771893",
        "alertname": "节点失联",
        "instance": "*************:9182",
        "severity": "info",
        "description": "节点失联.\\n  VALUE = 0\\n  LABELS = map[__name__:up address_pool:flat instance:*************:9182 instance_flavor:5323fa09-fcfc-41bd-b79e-1845a62a9562 instance_id:ec562c4a-c76d-4094-9be7-04ead8870a62 instance_name:业务1 instance_status:SHUTOFF job:openstackwindows private_ip:************* project_id:27acb4dc568c4768ab387772b2151bea tag_os_type:windows user_id:c472431b2d214e95a16e35d85f0aab81]",
        "activeAt": "2025-02-11 09:31:08"
      }
    ]
    return {
			data: list, // 数据
			total: list.length, // 总数
		};
  }
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 忽略
const ignoreClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.ignoreRow = arr
    state.ignoreTime = ''+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  refresh()
}
onMounted(() => {
})
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .log-general-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;

    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<div class="prompt-area">
			<p>
				<el-icon color="#fe944d"><WarningFilled /></el-icon
				>可以选择跨集群的宿主机物理网卡关联，作为分布式交换机的上行链路。支持宿主机上的虚拟机，在分布式交换机上的网络中按照规则通信。
			</p>
		</div>
		<el-form-item label="分布式交换机" prop="name">
			<el-input v-model="formItem.name" placeholder="请输入分布式交换机名称" />
		</el-form-item>
		<el-form-item label="勾选物理网卡" prop="card">
			<el-input v-show="false" v-model="formItem.card" disabled/>
			<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes" @returnOK="returnOK"></ZtreePublick>
		</el-form-item>
	</el-form>
	
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts';
import { propName } from '/@/model/resource.ts'; // 表列、正则
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['basicOK']);
const formItem = reactive({
	name: '',
	card: '',
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
	treeTable: [{ hostName: '主机1',netName:[{name:'wk1'}],link:'',load:'',ip:'',mask:''}],
	netCard: ''
});
const rulesForm = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'blur' },
	],
	card: [{ required: true, message: '请勾选下面物理网卡', trigger: 'change' },],
});
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '主机1', pid: '0' },
		{ id: '2', name: '主机2', pid: '0' },
		{ id: '3', name: '主机3', pid: '0' },
		{ id: '4', name: '网卡1', pid: '1' },
		{ id: '5', name: '网卡2', pid: '1' },
		{ id: '6', name: '网卡3', pid: '2' },
		{ id: '7', name: '网卡4', pid: '2' },
		{ id: '8', name: '网卡5', pid: '3' },
		{ id: '9', name: '网卡6', pid: '3' },
	];
};
interface NetCard {
	name: string;
}
interface HostData {
	hostName: string;
	netName: NetCard[];
	link: string;
	load: string;
	ip: string;
	mask: string;
}
const returnOK = (val: any) => {
	let result: HostData[] = [];
	const hostMap = new Map<string, HostData>(); // 用于存储主机信息，避免重复查找

	val.forEach((node: any) => {
		// 只处理网卡数据
		if (node.pid !== '0') {
			let host = formItem.zNodes.find((item: any) => item.id === node.pid);
			if (host) {
				let existingHost = hostMap.get(host.name);
				if (!existingHost) {
					existingHost = {
						hostName: host.name,
						netName: [],
						link: '',
						load: '',
						ip: '',
						mask: '',
					};
				}
				existingHost.netName = [...existingHost.netName, { name: node.name }];
				hostMap.set(host.name, existingHost);
			}
		}
	});
	const resultArray = Array.from(hostMap.values());
	formItem.treeTable = resultArray;
	formItem.treeTable.length>0?formItem.card = 'yixuan':formItem.card = ''
};

watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('basicOK', formItem);
				}
			});
		}
	}
);
setTimeout(() => {
	formItem.type = '分布式存储' + new Date();
	formItem.treeTable = []
	treeData();
}, 200);
</script>
<style lang="scss" scoped>
.prompt-area {
	margin-bottom: 20px;
	p {
		color: #ccc;
		.el-icon {
			margin-right: 5px;
		}
	}
}
</style>


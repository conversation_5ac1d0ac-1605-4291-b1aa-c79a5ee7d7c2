<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
        <el-button type="primary" plain @click="newClick">新建存储池</el-button>
        <el-button type="primary" plain @click="refresh">刷新</el-button>
        <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
      </div>
      <div>
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:searchParams="state.searchParams"
				:columns="state.columns"
				:request="getTableData"
        @selectionChange='selectChange'
			>
        <!-- 总容量 -->
				<template #capacity="{ row }">
					<span>{{ capacityConversion(row.capacity) }}</span>
				</template>
        <!-- 已分配容量 -->
				<template #allocation="{ row }">
					<span>{{ capacityConversion(row.allocation) }}</span>
				</template>
        <!-- 可用容量 -->
				<template #available="{ row }">
					<span>{{ capacityConversion(row.available) }}</span>
				</template>
        <!-- 磁盘 -->
				<template #storage_volume="{ row }">
          <el-button type="primary" link @click="diskClick(row)">磁盘</el-button>
				</template>
        <!-- 操作 -->
        <template #operation="{ row }">
          <el-dropdown @command="commandItem($event,row)">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="bj">编辑</el-dropdown-item>
                <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
			</my-table>
    </div>
    <Disk :tableRow="state.tableRow" :diskTime="state.diskTime"></Disk>
    <StoreNew :newTime="state.newTime" :treeItem="props.treeItem" @returnOK="returnOK"></StoreNew>
    <StoreEdit :editTime="state.editTime" :tableRow="state.tableRow" @returnOK="returnOK"></StoreEdit>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts" name="Storage">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { storagePollQuery,storagePollDelete } from '/@/api/ResourcePool/storage.ts'; // 接口
import { storageColumns,capacityConversion } from '/@/model/resource.ts'; // 表格 正则
import { dayjs,ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const Disk = defineAsyncComponent(() => import('./disk/index.vue'));
const StoreNew = defineAsyncComponent(() => import('./StoreNew.vue'));
const StoreEdit = defineAsyncComponent(() => import('./StoreEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  tableSearch: '',
  columns: storageColumns as Array<MyTableColumns>, // 表格表头配置
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSelect: [],
  diskTime: '',
  tableRow: {},
  newTime: '',
  editTime: '',
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = ( params: EmptyObjectType,page: EmptyObjectType) => {
  state.tableSelect = []
  return new Promise(async (resolve) => {
    let type = ''
    switch (props.treeItem.level) {
      case 1:
        type = 'pool'
      break;
      case 2:
        type = 'cluster'
      break;
      case 3:
        type = 'host'
      break;
    }
    await storagePollQuery({
      type: type, // 树节点
      _id: props.treeItem.id, // 树ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页显示条数
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch // 收索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {})
  })
};
const tableRef = ref();
// 刷新
const refresh = ()=>{
  // tableRef.value.handleSearch(); // 收索事件 表1页
  tableRef.value.refresh(); // 刷新事件 表当前
}
// 新建存储池
const newClick = () => {
  state.newTime = ''+new Date()
}
// 磁盘点击
const diskClick = (row:any)=>{
  state.tableRow = row
  state.diskTime = ''+new Date()
}
// 表操作列
const commandItem = (item: string,row:never)=>{
  state.tableRow = row
  switch (item) {
    case 'bj':
      state.editTime = ''+new Date()
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 删除存储池
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '存储池/'+new Date()
  }
}

// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    storagePollDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then((res:any) => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除存储池操作完成');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    refresh()
  }
}
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>
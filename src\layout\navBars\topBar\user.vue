<template>
	<div class="layout-navbars-breadcrumb-user pr15" :style="{ flex: layoutUserFlexNum }">
		<!-- 组件大小控制 -->
		<!-- <el-dropdown :show-timeout="70" :hide-timeout="50" trigger="click" @command="onComponentSizeChange">
			<div class="layout-navbars-breadcrumb-user-icon">
				<i class="iconfont icon-ziti" :title="$t('message.user.title0')"></i>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="large" :disabled="state.disabledSize === 'large'">{{ $t('message.user.dropdownLarge') }}</el-dropdown-item>
					<el-dropdown-item command="default" :disabled="state.disabledSize === 'default'">{{ $t('message.user.dropdownDefault') }}</el-dropdown-item>
					<el-dropdown-item command="small" :disabled="state.disabledSize === 'small'">{{ $t('message.user.dropdownSmall') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown> -->
		<!-- 语言切换 -->
		<!-- <el-dropdown :show-timeout="70" :hide-timeout="50" trigger="click" @command="onLanguageChange">
			<div class="layout-navbars-breadcrumb-user-icon">
				<i
					class="iconfont"
					:class="state.disabledI18n === 'en' ? 'icon-fuhao-yingwen' : 'icon-fuhao-zhongwen'"
					:title="$t('message.user.title1')"
				></i>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="zh-cn" :disabled="state.disabledI18n === 'zh-cn'">简体中文</el-dropdown-item>
					<el-dropdown-item command="en" :disabled="state.disabledI18n === 'en'">English</el-dropdown-item>
					<el-dropdown-item command="zh-tw" :disabled="state.disabledI18n === 'zh-tw'">繁體中文</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown> -->
		<!-- 菜单搜索 -->
		<!-- <div class="layout-navbars-breadcrumb-user-icon" @click="onSearchClick">
			<el-icon :title="$t('message.user.title2')">
				<ele-Search />
			</el-icon>
		</div> -->

		<!-- 大屏 -->
		<!-- <div class="layout-navbars-breadcrumb-user-icon" @click="openScreen">
			<i class="icon-diqiu1 iconfont" :title="$t('message.user.screen')"></i>
		</div> -->

		<!-- 换肤 -->
		<div class="layout-navbars-breadcrumb-user-icon" @click="onLayoutSetingClick">
			<i class="icon-skin iconfont" :title="$t('message.user.skin')"></i>
		</div>
		<!-- 日志 -->
		<div class="layout-navbars-breadcrumb-user-icon" @click="openLogs">
			<el-badge :is-dot="state.dot" class="item">
				<i class="iconfont-vrtsc icon-xiaoxi" :title="$t('message.user.logs')"></i>
			</el-badge>
		</div>
		<!-- 铃铛 -->
		<div class="layout-navbars-breadcrumb-user-icon" @click="alarmClick">
			<el-badge :is-dot="false" :show-zero="false" :value="state.alarmNumber">
				<el-icon :title="$t('message.user.alarms')">
					<ele-Bell />
				</el-icon>
			</el-badge>
		</div>
		<!-- 展开 -->
		<!-- <div class="layout-navbars-breadcrumb-user-icon" ref="userNewsBadgeRef" v-click-outside="onUserNewsClick">
			<el-badge :is-dot="true">
				<el-icon :title="$t('message.user.alarms')"><ele-Bell /></el-icon>
				<el-icon :title="展开"><Fold /></el-icon>
			</el-badge>
		</div>
		<el-popover ref="userNewsRef" :virtual-ref="userNewsBadgeRef" placement="bottom" trigger="click"
			transition="el-zoom-in-top" virtual-triggering :width="300" :persistent="false">
			<UserNews />
		</el-popover> -->
		<!-- F11 -->
		<div class="layout-navbars-breadcrumb-user-icon" @click="onScreenfullClick">
			<i
				class="iconfont"
				:title="state.isScreenfull ? $t('message.user.title6') : $t('message.user.title5')"
				:class="!state.isScreenfull ? 'icon-fullscreen' : 'icon-tuichuquanping'"
			></i>
		</div>

		<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
			<span class="layout-navbars-breadcrumb-user-link">
				<div class="user-header">
					<SvgIcon class="layout-navbars-breadcrumb-icon" name="iconfont-vrtsc icon-yonghu1" />
				</div>
				<span>{{ userInfos.userName === '' ? '未登录' : userInfos.userName }}</span>
				<SvgIcon class="layout-navbars-breadcrumb-icon" name="iconfont-vrtsc icon-a-08-xitongshezhi" />
			</span>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item divided command="lockScreen">{{ $t('message.user.lockScreen') }}</el-dropdown-item>
					<el-dropdown-item divided command="rules">{{ $t('message.user.passwordRules') }}</el-dropdown-item>
					<el-dropdown-item divided command="password">{{ $t('message.user.changePassword') }}</el-dropdown-item>
					<el-dropdown-item divided command="about">{{ $t('message.user.about') }}</el-dropdown-item>
					<el-dropdown-item divided command="logOut">{{ $t('message.user.logout') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<info ref="infoRef"></info>
		<!-- <Search ref="searchRef" /> -->
	</div>
</template>

<script setup lang="ts" name="layoutBreadcrumbUser">
import { defineAsyncComponent, ref, unref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage, ClickOutside as vClickOutside } from 'element-plus';
import screenfull from 'screenfull';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import mittBus from '/@/utils/mitt';
import { Session, Local } from '/@/utils/storage';

import { signOut } from '/@/api/login'; // 接口

const info = defineAsyncComponent(() => import('/@/layout/navBars/topBar/info.vue'));

// 定义变量内容
const userNewsRef = ref();
const userNewsBadgeRef = ref();
const infoRef = ref();
const { locale, t } = useI18n();
const router = useRouter();
const stores = useUserInfo();
const storesThemeConfig = useThemeConfig();
const { userInfos } = storeToRefs(stores);
const { themeConfig } = storeToRefs(storesThemeConfig);
const searchRef = ref();
const state = reactive({
	isScreenfull: false,
	disabledI18n: 'zh-cn',
	disabledSize: 'large',
	dot: true,
	alarmNumber: 5,
});

// 设置分割样式
const layoutUserFlexNum = computed(() => {
	let num: string | number = '';
	const { layout, isClassicSplitMenu } = themeConfig.value;
	const layoutArr: string[] = ['defaults', 'columns'];
	if (layoutArr.includes(layout) || (layout === 'classic' && !isClassicSplitMenu)) num = '1';
	else num = '';
	return num;
});
// 全屏点击时
const onScreenfullClick = () => {
	if (!screenfull.isEnabled) {
		ElMessage.warning('暂不不支持全屏');
		return false;
	}
	screenfull.toggle();
	screenfull.on('change', () => {
		if (screenfull.isFullscreen) state.isScreenfull = true;
		else state.isScreenfull = false;
	});
};
// 消息通知点击时
const onUserNewsClick = () => {
	unref(userNewsRef).popperRef?.delayHide?.();
};
// 布局配置 icon 点击时
const onLayoutSetingClick = () => {
	mittBus.emit('openSkin');
};

// 打开日志信息
const openLogs = () => {
	router.push('/System/LogTabs');
};
// 打开告警
const alarmClick = () => {
	state.alarmNumber -- 
	state.alarmNumber == -1?state.alarmNumber =5:''
	router.push('/Monitoring/AllAlarms');
};
// 下拉菜单点击时
const onHandleCommandClick = (path: string) => {
	if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('message.user.logOutTitle'),
			message: t('message.user.logOutMessage'),
			showCancelButton: true,
			confirmButtonText: t('message.user.logOutConfirm'),
			cancelButtonText: t('message.user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('message.user.logOutExit');
					setTimeout(() => {
						done();
						setTimeout(() => {
							instance.confirmButtonLoading = false;
						}, 300);
					}, 700);
				} else {
					done();
				}
			},
		})
			.then(async () => {
				signOut()
				// 清除缓存/token等
				Session.clear();
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				window.location.reload();
				
			})
			.catch(() => {
				// // 清除缓存/token等
				// Session.clear();
				// // 使用 reload 时，不需要调用 resetRoute() 重置路由
				// window.location.reload();
			});
	} else if (path === 'lockScreen') {
		Local.remove('themeConfig');
		themeConfig.value.isLockScreen = true;
		themeConfig.value.lockScreenTime = 0;
		Local.set('themeConfig', themeConfig.value);
	} else if (path === 'rules') {
		router.push('/System/Secure');
	} else if (path == 'password') {
		router.push('/System/Password');
	} else if (path == 'about') {
		infoRef.value.openInfo();
	} else {
		router.push(path);
	}
};
// 菜单搜索点击
const onSearchClick = () => {
	searchRef.value.openSearch();
};
// 组件大小改变
const onComponentSizeChange = (size: string) => {
	Local.remove('themeConfig');
	themeConfig.value.globalComponentSize = size;
	Local.set('themeConfig', themeConfig.value);
	initI18nOrSize('globalComponentSize', 'disabledSize');
	window.location.reload();
};
// 语言切换
const onLanguageChange = (lang: string) => {
	Local.remove('themeConfig');
	themeConfig.value.globalI18n = lang;
	Local.set('themeConfig', themeConfig.value);
	locale.value = lang;
	other.useTitle();
	initI18nOrSize('globalI18n', 'disabledI18n');
};
// 初始化组件大小/i18n
const initI18nOrSize = (value: string, attr: string) => {
	(<any>state)[attr] = Local.get('themeConfig')[value];
};

const openScreen = () => {
	// 使用a标签为防止浏览器阻止
	const a = document.createElement('a');
	a.href = '/#/vis';
	a.target = '_blank';
	document.body.appendChild(a);
	a.click();
	document.body.removeChild(a);
};

// 页面加载时
onMounted(() => {
	if (Local.get('themeConfig')) {
		initI18nOrSize('globalComponentSize', 'disabledSize');
		initI18nOrSize('globalI18n', 'disabledI18n');
	}
});
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;

	&-link {
		height: 100%;
		display: flex;
		align-items: center;
		white-space: nowrap;

		&-photo {
			width: 25px;
			height: 25px;
			border-radius: 100%;
		}
	}

	&-icon {
		padding: 0 10px;
		cursor: pointer;
		color: var(--next-bg-topBarColor);
		height: 50px;
		line-height: 50px;
		display: flex;
		align-items: center;

		i {
			font-size: 20px !important;
		}

		&:hover {
			i {
				display: inline-block;
				animation: logoAnimation 0.3s ease-in-out;
			}
		}
	}

	:deep(.el-dropdown) {
		color: var(--next-bg-topBarColor);
	}

	:deep(.el-badge) {
		height: 40px;
		line-height: 40px;
		display: flex;
		align-items: center;
	}

	:deep(.el-badge__content) {
		color: var(--next-color-white);
		border: none;
	}

	:deep(.el-badge__content.is-fixed) {
		top: 6px;
	}

	.layout-navbars-breadcrumb-user-link {
		cursor: pointer;
		padding-left: 10px;

		> * {
			padding: 0 6px;
			font-size: 18px;
		}

		.user-header {
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background: rgb(222, 225, 230);
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0 5px;

			i {
				z-index: 999;
				font-size: 20px;
				color: #555f70;
			}
		}
	}
}
</style>

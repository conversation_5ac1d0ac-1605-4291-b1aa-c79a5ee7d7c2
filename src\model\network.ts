// 分布式交换机
const distributedColumns = [
	{ type: 'selection', wrap: true },
	{ label: '分布式交换机', prop: 'name', sortable: true, align: 'left' },
	{ label: '网络类型', tdSlot: 'type', align: 'center' },
	{ label: '关联主机数量', tdSlot: 'host', align: 'center' },
	{ label: '端口数量', tdSlot: 'port', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 虚拟交换机
const switchColumns = [
	{ type: 'selection', wrap: true },
	{ label: '交换机名称', prop: 'name', sortable: true, align: 'left' },
	{ label: '集群', tdSlot: 'colony', align: 'center' },
	{ label: '宿主机', tdSlot: 'host', align: 'center' },
	{ label: '类型', tdSlot: 'type', align: 'center' },
	{ label: '状态', prop: 'status', align: 'center' },
	{ label: '端口组', tdSlot: 'port', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 端口组
const portGroupColumns = [
	{ type: 'selection', wrap: true },
	{ label: '端口组名称', prop: 'name', sortable: true, align: 'center', wrap: true },
	{ label: '端口数', prop: 'pool', align: 'center' },
	{ label: 'VLAN', prop: 'vlan', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 活动适配器
const adapterColumns = [
	{ type: 'selection', wrap: true },
	{ label: '活动适配器', prop: 'name', sortable: true, align: 'center' },
];
// 防火墙组
const firewallColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name' },
	{ label: '规则', tdSlot: 'rule' },
	{ label: '描述', prop: 'describe' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 表列
const tableColumns = [
	{ type: 'selection', wrap: true },
	{ type: 'expand', expand: 'expandContent' },
	{ label: '名称', prop: 'name' },
	{ label: '子网', tdSlot: 'cidr', align: 'center' },
	{ label: '状态', prop: 'status', align: 'center' },
	{ label: '网络类型', prop: 'network_type', align: 'center' },
	{ label: 'VLAN ID', tdSlot: 'vlanid', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: '120px', wrap: true },
];
// 安全组表列
const secureGroupColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name' },
	{ label: '子网', tdSlot: 'cidr', align: 'center' },
	{ label: '状态', prop: 'status', align: 'center' },
	{ label: '网络类型', prop: 'network_type', align: 'center' },
	{ label: 'VLAN ID', tdSlot: 'vlanid', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: '120px', wrap: true },
];
// 名称
const propName = (rule: any, value: any, callback: any) => {
	const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
	if (!regex.test(value)) {
		callback(new Error('2-32 个中文、英文、数字、特殊字符@_.-'));
	} else {
		callback();
	}
};
// VLAN
const propVlan = (rule: any, value: any, callback: any) => {
	const regex = /^[1-9][0-9]*$/;
	if (regex.test(value) && value < 4097 && 0 < value) {
		callback();
	} else {
		callback(new Error('VLAN范围1-4096的整数'));
	}
};
// 子网
const propSub = (rule: any, value: any, callback: any) => {
	const regex = /^((25[0-5]|(2[0-4][0-9])|([01]?[0-9][0-9]?))\.){3}(25[0-5]|(2[0-4][0-9])|([01]?[0-9][0-9]?))\/([1-2]?[0-9]|3[0-2])$/;
	if (!regex.test(value)) {
		callback(new Error('请输入正确的子网，且掩码范围1-32。'));
	} else {
		callback();
	}
};
// 判定网络是否属于子网
const ipInSub = (ip: string, subnet: string) => {
	let [subnetIp, maskBits] = subnet.split('/');
	let maskNumber = 0;
	maskNumber = parseInt(maskBits, 10);
	let ipBinary = ip
		.split('.')
		.map((octet) => parseInt(octet, 10).toString(2).padStart(8, '0'))
		.join('');
	let subnetBinary = subnetIp
		.split('.')
		.map((octet) => parseInt(octet, 10).toString(2).padStart(8, '0'))
		.join('');
	for (let i = 0; i < maskNumber; i++) {
		if (ipBinary[i] != subnetBinary[i]) {
			return false;
		}
	}
	return true;
};
const ipToDecimal = (ip: string) => {
	return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0);
};
// 同一网段的不同IP判定
const subnetCheck = (gateway: string, ipPool: string) => {
	// 提取网关的首三个八位字节以形成默认的 /24 网络地址
	const gatewayParts = gateway.split('.').slice(0, 3).join('.');
	const poolParts = ipPool.split('.').slice(0, 3).join('.');

	// 判断 IP 是否在同一网段且不同
	if (gatewayParts === poolParts && gateway !== ipPool) {
		return true; // 在同一网段但不同 IP
	}
	return false; // 不在同一网段或者相同 IP
};
// 结束IP起始IP判断
const startEnd = (start: string, end: string) => {
	const startDecimal = ipToDecimal(start);
	const endDecimal = ipToDecimal(end);
	// 判断结束 IP 是否大于起始 IP
	if (endDecimal <= startDecimal) {
		return false; // 结束 IP 小于或等于起始 IP
	}
	// 提取网络地址部分以判断是否在同一网段（默认为 /24）
	const startNetwork = start.split('.').slice(0, 3).join('.');
	const endNetwork = end.split('.').slice(0, 3).join('.');
	// 判断是否在同一网段
	if (startNetwork === endNetwork) {
		return true; // 在同一网段，且结束 IP 大于起始 IP
	}
	return false; // 不在同一网段
};
const ruleData = [
	{ label: '定制TCP规则', value: 'GZ_TCP' },
	{ label: '定制UDP规则', value: 'GZ_UDP' },
	{ label: '定制ICMP规则', value: 'GZ_ICMP' },
	{ label: '所有ICMP协议', value: 'XY_ICMP' },
	{ label: '所有TCP协议', value: 'XY_TCP' },
	{ label: '所有UDP协议', value: 'XY_UDP' },
];
const directionData = [
	{ label: '入口', value: 'enter' },
	{ label: '出口', value: 'out' },
];
const openPortData = [
	{ label: '所有端口', value: 'all' },
	{ label: '指定端口', value: 'port' },
	{ label: '端口范围', value: 'range' },
];
const longRangeData = [
	{ label: 'CIDR', value: 'cidr' },
	{ label: '安全组', value: 'aqz' },
];
const ethernetData = [
	{ label: 'IPv4', value: 'ipv4' },
	{ label: 'IPv6', value: 'ipv6' },
];
// ICMP
const propICMP = (rule: any, value: any, callback: any) => {
	const regex = /^(-1|([0-9]{1,2}|1[0-9]{2}|2[0-4][0-9]|25[0-5]))$/;
	if (!regex.test(value)) {
		callback(new Error('ICMP类型的范围值-1至255'));
	} else {
		callback();
	}
};
// 端口
const propPort = (rule: any, value: any, callback: any) => {
	// const regex = /^(?:[1-9][0-9]{0,4}|[1-5][0-9]{5}|6[0-4][0-9]{4}|65[0-4][0-9]{3}|655[0-2][0-9]{1}|6553[0-4])$/;
	const regex = /^[1-9][0-9]*$/;
	if (regex.test(value) && value < 65535 && 0 < value) {
		callback();
	} else {
		callback(new Error('请输入1至65534之间的整数'));
	}
};
// IP
const propIP = (rule: any, value: any, callback: any) => {
	const regex =
		/^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$/;
	if (!regex.test(value)) {
		callback(new Error('请输入正确的IP地址'));
	} else {
		callback();
	}
};

export {
	distributedColumns,
	switchColumns,
	portGroupColumns,
	adapterColumns,
	firewallColumns,
	tableColumns,
	secureGroupColumns,
	propName,
	propVlan,
	propSub,
	ipInSub,
	subnetCheck,
	startEnd,
	ruleData,
	directionData,
	openPortData,
	longRangeData,
	ethernetData,
	propICMP,
	propPort,
	propIP,
};

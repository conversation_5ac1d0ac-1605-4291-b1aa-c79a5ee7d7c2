<template>
	<el-dialog v-model="formItem.isShow" title="对接存储" append-to-body class="dialog-700">
		<div class="form-switch-area">
			<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
				<el-form-item label="存储名称" prop="name">
					<el-input v-model="formItem.name" placeholder="请输入网络名称" />
				</el-form-item>
				<el-form-item label="存储类型" prop="type">
					<el-radio-group v-model="formItem.type">
						<el-radio value="local">本地存储</el-radio>
						<el-radio value="ceph">CEPH</el-radio>
						<el-radio value="iscis">ISCIS</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="使用方式">
					<el-checkbox v-model="formItem.share" label="共享存储" />
				</el-form-item>
				<el-form-item label="宿主机" prop="hosts">
					<el-select v-model="formItem.hosts" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3" placeholder="选择宿主机">
						<el-option v-for="item in formItem.hostData" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</el-form-item>
			</el-form>
      <div class="table-area">
        <my-table
          ref="tableRef"
          :pagination="formItem.pagination"
          :columns="formItem.columns"
          :request="getTableData"
          @selectionChange='selectChange'
        >
        </my-table>
      </div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="netNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { storeDocking,storageResourcesQuery } from '/@/api/StoreManage/index.ts'; // 接口
import { hostAllQuery } from '/@/api/Network'; // 接口
import { propName, propVlan, propSub, ipInSub } from '/@/model/network.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const props = defineProps({
	newTime: {
		type: String,
		required: true,
	},
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  columns: [] as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
	name: '',
	type: 'local',
	share: false,
  hosts: [],
  hostData: [{ name: '主机1', id: '1111111' }],
});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
	type: [{ required: true, message: '必选项', trigger: 'blur' }],
  hosts: [{ required: true, message: '必选项', trigger: 'change' }],
});
// 获取主机集群数据
const hostColnyQuery = ()=>{
  let hostData: any[] = []
  hostAllQuery()
  .then(res => {
    res?.forEach((em:any) => {
      hostData.push({
        name: em.name, 
        id: em.id
      })
    });
    formItem.hostData = hostData
  })
}
// 获取表数据
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  formItem.tableSelect = []
  if(true) {
    return {
      data: [{name: '测试1'}], // 数据
      total: 1 // 总数
    }
  }
	return new Promise(async(resolve)=>{
    storageResourcesQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: formItem.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 表格选中变化
const selectChange = (row: any)=>{
  formItem.tableSelect = row
}
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				storeDocking({
					name: formItem.name,
					type: formItem.type,
				}).then((res: any) => {
					if (res.msg == 'ok') {
						formItem.isShow = false;
						emit('returnOK', 'refresh');
						ElMessage.success('存储对接已完成');
					} else {
						ElMessage.error(res.msg);
					}
				});
			}
		});
	}
};
watch(
	() => props.newTime,
	(val) => {
		formItem.isShow = true;
    hostColnyQuery()
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
</script>
<style lang="scss" scoped>
.form-switch-area {
	width: 100%;
	overflow: auto;
	.route-input {
		width: 100%;
		display: flex;
	}
	.route-datas {
		height: 70px;
		overflow: auto;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		.route-item {
			width: 49%;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.route-wd {
				display: inline-block;
				width: 50%;
			}
			.route-xyt {
				display: inline-block;
				width: 42%;
			}
		}
	}
}
</style>
<style lang="scss" scoped>
.table-area {
	height: 400px;
	width: 100%;
	position: relative;
}
</style>
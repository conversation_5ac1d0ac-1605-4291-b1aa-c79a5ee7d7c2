<template>
	<el-card>
		<div>
			<el-form ref="ruleFormRef" :model="state" :rules="rules" label-width="auto">
				<el-form-item label="密码长度" prop="position">
					<el-input v-model="state.position" type="number" style="width: 135px">
						<template #append>位</template>
					</el-input>
					<el-icon class="icons"><InfoFilled /></el-icon>
					<span class="describe">范围最小值为 {{ state.position }} - 31 位，最大值为固定32位。</span>
				</el-form-item>
				<el-form-item label="安全等级" prop="level">
					<el-radio-group v-model="state.level" @change="migrateChange">
						<el-radio-button value="l">低</el-radio-button>
						<el-radio-button value="m">中</el-radio-button>
						<el-radio-button value="h">高</el-radio-button>
					</el-radio-group>
					<el-icon class="icons" :style="{ color: state.color }"><InfoFilled /></el-icon>
					<span :style="{ color: state.color }">{{ state.text }}</span>
				</el-form-item>
				<el-form-item label="有效期" prop="time">
					<el-input v-model="state.time" type="number" style="width: 135px">
						<template #append>天</template>
					</el-input>
					<el-icon class="icons"><InfoFilled /></el-icon>
					<span class="describe">有效期为密码定期修改时间。</span>
				</el-form-item>
				<el-form-item label="是否启用规则" prop="enable">
					<el-switch
						v-model="state.enable"
						inline-prompt
						active-text="&nbsp;&nbsp;启用 规则&nbsp;&nbsp;"
						inactive-text="&nbsp;&nbsp;禁用 规则&nbsp;&nbsp;"
						style="width: 135px"
					/>
					<el-icon class="icons"><InfoFilled /></el-icon>
					<span class="describe">规则禁用后将使用 默认规则（8-32位 低 安全等级）。</span>
				</el-form-item>
				<div class="button-area">
					<el-button type="primary" plain @click="initClick">初始化</el-button>
					<!-- <el-button type="primary" plain @click="resetClick">刷新规则</el-button> -->
					<el-button type="primary" @click="confirmClick">规则提交</el-button>
				</div>
			</el-form>
		</div>
	</el-card>
</template>
<script setup lang="ts" name="PasswordRulesSetting">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessageBox, ElMessage } from 'element-plus';
import { codeRuleQuery, codeRuleSubmit } from '/@/api/login'; // 接口
const state = reactive({
	id: '',
	position: '8',
	level: 'm',
	time: '30',
	enable: true,
	color: '#ff7474',
	text: '中级密码必须包含英文及数字，还可以使用部分特殊符号（@_.）。',
});
const rules = reactive<FormRules>({
	position: [
		{ required: true, message: '必填项', trigger: 'bulk' },
		// { validator: propName, trigger: 'change' },
	],
	level: [{ required: true, message: '必选项', trigger: 'bulk' }],
	time: [{ required: true, message: '必填项', trigger: 'bulk' }],
	enable: [{ required: true, message: '必选项', trigger: 'bulk' }],
});
const migrateChange = (item: string) => {
	if (item == 'l') {
		state.color = '#ff7474';
		state.text = '低级密码英文、数字、部分特殊符号（@_.），可单独或混搭使用。';
	} else if (item == 'm') {
		state.color = '#ffb91b';
		state.text = '中级密码必须包含英文及数字，还可以使用部分特殊符号（@_.）。';
	} else {
		state.color = '#87bf87';
		state.text = '高级密码必须包含英文数字及部分特殊符号（@_.）。';
	}
};
const ruleFormRef = ref<FormInstance>();
// 初始化 规则
const initClick = () => {
	ElMessageBox.confirm(`是否进行初始化密码规则？`, {
		dangerouslyUseHTMLString: true,
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			ruleFormRef.value?.resetFields()
		})
		.catch(() => {});
};
// 刷新 规则
const resetClick = () => {
	codeRuleQuery().then((res) => {
		state.id = res.id;
		state.position = res.pwd_length;
		state.level = res.pwd_class;
		state.time = res.pwd_expired_day;
		state.enable = res.status == 'on' ? true : false;
	});
};
// 确认 规则
const confirmClick = () => {
	ElMessageBox.confirm(`是否提交密码规则？`, {
		dangerouslyUseHTMLString: true,
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			codeRuleSubmit({
				id: state.id,
				pwd_length: state.position,
				pwd_class: state.level,
				pwd_expired_day: state.time,
				status: state.enable ? 'on' : 'off',
			}).then((res) => {
				if (res.msg == 'ok') {
					ElMessage.success('提交密码规则操作完成');
					resetClick();
				} else {
					ElMessage.error('提交密码规则操作失败');
				}
			});
		})
		.catch(() => {});
};
onMounted(() => {
	// resetClick()
});
</script>
<style scoped lang="scss">
.icons {
	margin: 0 10px;
	color: #ccc;
}
.describe {
	color: #ccc;
}
.button-area {
	width: 400px;
	display: flex;
	justify-content: space-evenly;
}
</style>
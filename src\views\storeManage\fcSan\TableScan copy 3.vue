<template>
	<el-dialog v-model="formItem.isShow" title="扫描FC-SAN存储资源" append-to-body width="600">
		<h3>勾选要扫描的主机</h3>
		<span>( 不选默认全部扫描 )</span>
		<div class="dialog-tree-body">
			<el-input v-model="formItem.searchValue" class="search-input" placeholder="搜索主机" :prefix-icon="Search" @input="onSearch" />
      <div class="tree-area">
        <ul id="treeDemo" class="ztree"></ul>
      </div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="quxiaoClick">选中树</el-button>
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { portGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const formItem = reactive({
	isShow: false,
	searchValue: '',
  zTreeObj: null as any, // 树形控件对象
});
const setting = {
  check: {
    enable: true, // 启用 checkbox
    chkboxType: { "Y": "s", "N": "s" } // 级联勾选（Y: 选中时子节点是否选中，N: 取消选中时子节点是否取消）
  },
  data: {
		simpleData: {
			enable: true,
			idKey: 'id',
			pIdKey: 'pid',
			rootPId: 0,
		},
	},
  view: {
		showIcon: false, // 显示图标
    fontCss: (treeId:any, treeNode:any) => (treeNode.highlight ? { color: 'red', 'font-weight': 'bold' } : { color: '#333' }),
	},
}
interface Node {
	id: string;
	name: string;
	pid: string;
}
let zNodes: Node[] = [];
const treeData = () => {
  formItem.searchValue = ''
	if (true) {
		zNodes = [
			{ id: '1', name: '资源节点', pid: '0' },
			{ id: '2', name: '主机池1', pid: '1' },
			{ id: '3', name: '集群1', pid: '2' },
			{ id: '4', name: '主机1', pid: '3' },
			{ id: '5', name: '主机2', pid: '3' },
			{ id: '6', name: '主机3', pid: '3' },
			{ id: '7', name: 'vm1', pid: '4' },
			{ id: '8', name: 'vm2', pid: '4' },
			{ id: '9', name: 'vm3', pid: '5' },
			{ id: '11', name: 'vm4', pid: '5' },
			{ id: '12', name: 'vm5', pid: '5' },
			{ id: '13', name: 'vm3', pid: '5' },
			{ id: '14', name: 'vm3', pid: '5' },
			{ id: '31', name: 'aaaaa', pid: '5' },
			{ id: '32', name: 'vm4', pid: '5' },
			{ id: '63', name: 'vm2', pid: '5' },
		];
		init();
	}
};
const init = () => {
	formItem.zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
	formItem.zTreeObj.expandAll(true); // 默认展开所有树的分支
};
// 打开弹窗
const openDialog = async () => {
	formItem.isShow = true;
	nextTick(() => {
		formItem.searchValue = '';
    treeData()
	});
};
// 获取选中的主机
const quxiaoClick =()=>{
  console.log(treeChecked())
}
const treeChecked = () => {
  if (!formItem.zTreeObj) return [];
  let nodesTrue = formItem.zTreeObj.getCheckedNodes(true); // 获取选中的节点
  let nodesFalse = formItem.zTreeObj.transformToArray(formItem.zTreeObj.getNodes()).filter((node:any) => !node.checked)
  let listTrue = nodesTrue.filter((node: any) => node.level === 4) // 先筛选出 level 为 4 的节点
  let listFalse = nodesFalse.filter((node: any) => node.level === 4) // 先筛选出 level 为 4 的节点
  if(nodesTrue.length == 0) {
    return listFalse
  }else {
    return listTrue
  }
};
const onSearch = () => {
  let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
  if (!zTreeObj) return;
	const value = formItem.searchValue.trim();
	const allNodes = zTreeObj.transformToArray(zTreeObj.getNodes());

	// 清除所有节点的高亮状态
	allNodes.forEach((node:any) => {
		node.highlight = false;
		zTreeObj.updateNode(node);
	});

	// 如果搜索值为空，直接返回
	if (!value) return;

	// 获取匹配的节点
	const matchedNodes = zTreeObj.getNodesByParamFuzzy('name', value);

	// 高亮匹配的节点并展开父节点
	matchedNodes.forEach((node:any) => {
		node.highlight = true;
		zTreeObj.updateNode(node);
		zTreeObj.expandNode(node.getParentNode(), true);
	});
};
const emit = defineEmits(['returnOK']);
const confirm = () => {};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">
h3 {
	display: inline-block;
	margin-bottom: 10px;
}
.dialog-tree-body {
	width: 100%;
	height: 300px;
	padding: 10px;
	border: 1px solid #ccc;
	.search-input {
		margin-bottom: 10px;
	}
	.tree-area {
		width: 100%;
		height: 230px;
    overflow: auto;
	}
}
</style>
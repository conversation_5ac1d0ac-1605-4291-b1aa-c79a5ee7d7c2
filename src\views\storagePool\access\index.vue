<template>
	<div>
		<el-menu default-active="IPsan" class="el-menu-vertical-demo" @select="onClose">
			<el-menu-item index="IPsan">
				<template #title>
					<span>IPsan</span>
				</template>
			</el-menu-item>
			<el-menu-item index="FCsan">
				<template #title>
					<span>FCsan</span>
				</template>
			</el-menu-item>
			<el-menu-item index="NAS">
				<template #title>
					<span>NAS</span>
				</template>
			</el-menu-item>
			<el-menu-item index="NVME">
				<template #title>
					<span>NVME</span>
				</template>
			</el-menu-item>
			<el-menu-item index="distributed">
				<template #title>
					<span>分布式存储</span>
				</template>
			</el-menu-item>
		</el-menu>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { dayjs } from 'element-plus';
// 定义变量内容
const state = reactive({
	
});
const emit = defineEmits(['accessOK']);
const onClose = (item: string) => {
  emit('accessOK', item);
}
// 页面加载时
onMounted(() => {
  emit('accessOK', 'IPsan');
});
</script>
<style scoped lang="scss">
.el-menu-vertical-demo {
	width: 100%;
	.el-menu-item.is-active {
		color: var(--next-color-white) !important;
	}
	.el-menu-item {
		margin-bottom: 20px;
	}
	.el-menu-hover-bg-color, .el-menu-item:hover, .el-menu-item.is-active, .el-sub-menu.is-active .el-sub-menu__title, .el-sub-menu:not(.is-opened):hover .el-sub-menu__title {
		background: var(--el-menu-active-color)!important;
	}
}
</style>

<template>
	<el-dialog v-model="formItem.isShow" title="新建用户" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="登录账号" prop="username">
				<el-input v-model="formItem.username" placeholder="请输入登录账号" />
			</el-form-item>
			<el-form-item label="登录密码" prop="password">
				<el-input v-model="formItem.password" show-password placeholder="请输入登录的密码" />
			</el-form-item>
			<el-form-item label="确认密码" prop="confirmPW">
				<el-input v-model="formItem.confirmPW" show-password placeholder="请重新入输登录密码" />
			</el-form-item>
			<el-form-item label="用户名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入用户名" />
			</el-form-item>
			<el-form-item label="用户角色" prop="role">
				<el-select v-model="formItem.role" style="width: 100%">
          <el-option label="审计员" value="adtadm" />
          <el-option label="安全员" value="secadm" />
          <el-option label="操作员" value="operator" />
        </el-select>
			</el-form-item>
			<el-form-item label="有效期" prop="time">
				<el-date-picker v-model="formItem.time" type="date" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="UserNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { userNew } from '/@/api/System'; // 接口
import { propName, propPassword,timeFormat } from '/@/model/resource.ts'; // 表列、正则
const props = defineProps({
	newTime: {
		type: String,
		required: true,
	},
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	username: '',
	password: '',
	confirmPW: '',
	name: '',
	role: 'operator',
	time: '',
});
const propCpwd = (rule: any, value: any, callback: any) => {
	if (formItem.password !== value) {
		callback(new Error('两次输入的密码不匹配'));
	} else {
		callback();
	}
}
const rules = reactive<FormRules>({
	username: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'bulk' },
	],
	password: [
		{ required: true, message: '必填项' },
		{ validator: propPassword, trigger: 'blur' },
	],
	confirmPW: [
		{ required: true, message: '必填项' },
		{ validator: propCpwd, trigger: 'blur' },
	],
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
	role: [{ required: true, message: '必选项', trigger: 'bulk' }],
	time: [{ required: true, message: '必选项', trigger: 'bulk' }]
});
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				userNew({
					username: formItem.username,
					password: formItem.password,
          name: formItem.name,
					role: formItem.role,
					expiredday: timeFormat(formItem.time),
				}).then((res) => {
					if(res.msg == 'ok') {
        		ElMessage.success('新建用户操作完成');
						emit('returnOK', 'refresh');
					}else {
        		ElMessage.error(res.msg);
					}
				});
			}
		});
	}
};
watch(
	() => props.newTime,
	(val) => {
		formItem.isShow = true;
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
</script>
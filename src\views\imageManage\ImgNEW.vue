<template>
	<el-dialog v-model="formItem.isShow" title="新建镜像" width="600">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="镜像名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入镜像名称" />
			</el-form-item>
			<el-form-item label="镜像类型" prop="imgType">
        <el-select v-model="formItem.imgType" style="width: 100%" @change="osChange">
          <el-option label="iso" value="iso" />
          <el-option label="qcow2" value="qcow2" />
          <el-option label="raw" value="raw" />
        </el-select>
			</el-form-item>
			<el-form-item label="系统类型" prop="osType">
        <el-select v-model="formItem.osType" style="width: 100%">
          <el-option label="Windows" value="windows" />
          <el-option label="Linux" value="linux" />
        </el-select>
			</el-form-item>
			<el-form-item label="镜像" prop="imgFile">
        <el-upload
					ref="upload"
					class="upload-demo"
					action="/upload/v1/images/upload"
					:limit="1"
					:data="{
						name: formItem.name,
						imgType: formItem.imgType,
						osType: formItem.osType,
					}"
					:on-exceed="handleExceed"
					:auto-upload="false"
					:on-change="onChange"
					:on-remove="onRemove"
					:on-success="onSuccess"
					:on-error="onError"
				>
					<template #trigger>
						<el-button type="primary" plain>选择文件</el-button>
					</template>
					<!-- <template #tip>
						<div class="el-upload__tip text-red">
							只能上传一个文件, 新文件将覆盖旧文件
						</div>
					</template> -->
				</el-upload>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="submitUpload">生成镜像</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="imgNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessage,genFileId } from 'element-plus';
import { imageNew } from '/@/api/ImageManage'; // 接口
import { propName } from '/@/model/resource.ts'; // 表列、正则
const props = defineProps({
	newTime: {
		type: String,
		required: true,
	},
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	name: '',
	imgType: 'iso',
	osType: 'windows',
	imgFile: '',
});
const propFile = (rule: any, value: any, callback: any) => {
	if (value == '') {
		callback(new Error('请选择文件'));
	} else if (sameType() != formItem.imgType) {
		callback(new Error('文件类型与镜像类型不一致'));
	} else {
		callback();
	}
};
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
  imgType: [{ required: true, message: '必选项', trigger: 'blur' }],
  osType: [{ required: true, message: '必选项', trigger: 'blur' }],
	imgFile: [
		{ required: true, message: '必选项' },
		{ validator: propFile, trigger: 'blur' }
	],
});
// 系统选择
const osChange = (value: any) => {
	ruleFormRef.value?.validateField('imgFile',(val) => {})
};
const upload = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
// 文件状态改变
const onChange = (uploadFile: any) => {
	formItem.imgFile = uploadFile.name
	ruleFormRef.value?.validateField('imgFile',(val) => {})
};
// 文件移出
const onRemove = (uploadFile: any) => {
	formItem.imgFile = ''
	ruleFormRef.value?.validateField('imgFile',(val) => {})
};
// 上传成功
const onSuccess = (response: any, file: any) => {
	imageNew({
    name: formItem.name,
    disk_format: formItem.imgType,
    os_type: formItem.osType,
    filename: response.filename,
    hash512: response.hash512,
    size: response.size,
	}).then((res) => {
		if(res.msg == 'ok') {
			formItem.isShow = false
  		ElMessage.success('新建镜像操作完成');
			emit('returnOK', 'refresh');
		}else {
  		ElMessage.error(res.msg);
		}
	});
};
// 上传失败
const onError = (err: any, file: any) => {
	formItem.imgFile = ''
	onRemove(file)
	ElMessage.error('上传镜像失败');
};
const emit = defineEmits(['returnOK']);
const submitUpload = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				upload.value!.submit()
			}
		});
	}
};
// 类型-镜像 同类型判断
const sameType = () => {
	let listIndex = formItem.imgFile.lastIndexOf(".")
	return formItem.imgFile.slice(listIndex + 1,listIndex+formItem.imgType.length+1).toLowerCase()
}
watch(
	() => props.newTime,
	(val) => {
		formItem.isShow = true;
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value?.resetFields();
	}
);
</script>
<style>
.upload-demo {
	width: 100%;
}
</style>
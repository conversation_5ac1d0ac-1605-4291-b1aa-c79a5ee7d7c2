<template>
	<div>
	<el-dialog v-model="formItem.isShow" :title="formItem.title" width="600">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="模板名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入模板名称" />
			</el-form-item>
			<el-form-item label="CPU架构" prop="framework">
				<el-select v-model="formItem.framework" style="width: 100%">
          <el-option label="X86" value="X86" />
          <el-option label="ARM" value="ARM" />
          <el-option label="MIPS" value="MIPS" />
          <el-option label="loongarch64" value="loongarch64" />
          <el-option label="SW" value="申威" />
        </el-select>
      </el-form-item>
			<el-form-item label="CPU数量" prop="cpu">
				<el-input v-model="formItem.cpu" :max="48" :min="2" type="number"><template #append>核</template></el-input>
			</el-form-item>
			<el-form-item label="内存" prop="memory">
				<el-input v-model="formItem.memory" type="number">
					<template #append>
						<el-select v-model="formItem.memoryUnit" style="width: 80px">
							<el-option label="KB" value="KB" />
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="存储类型" prop="storageType">
				<el-select v-model="formItem.storageType" :disabled="formItem.disabled" style="width: 100%">
          <el-option v-for="item in formItem.typeData" :key="item.code" :label="item.name" :value="item.code" />
				</el-select>
			</el-form-item>
			<el-form-item label="网卡选择" prop="net">
				<el-input v-model="formItem.net" disabled>
					<template #append>
						<el-button @click="formItem.netDialog = ''+new Date()" :icon="Search" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="磁盘模式">
				<el-radio-group v-model="formItem.diskMode">
					<el-radio value="existing">现有磁盘</el-radio>
					<el-radio value="upload">上传磁盘</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="磁盘" prop="diskName" v-if="formItem.diskMode == 'existing'">
				<el-input v-model="formItem.diskName" disabled placeholder="请选择磁盘">
					<template #append>
						<el-button @click="formItem.existingTime = ''+new Date()" :icon="Search" />
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="上传磁盘" prop="fileName" v-if="formItem.diskMode == 'upload'">
				<input type="file" @change="handleFileChange" class="upload-input" />
      	<el-progress v-if="formItem.progressAll!==0" :percentage="parseFloat((formItem.progressUsed/formItem.progressAll*100).toFixed(1))" :status="formItem.status" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
	<SelectExisting :existingTime="formItem.existingTime" :treeItem="props.treeItem" @existing-return="existingReturn"></SelectExisting>
	<SelectNet :netDialog="formItem.netDialog" :treeItem="props.treeItem" @net-return="netReturn"></SelectNet>

	</div>
</template>

<script lang="ts" setup name="UserNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { Search,Delete,UploadFilled } from '@element-plus/icons-vue'
import pLimit from 'p-limit';
import { ElMessage } from 'element-plus';
import { propName, propCPU, propMEM } from '/@/model/templateManage.ts'; // 表列、正则
import { storageTypeQuery } from '/@/api/ResourcePool/storage.js'; // 接口
import { templateNew,templateEdit } from '/@/api/TemplateAPI'; // 接口
import { diskSlice,diskUpload } from '/@/api/ResourcePool/storage.ts'; // 接口

const SelectExisting = defineAsyncComponent(() => import('/@/views/resourcePool/resourcePublic/addVM/SelectExisting.vue'));
const SelectNet = defineAsyncComponent(() => import('/@/views/resourcePool/resourcePublic/addVM/SelectNet.vue'));

const props = defineProps({
	treeItem: {
    type: Object,
    required: true
  },
	newTime: {
		type: String,
		required: true,
	},
  editTime: {
		type: String,
		required: true,
	},
  tableRow: {
    type: Object,
    required: true,
  }
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  title: '新建模板',
	name: '',
	framework: 'X86',
	cpu: '2',
	memory: '2',
	memoryUnit: 'MB',
	typeData: [],
	storageType: '',
	net: '',
	diskMode: 'existing',
	fileName: '',
	diskName: '',
	diskPath: '',
	diskType: '',

	existingTime: '',
	netDialog: '',

	progressAll: 0,
	progressUsed: 0,
	status: '',
	fileSize:  0,


});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'bulk' },
	],
	framework: [{ required: true, message: '必选项', trigger: 'bulk' }],
	cpu: [
		{ required: true, message: '必选项' },
		{ validator: propCPU, trigger: 'bulk' },
	],
	memory: [
		{ required: true, message: '必选项' },
		{ validator: propMEM, trigger: 'bulk' },
	],
	fileName: [{ required: true, message: '必选项', trigger: 'bulk' }],
	storageType: [{ required: true, message: '必选项', trigger: 'bulk' }],
	diskName: [{ required: true, message: '必选项', trigger: 'bulk' }],
	net: [{ required: true, message: '必选项', trigger: 'bulk' }],

});
const emit = defineEmits(['returnOK']);
// 查询存储类型
const storageTypeData = ()=>{
  storageTypeQuery().then((res:any)=>{
    formItem.typeData = res
    formItem.storageType = res[0].code
  })
}
const handleFileChange = (event: any) => {
	formItem.fileName = event.target.files[0];
};
const uploadFile = async (file: any) => {
	const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
	let chunks = Math.ceil(file.size / chunkSize);
	formItem.progressAll = chunks;
	formItem.fileSize = file.size;
	
	const limit = pLimit(5); // 设置最大并发数为5

	// 创建分片数组
	const chunkPromises = [];
	for (let i = 0; i < chunks; i++) {
		let start = i * chunkSize;
		let end = Math.min(file.size, start + chunkSize);
		let chunk = file.slice(start, end);
		chunkPromises.push({ chunk, index: i });
	}

	try {
		// 创建一个队列用于并发上传
    for (const { chunk, index } of chunkPromises) {
      // 使用 limit 保证并发控制
      await limit(() => uploadChunk(chunk, index, chunks, file.name));
    }
		// 并发上传分片
		// await Promise.all(chunkPromises.map(({ chunk, index }) => limit(() => uploadChunk(chunk, index, chunks, file.name))));
		// 通知完成
		await notifyComplete(chunks, file.name);
	} catch (error) {
		ElMessage.error('上传本地磁盘失败');
	}
};

const uploadChunk = async (chunk: string, index: number, total: number, filename: string) => {
	let formData = new FormData();
	formData.append('chunk', chunk);
	formData.append('index', index.toString());
	formData.append('total', total.toString());
	formData.append('filename', filename);
	formData.append('storage_pool_id', props.tableRow.id);
	try {
		await diskSlice(formData);
	  formItem.progressUsed++
	} catch (error) {
		formItem.status = 'error'
		throw error;
	}
};
const notifyComplete = async (total: number, filename: string) => {
	try {
		await diskUpload({
			name: formItem.name,total:total,
			filename:filename,
			storage_pool_id: props.tableRow.id,
			size:formItem.fileSize
		});
		formItem.status = 'success'
		setTimeout(() => {
			ElMessage.success('新建模板-上传本地磁盘操作完成');
			formItem.isShow = false;
			emit('returnOK', 'refresh');
		}, 1000);
	} catch (error) {
		// console.error('通知完成失败：', error);
		formItem.status = 'exception'
	}
};
// 磁盘返回
const existingReturn = (item: any) => {
	formItem.diskName = item.tableName
	formItem.diskPath = item.tablePath
	formItem.diskType = item.tableType
	// formItem.disk = val
}
// 网卡返回
const netReturn = (item: any) => {
	formItem.net = item.tableName
	// formItem.net = item
}
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        if(formItem.title == '新建模板') {
					if (formItem.diskMode == 'existing') {
						formItem.isShow = false;
						templateNew({
							name: formItem.name,
							cpu_arch: formItem.framework,
							vcpu: formItem.cpu,
							memory: formItem.memory,
							memory_unit: formItem.memoryUnit,
							disk_type_code: formItem.storageType,
							disk_name: formItem.diskName,
							disk_path: formItem.diskPath,
							disk_type: formItem.diskType,
							network: formItem.net
						}).then((res) => {
							if(res.msg == 'ok') {
								ElMessage.success('新建模板操作完成');
								emit('returnOK', 'refresh');
							}else {
								ElMessage.error(res.msg);
							}
						});
					}else {
						uploadFile(formItem.fileName);
					}
        }else {
          templateEdit({
            id: props.tableRow.id,
            name: formItem.name,
            cpu_arch: formItem.framework,
            vcpu: formItem.cpu,
            memory: formItem.memory,
            memory_unit: formItem.memoryUnit,
            disk_type_code: formItem.storageType,
            disk_name: formItem.diskName,
            disk_path: formItem.diskPath,
            disk_type: formItem.diskType,
            network: formItem.net
          }).then((res) => {
            if(res.msg == 'ok') {
              ElMessage.success('修改模板操作完成');
              emit('returnOK', 'refresh');
            }else {
              ElMessage.error(res.msg);
            }
          });
        }
			}
		});
	}
};
watch(
	() => props.newTime,
	(val) => {
		formItem.isShow = true;
		formItem.title = '新建模板';
		formItem.diskMode = 'existing';
		storageTypeData()
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
watch(
	() => props.editTime,
	(val) => {
		storageTypeData()
		formItem.isShow = true;
		formItem.title = '编辑模板';
    formItem.name = props.tableRow.name
    formItem.framework = props.tableRow.cpu_arch
    formItem.cpu = props.tableRow.vcpu
    formItem.memory = props.tableRow.memory
    formItem.memoryUnit = props.tableRow.memory_unit
    formItem.storageType = props.tableRow.disk_type_code
    formItem.diskName = props.tableRow.disk_name
    formItem.diskPath = props.tableRow.disk_path
    formItem.diskType = props.tableRow.disk_type
    formItem.net = props.tableRow.network
	}
);
</script>
<style lang="scss" scoped>
	.upload-input {
		width: 425px;
	}
	:deep(.el-progress__text) {
		min-width: auto;
	}
</style>
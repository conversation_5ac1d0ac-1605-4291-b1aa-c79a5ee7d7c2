<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="告警忽略"
    class="dialog-500"
  >
    <div>
      <span>是否忽略下列告警信息？</span>
      <p style="color:red;word-wrap: break-word">{{formItem.names.toString()}}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="Ignore">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { clusterAlarmIgnore,storageAlarmIgnore } from '/@/api/LogManage'; // 接口

const props = defineProps({
  ignoreRow: {
    type: Array,
    required: true
  },
  ignoreTime: {
    type: String,
    required: true
  },
  ignoreType: {
    type: String,
    required: true
  }
});
interface FormItem {
  isShow: boolean;
  names: any[];
  ids: any[];
}
const formItem = reactive<FormItem>({
  isShow: false,
  names: [],
  ids: [],
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  formItem.isShow = false;
  emit('returnOK', 'delete');
  if(props.ignoreType == '集群') {
    clusterAlarmIgnore({
      names: formItem.names,
      ids: formItem.ids,
    })
    .then(res => {
      emit('returnOK', 'refresh');
    })
  }else {
    storageAlarmIgnore({
      names: formItem.names,
      ids: formItem.ids,
    })
    .then(res => {
      emit('returnOK', 'refresh');
    })
  }
  
}
watch(
  ()=> props.ignoreTime,
  (val)=>{
    formItem.isShow = true;
    let ids:any[] = [];
    let names:any[] = [];
    props.ignoreRow.forEach((item:any) => {
      names.push(item.summary);
      ids.push(item.fingerprint);
    });
    formItem.ids = ids;
    formItem.names = names;
  }
);
</script>
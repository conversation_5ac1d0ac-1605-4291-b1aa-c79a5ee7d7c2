<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="新建磁盘"
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="磁盘名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入磁盘名称"/>
      </el-form-item>
      <el-form-item label="创建方式" prop="way">
        <el-radio-group v-model="formItem.way">
          <el-radio value="new">普通创建</el-radio>
          <el-radio value="disk">上传本地磁盘</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formItem.way=='new'" label="存储格式" prop="format">
        <el-select v-model="formItem.format" style="width: 100%">
          <el-option v-for="item in formItem.formatData" :key="item.id" :label="item.name" :value="item.id"/>
				</el-select>
      </el-form-item>
      <el-form-item v-if="formItem.way=='new'" label="磁盘容量" prop="disk">
				<el-input v-model="formItem.disk" type="number">
					<template #append>
						<el-select v-model="formItem.unit" style="width: 80px">
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
      </el-form-item>
      <el-form-item v-if="formItem.way=='new'" label="置备类型" prop="type">
        <el-select v-model="formItem.type" style="width: 100%">
          <el-option label="精简置备" :value="1" />
          <el-option label="厚置备延迟置零" :value="2" />
          <el-option label="厚置备置零" :value="3" />
				</el-select>
      </el-form-item>
      <el-form-item v-if="formItem.way=='new'" label="备注">
        <el-input v-model="formItem.notes" :rows="2" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息"/>
      </el-form-item>
      <el-form-item v-if="formItem.way=='disk'" label="上传磁盘" prop="file">
        <el-upload
					ref="upload"
					class="upload-demo"
					action="/upload/v6/upload/disk"
					:limit="1"
					:data="{
						name: formItem.name,
						storage_pool_id: props.tableRow.id,
            host: '***********'
					}"
					:on-exceed="handleExceed"
					:auto-upload="false"
					:on-change="onChange"
					:on-remove="onRemove"
					:on-success="onSuccess"
					:on-error="onError"
				>
					<template #trigger>
						<el-button type="primary" style="width: 350px" plain>选择文件</el-button>
					</template>
					<template #tip>
						<div class="el-upload__tip text-red">
							只能上传一个文件, 新文件将覆盖旧文件
						</div>
					</template>
				</el-upload>
			</el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="DiskNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize,UploadInstance,UploadProps, FormInstance, FormRules,UploadRawFile } from 'element-plus'
import { ElMessage,genFileId } from 'element-plus';
import { diskFormat,diskNew } from '/@/api/ResourcePool/storage.ts'; // 接口
import { propName,propNumber } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  tableRow: {
    type: Object,
    required: true
  },
  newTime: {
    type: String,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name:'',
  way: 'new',
  format: '',
  formatData: [],
  disk: '1',
  unit: 'MB',
  type: 1,
  notes: '',
  file: '',
});
const propFile = (rule: any, value: any, callback: any) => {
	if (value == '') {
		callback(new Error('请选择文件'));
	// } else if (sameType() != formItem.imgType) {
	// 	callback(new Error('文件类型与镜像类型不一致'));
	// } else {
  } else {
		callback();
	}
};
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "blur" },
  ],
  way: [{ required: true, message: '必选项', trigger: 'blur' }],
  format: [{ required: true, message: '必选项', trigger: 'blur' }],
  disk: [
    { required: true, message: '必选项', trigger: 'blur' },
    { validator: propNumber, trigger: "change" },
  ],
  type: [{ required: true, message: '必选项', trigger: 'blur' }],
  file: [
		{ required: true, message: '必选项' },
		{ validator: propFile, trigger: 'blur' }
	],
})
// 查询存储格式
const formatQuery = () => {
  diskFormat().then((res:any)=>{
    formItem.formatData = res
    formItem.format = res[0].id
  })
}
const upload = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
// 文件状态改变
const onChange = (uploadFile: any) => {
	formItem.file = uploadFile.name
	ruleFormRef.value?.validateField('file',(val) => {})
};
// 上传成功
const onSuccess = (response: any, file: any) => {
  console.log('上传成功',response)
  console.log('file',file)
};
// 文件移出
const onRemove = (uploadFile: any) => {
	formItem.file = ''
	ruleFormRef.value?.validateField('file',(val) => {})
};
// 上传失败
const onError = (err: any, file: any) => {
	formItem.file = ''
	onRemove(file)
	ElMessage.error('上传镜像失败');
};
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      // let size = 0
      // if (formItem.unit == 'MB') {
      //   size = parseInt(formItem.disk)*1024*1024
      // }else if(formItem.unit == 'GB') {
      //   size = parseInt(formItem.disk)*1024*1024*1024
      // }else if(formItem.unit == 'TB') {
      //   size = parseInt(formItem.disk)*1024*1024*1024*1024
      // }
      if (val) {
        
        upload.value!.submit()

        // formItem.isShow = false;
        // diskNew({
        //   // host: '***********',
        //   storage_device_id: props.tableRow.storage_device_id,
        //   storage_pool_id: props.tableRow.id,
        //   name: formItem.name,
        //   path: '', // 默认是存储池路径
        //   encrypt: 0, // 加密
        //   volume_type:  formItem.format, // 存储卷类型
        //   join_type: 3, // 加入类型
        //   capacity: size,
        //   preallocation: formItem.type, // 置备类型
        //   remark: formItem.notes,
        // })
        // .then((res:any) => {
        //   emit('returnOK', 'refresh');
        // })
      }
    })
  }
}
watch(
  ()=> props.newTime,
  (val)=>{
    formItem.isShow = true;
    formItem.unit == 'MB'
    formItem.notes == ''
    formatQuery()
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
  }
);
</script>
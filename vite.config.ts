import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv, ConfigEnv } from 'vite';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import viteCompression from 'vite-plugin-compression';
import { buildConfig } from './src/utils/build';
import postCssPxToRem from 'postcss-pxtorem';

const pathResolve = (dir: string) => {
	return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
	'/@': pathResolve('./src/'),
	'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
};

const viteConfig = defineConfig((mode: ConfigEnv) => {
	const env = loadEnv(mode.mode, process.cwd());
	return {
		plugins: [vue(), vueSetupExtend(), viteCompression(), JSON.parse(env.VITE_OPEN_CDN) ? buildConfig.cdn() : null],
		root: process.cwd(),
		resolve: { alias },
		base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
		optimizeDeps: { exclude: ['vue-demi'] },
		server: {
			host: '0.0.0.0',
			port: env.VITE_PORT as unknown as number,
			open: JSON.parse(env.VITE_OPEN),
			hmr: true,
			ws: true,
			// https: {
			// 	// 配置https证书
			// 	key: fs.readFileSync('ssl/lee.key'),
			// 	cert: fs.readFileSync('ssl/lee.crt'),
			// },
			proxy: {
				'/vtl': {
					target: `https://**************:8001/vtl/v1`,
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/vtl/, '/'),
				},
				'/login': {
					target: `http://**************:8088`,
					// target: `http://************:8088`,
					// target: 'http://***********:8088', // sjb
					// target: `http://**************:8088`, // yjy
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/login/, '/'),
				},
				'/theapi': {
					// target: `http://**************:8006`,
					// target: `http://***********:8006`, // sjb
					target: `http://**************:8006`, // yjy
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/theapi/, '/'),
				},
				'/user': {
					target: `http://**************:8089`,
					// target: `http://**************:8089`, // yjy
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/user/, '/'),
				},
				'/acapi': {
					target: `http://**************:9998`,
					// target: `http://192.168.7.93:9998`,
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/acapi/, '/'),
				},
				'/upload': {
					// target: `http://**************:8090`,
					target: `http://**************:8090`, // yjy
					// target: `http://***********:8090`, // sjb
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/upload/, '/'),
				},
				'/glcLog': {
					target: `http://**************:9996/glc`,
					changeOrigin: true,
					secure: false, // https 不验证证书
					rewrite: (path) => path.replace(/^\/glcLog/, '/'),
				},
			},
		},
		build: {
			outDir: 'dist',
			assetsInlineLimit: 0, // 图片太小时，被打包成base64打包到js文件里，想要在文件夹显示出来设置此项为0
			chunkSizeWarningLimit: 1500,
			rollupOptions: {
				output: {
					chunkFileNames: 'assets/js/[name]-[hash].js',
					entryFileNames: 'assets/js/[name]-[hash].js',
					assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups!.moduleName ?? 'vender';
						}
					},
				},
				...(JSON.parse(env.VITE_OPEN_CDN) ? { external: buildConfig.external } : {}),
			},
		},
		css: {
			preprocessorOptions: {
				css: { charset: false },
			},
			postcss: {
				plugins: [
					// 自适应rem布局
					postCssPxToRem({
						// 1rem的大小
						rootValue: 1920 / 10, // 设计稿 / 10
						propList: ['*'],
					}),
				],
			},
		},
		define: {
			__VUE_I18N_LEGACY_API__: JSON.stringify(false),
			__VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
			__INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
			__NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
			__NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
		},
	};
});

export default viteConfig;

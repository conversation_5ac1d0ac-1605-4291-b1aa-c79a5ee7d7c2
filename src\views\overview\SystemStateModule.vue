<template>
	<div class="task-statistics">
		<my-echarts :options="state.chartOption"></my-echarts>
	</div>
</template>

<script setup lang="ts" name="SystemStateModule">
import { defineAsyncComponent, reactive, onMounted, ref, Ref, inject, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Plus, Minus, Refresh, Edit, Link, Delete, Search, CopyDocument, Lock } from '@element-plus/icons-vue';
import { transformBySizeNumber } from '/@/utils/tools';

const MyEcharts = defineAsyncComponent(() => import('/@/components/echarts/index.vue'));
const props = defineProps({
  systemNumber: {
    type: Number,
    required: true
  }
});
const getOption = () => {
  let data = props.systemNumber<1?1:props.systemNumber
	let level = '70';
	let colors = '#f45d3f';
	if (data <= 30) {
		level = '优';
		colors = '#2ebe76';
	} else if (data > 30 && data <= 70) {
		level = '良';
		colors = '#fcc65c';
	} else {
		level = '差';
		colors = '#f45d3f';
	}
	return {
		backgroundColor: '#fff',
		series: [
			{
				type: 'gauge',
				radius: '95%',
				z: 1,
				startAngle: 225,
				endAngle: -45,
				splitNumber: 8,
				splitLine: {
					show: false,
				},
				detail: {
					show: true,
					offsetCenter: [0, '50%'],
					// fontSize: 18,
					// formatter: (val) => [`{a|${pointData}}`, `{b|系统表现}`].join(""),
					formatter: `{a|${level}}\n{b|系统表现}`,
					rich: {
						a: {
							fontSize: transformBySizeNumber(20 /192),
							lineHeight: transformBySizeNumber(40 /192),
							fontFamily: 'Microsoft YaHei',
							fontWeight: 'bold',
							color: colors,
						},
						b: {
							fontSize: transformBySizeNumber(14 /192),
							lineHeight: transformBySizeNumber(20 /192),
							padding: [-15, 0, 0, 0],
							fontFamily: 'Microsoft YaHei',
							fontweight: '400',
							color: '#000',
						},
					},
				},
				pointer: {
					show: true,
					width: '3',
					length: '60%',
				},
				itemStyle: {
					color: '#ccc',
					borderColor: '#000',
					borderWidth: transformBySizeNumber(3 /192),
				},
				data: [{ value: data }],
				// 仪表盘的线，颜色值为一个数组
				axisLine: {
					show: true,
					lineStyle: {
						width: transformBySizeNumber(30 /192),
						opacity: 1,
						color: [
							[
								data,
								{
									x: 0,
									y: 0,
									x1: 0,
									y1: 0,
									colorStops: [
										{
											offset: 1,
											color: '#f45d3f',
										},
										{
											offset: 0.5,
											color: '#fcc65c',
										},
										{
											offset: 0,
											color: '#2ebe76',
										},
									],
								},
							],
							// [1, "rgba(82, 255, 0, 0.7)"],
						],
					},
				},
				// 仪表盘刻度标签
				axisLabel: {
					show: true,
					distance: -45,
					formatter: (val: number) => {
						const num = Math.floor(val);
						return num % 20 === 0 ? num : '';
					},
					textStyle: {
						color: '#ffffff',
						fontSize: '10',
						fontFamily: 'Microsoft YaHei',
						fontWeight: 400,
					},
				},
				axisTick: {
					show: true,
					lineStyle: {
						color: '#fff',
						width: 4,
					},
					length: transformBySizeNumber(30 /192),
				}, //刻度样式
			},
			{
				//指针外环
				type: 'pie',
				hoverAnimation: false,
				legendHoverLink: false,
				radius: ['5%', '10%'],
				z: 10,
				label: {
					normal: {
						show: false,
					},
				},
				labelLine: {
					normal: {
						show: false,
					},
				},
				data: [
					{
						value: 100,
						itemStyle: {
							normal: {
								color: '#67b3ef',
							},
						},
					},
				],
			},
			{
				//指针内环
				type: 'pie',
				hoverAnimation: false,
				legendHoverLink: false,
				radius: ['0%', '5%'],
				z: 10,
				label: {
					normal: {
						show: false,
					},
				},
				labelLine: {
					normal: {
						show: false,
					},
				},
				data: [
					{
						value: 100,
						itemStyle: {
							normal: {
								color: '#12214c',
							},
						},
					},
				],
			},
		],
	};
};
// 定义变量内容
const state = reactive<EmptyObjectType>({
	chartOption: getOption(),
});

// 页面加载时
onMounted(() => {
	window.addEventListener('resize', () => {
		state.chartOption = getOption();
	});
});
watch(() => props.systemNumber, (val) => {
    getOption();
})
</script>

<style scoped lang="scss">
.task-statistics {
	height: calc(100%);
}
</style>

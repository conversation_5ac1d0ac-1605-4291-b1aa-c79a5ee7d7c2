<template>
  <div class="resource-pool-container">
    <div class="colony-information-area">
      <p style="cursor: pointer" @click="summaryQuery" class="information-title"><span>集群信息</span></p>
      <!-- <p class="information-title"><span>集群信息</span></p> -->
      <div class="colony-content">
        <img src="../../../assets/resource/jq.png" alt="">
        <!-- <div><span>CPU架构:</span><span></span><span>{{ state.framework }}</span></div> -->
        <div><span>物理机:</span><span></span><span>{{ state.physical }}</span></div>
        <div><span>虚拟机:</span><span></span><span>{{ state.vm }}</span></div>
        <div><span>物理CPU颗数:</span><span></span><span>{{ state.cpuCore }}</span></div>
        <div><span>物理CPU核数:</span><span></span><span>{{ state.cpuNucleus }}</span></div>
        <div><span>运行VCPU核数:</span><span></span><span>{{ state.cpuRun }}</span></div>
        <div><span>已分配VCPU核数:</span><span></span><span>{{ state.cpuUsed }}</span></div>
      </div>
    </div>
    <div class="config-information-area">
      <p class="information-title"><span>配置信息</span></p>
      <div class="config-content">
        <div class="general-content">
          <div class="general-img">
            <img src="../../../assets/resource/cpu.jpg" alt="">
            <div>
              <p><span>CPU总频率:</span><span>{{ hzConversion(state.cpuFrequencyTotal) }}</span></p>
              <p><span>已使用频率:</span><span>{{ hzConversion(state.cpuFrequencyUsed) }}</span></p>
            </div>
          </div>
          <div class="general-progress">
            <span>CPU使用率</span>
            <el-progress :percentage="state.cpuRate? state.cpuRate : 0"/>
          </div>
        </div>
        <div class="general-content">
          <div class="general-img">
            <img src="../../../assets/resource/nc.jpg" alt="">
            <div>
              <p><span>内存:</span><span>{{ capacityConversion(state.mem) }}</span></p>
              <p><span>已使用内存:</span><span>{{ capacityConversion(state.memUsed) }}</span></p>
              <p><span>已分配内存:</span><span>{{ capacityConversion(state.memAssigned) }}</span></p>
            </div>
          </div>
          <div class="general-progress">
            <span>内存使用率</span>
            <el-progress :percentage="state.memRate? state.memRate : 0"/>
          </div>
        </div>
        <div class="general-content">
          <div class="general-img">
            <img src="../../../assets/resource/cc.jpg" alt="">
            <div>
              <p><span>存储:</span><span>{{ capacityConversion(state.stor) }}</span></p>
              <p><span>已使用存储:</span><span>{{ capacityConversion(state.storUsed) }}</span></p>
              <p><span>已分配存储:</span><span>{{ capacityConversion(state.storAssigned) }}</span></p>
            </div>
          </div>
          <div class="general-progress">
            <span>存储使用率</span>
            <el-progress :percentage="state.storRate? state.storRate : 0"/>
          </div>
        </div>
      </div>
    </div>
    <div class="strategy-information-area">
      <p class="information-title"><span>策略信息</span></p>
    </div>
  </div>
</template>
<script setup lang="ts" name="ResourceSummary">
import { reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { dayjs } from 'element-plus';
import { colonyOverview } from '/@/api/ResourcePool'; // 接口
import { capacityConversion,hzConversion } from '/@/model/resource.ts'; // 表格 正则
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  framework: '-',
  physical: '-',
  vm: '-',
  cpuCore: '-',
  cpuNucleus: '-',
  cpuRun: '-',
  cpuUsed: '-',
  cpuFrequencyTotal: '-',
  cpuFrequencyUsed: '-',
  cpuRate: 0,
  mem: '-',
  memUsed: '-',
  memAssigned: '-',
  memRate: 0,
  stor: '-',
  storUsed: '-',
  storAssigned: '-',
  storRate: 0,
});
// 概要 数据
const summaryQuery=()=>{
  colonyOverview(props.treeItem.id).then(res=>{
    state.physical = res.data.host_count+' 台'
    state.vm = res.data.vm_count+' 台'
    state.cpuCore = res.data.cpu_all_count+ ' 颗'
    state.cpuNucleus = res.data.cpu_all_count+ ' 核'
    state.cpuRun =  '-'
    state.cpuUsed = res.data.cpu_allocation_count+ ' 核'
    
    
    // parseFloat((res.data.mem_use_count/res.data.mem_all_count*100).toFixed(1))
    state.cpuFrequencyUsed = res.data.cpu_use_hz
    state.cpuFrequencyTotal = res.data.cpu_all_hz
    state.cpuRate = parseFloat((res.data.cpu_use_hz/res.data.cpu_all_hz*100).toFixed(1))
    state.mem = res.data.mem_all_count
    state.memUsed = res.data.mem_use_count
    state.memAssigned = res.data.mem_allocation_count
    state.memRate = parseFloat((res.data.mem_use_count/res.data.mem_all_count*100).toFixed(1))
    state.stor = res.data.disk_all_count
    state.storUsed = res.data.disk_use_count
    state.storAssigned = res.data.disk_allocation_count
    state.storRate = parseFloat((res.data.disk_use_count/res.data.disk_all_count*100).toFixed(1))
  })
}
onMounted(() => {
  summaryQuery()
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    display: flex;
    justify-content: space-between;
    .colony-information-area {
      width: 510px;
		  height: calc(100%);
      border: 1px solid var(--el-card-border-color);
      border-radius: var(--el-card-border-radius);
      box-shadow: var(--el-box-shadow-light);
      .colony-content {
        img {
          width: 120px;
        }
        width: 100%;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        div {
          span {
            display: inline-block;
            width: 150px;
          }
          span:first-child {
            text-align: right;
          }
          span:nth-child(2) {
            width: 50px;
          }
        }
      }
    }
    .config-information-area {
      width: 650px;
		  height: calc(100%);
      border: 1px solid var(--el-card-border-color);
      border-radius: var(--el-card-border-radius);
      box-shadow: var(--el-box-shadow-light);
      .config-content {
        width: 650px;
        padding: 0 60px;
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
    }
    .strategy-information-area {
      width: 420px;
		  height: calc(100%);
      border: 1px solid var(--el-card-border-color);
      border-radius: var(--el-card-border-radius);
      box-shadow: var(--el-box-shadow-light);
    }
  }
  .information-title {
    height: 50px;
    display: flex;
    align-items: flex-end;
    span {
      display: inline-block;
      text-align: center;
      width: 100px;
      line-height: 30px;
		  background-image: url('/@/assets/resource/title.jpg');
      background-size: 100% 100%;
    }
  }
  .general-content {
    height: 130px;
    .general-img {
      display: flex;
      img {
        width: 120px;
      }
      div {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        p {
          span:nth-child(2) {
            margin-left: 15px;
          }
        }
      }
    }
    .general-progress {
      display: flex;
      justify-content: space-between;
      .el-progress {
        width: 440px;
      }
    }
  }
</style>
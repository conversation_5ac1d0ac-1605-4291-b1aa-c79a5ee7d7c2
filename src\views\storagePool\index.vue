<template>
	<div class="resource-area layout-padding">
		<el-card>
			<div class="resource-pool-container">
				<div class="resource-pool-tree">
					<el-card>
						<div class="pool-btn">
							<el-radio-group v-model="state.type" size="large">
								<el-radio-button label="接入" value="access" />
								<el-radio-button label="存储池" value="pool" />
							</el-radio-group>
						</div>
						<Access v-if="state.type == 'access'" @accessOK="accessOK"></Access>
						<Pool v-else @poolOK="poolOK"></Pool>
					</el-card>
				</div>
				<div class="resource-pool-tabs">
					<!-- 接入 -->
					<IPsan v-if="state.accessActve == 'IPsan'&&state.type == 'access'"></IPsan>
					<FCsan v-if="state.accessActve == 'FCsan'&&state.type == 'access'"></FCsan>
					<NAS v-if="state.accessActve == 'NAS'&&state.type == 'access'"></NAS>
					<NVME v-if="state.accessActve == 'NVME'&&state.type == 'access'"></NVME>
					<Distributed v-if="state.accessActve == 'distributed'&&state.type == 'access'"></Distributed>
					<!-- 存储池 -->
					<TablePool v-if="state.type == 'pool'" ref="poolRef"></TablePool>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts" name="ResourcePublic">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
const Access = defineAsyncComponent(() => import('./access/index.vue'));
const Pool = defineAsyncComponent(() => import('./pool/index.vue'));
// 引入组件 接入
const IPsan = defineAsyncComponent(() => import('./access/ipsan/index.vue'));
const FCsan = defineAsyncComponent(() => import('./access/fcsan/index.vue'));
const NAS = defineAsyncComponent(() => import('./access/nas/index.vue'));
const NVME = defineAsyncComponent(() => import('./access/nvme/index.vue'));
const Distributed = defineAsyncComponent(() => import('./access/distributed/index.vue'));
// 引入组件 存储池
const TablePool = defineAsyncComponent(() => import('./pool/tablePool/index.vue'));

// 定义变量内容
const state = reactive({
	type: 'access',
	accessActve: 'IPsan',
});
// 接入选择返回
const accessOK = (item: string) => {
	state.accessActve = item;
}
// 存储池选择返回
const poolRef = ref();
const poolOK = (item: any) => {
	poolRef.value.openDialog(item)
};
// 存储池传递数据
watch(
  ()=> state.type,
  (val)=>{
    if(val == 'access') {
		}else {
		}
  }
);
// 页面加载时
onMounted(() => {});
</script>

<style scoped lang="scss">
.resource-area {
	width: calc(100%);
	height: calc(100%);
	.resource-pool-container {
		padding-top: 0 !important;
		width: calc(100%);
		height: calc(100%);
		min-width: 1166px;
		min-height: 600px;
		position: relative;
		display: flex;
		.resource-pool-tree {
			width: 220px;
			height: 100%;
			margin-right: 20px;
			border-radius: 15px;
			padding: 5px;
			background: var(--el-fill-color-blank);
			.pool-btn {
				display: flex;
				justify-content: space-evenly;
				margin-bottom: 15px;
			}
		}
		.resource-pool-tabs {
			width: calc(100% - 240px);
			height: 100%;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 10px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}

</style>

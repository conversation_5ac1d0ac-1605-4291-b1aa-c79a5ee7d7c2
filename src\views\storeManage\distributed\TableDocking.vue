<template>
	<el-dialog v-model="formItem.isShow" width="600">
		<template #header="{ close, titleId, titleClass }">
			<span class="el-dialog__title">{{ formItem.title === 'new'?'接入':'修改' }} 分布式存储设备</span>
		</template>
    <el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储设备名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入端口组名称" />
			</el-form-item>
      <el-form-item label="存储 I P" prop="ip">
				<el-input v-model="formItem.ip" placeholder="请输入存储 IP" />
			</el-form-item>
      <el-form-item label="存储端口" prop="port">
        <el-input v-model="formItem.port" :min="1" type="number" placeholder="请输入端口" />
			</el-form-item>
			<el-form-item label="接入认证">
				<el-checkbox v-model="formItem.share" label="启用认证" />
			</el-form-item>
      <el-form-item label="用户" prop="user" v-if="formItem.share">
				<el-input v-model="formItem.user" placeholder="请输入用户" />
			</el-form-item>
      <el-form-item label="密码" prop="password" v-if="formItem.share">
        <el-input v-model="formItem.password" show-password placeholder="请输入密码" />
			</el-form-item>
      <el-form-item label="关联主机" v-if="formItem.title === 'new'">
				已选 （ {{ formItem.selectNodes.length }} ）
			</el-form-item>
		</el-form>
		<ZtreePublick v-if="formItem.title === 'new'" :type='formItem.type' :zNodes='formItem.zNodes'></ZtreePublick>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { portGroupEdit } from '/@/api/Network'; // 接口
import { propName,propIP,propPort } from '/@/model/network.ts'; // 表列、正则
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	title: '',
  name: '',
  ip: '',
  port: '3260',
  share: false,
  user: '',
  password: '',
  selectNodes: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'blur' },
	],
	ip: [
    { required: true, message: '必填项', trigger: 'blur' },
		{ validator: propIP, trigger: 'blur' },
  ],
	port: [
    { required: true, message: '必填项', trigger: 'blur' },
		{ validator: propPort, trigger: 'blur' }
  ],
	user: [{ required: true, message: '必选项', trigger: 'blur' }],
	password: [{ required: true, message: '必选项', trigger: 'blur' }],
});
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0' },
		{ id: '2', name: '主机池1', pid: '1' },
		{ id: '3', name: '集群1', pid: '2' },
		{ id: '4', name: '主机1', pid: '3' },
		{ id: '5', name: '主机2', pid: '3' },
		{ id: '6', name: '主机3', pid: '3' },
		{ id: '7', name: 'vm1', pid: '4' },
		{ id: '8', name: 'vm2', pid: '4' },
		{ id: '9', name: 'vm3', pid: '5' },
		{ id: '11', name: 'vm4', pid: '5' },
		{ id: '12', name: 'vm5', pid: '5' },
		{ id: '13', name: 'vm3', pid: '5' },
		{ id: '14', name: 'vm3', pid: '5' },
		{ id: '31', name: 'aaaaa', pid: '5' },
		{ id: '32', name: 'vm4', pid: '5' },
		{ id: '63', name: 'vm2', pid: '5' },
	];
};
// 打开弹窗
const openDialog = async (type: string,row: any) => {
	formItem.isShow = true;
	formItem.title = type
	nextTick(() => {
		if(type == 'new') {
			if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
				ruleFormRef.value.resetFields();
			}
			setTimeout(() => {
				formItem.type = '分布式存储-接入'
				treeData()
			}, 200);
		}else {
			formItem.name = row.name
			formItem.ip = row.ip.split(':')[0]
			formItem.port = row.ip.split(':')[1]
			formItem.share = row.share
		}
	});
};

const emit = defineEmits(['returnOK']);
const confirm = () => {
  if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        // formItem.isShow = false;
        // userNew({
				// 	username: formItem.username,
				// 	password: formItem.password,
        //   name: formItem.name,
				// 	role: formItem.role,
				// 	expiredday: timeFormat(formItem.time),
				// }).then((res) => {
				// 	if(res.msg == 'ok') {
        // 		ElMessage.success('新建用户操作完成');
				// 		emit('returnOK', 'refresh');
				// 	}else {
        // 		ElMessage.error(res.msg);
				// 	}
				// });
      }
    })
  }
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">

</style>
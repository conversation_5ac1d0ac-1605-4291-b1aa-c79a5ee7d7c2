<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    width="500"
  >
    <div>
      <span>是否删除下列{{ props.deleteTime.split('/')[0] }}？</span>
      <p style="color:red;word-wrap: break-word">{{ props.names.toString() }}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="TableDelete">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';

const props = defineProps({
  names: {
    type: Array,
    required: true
  },
  deleteTime: {
    type: String,
    required: true
  }
});
const formItem = reactive({
  isShow: false,
  title: '删除',
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  emit('returnOK', 'delete');
  formItem.isShow = false;
}
watch(
  ()=> props.deleteTime,
  (val)=>{
    formItem.isShow = true;
    formItem.title = '删除'+val.split('/')[0]
  }
);
</script>
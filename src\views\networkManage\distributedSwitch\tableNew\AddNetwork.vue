<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<div class="prompt-area">
			<p><el-icon color="#fe944d"><WarningFilled /></el-icon>交换机VLAN ID 用于支持多台宿主机之间的通信。若交换机已用于存储网络或集群HA心跳网络，则不允许修改“VLAN ID、类型、移除宿主机或网卡、修改IP、掩码、链路聚合模式”，请先调整存储网络或关闭集群HA，再调整交换机相关配置。</p>
		</div>
		<!-- <el-form-item label="交换机VLAN ID" prop="vlanID">
      <el-input v-model="formItem.vlanID" type="number" placeholder="有效值1-4096"/>
		</el-form-item> -->
		<el-form-item label="网络类型" prop="type">
			<el-select v-model="formItem.type" style="width: 100%">
				<el-option label="业务网络" value="yw" />
				<el-option label="管理网络" value="gl" />
				<el-option label="存储网络" value="cc" />
			</el-select>
		</el-form-item>
		<el-form-item label="网络配置">
		</el-form-item>
		<div class="net-config-area">
			<my-table
				ref="tableRef"
				:pagination="formItem.pagination"
				:columns="formItem.columns"
				:request="getTableData"
				v-if="formItem.show"
			>
				<!-- 网卡名称 -->
        <template #netName="{ row }">
          <span>
						{{ names(row.netName) }}
					</span>
        </template>
				<!-- 链路聚合模式 -->
				<template #link="{ row }">
          <el-select v-model="row.link" style="width: 100%">
						<el-option label="静态" value="jt" />
						<el-option label="动态" value="dt" />
					</el-select>
        </template>
				<!-- 负载模式 -->
				<template #load="{ row }">
          <el-select v-model="row.load" style="width: 100%">
						<el-option label="主备负载" value="jt" />
						<el-option label="基本负载" value="dt" />
						<el-option label="高级负载分组" value="dt" />
					</el-select>
        </template>
				<!-- IP -->
				<template #ip="{ row }">
          <el-input v-model="row.ip" placeholder="请输入"/>
        </template>
				<!-- 子网掩码 -->
				<template #mask="{ row }">
          <el-input v-model="row.mask" placeholder="请输入"/>
        </template>
			</my-table>
		</div>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { propVlan } from '/@/model/network.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
	besicData: {
		type: Object,
		required: true,
	}
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['netOK']);
const formItem = reactive({
	vlanID: '1',
	type: 'yw',
	show: true,
	columns: [
		{ label: '物理机名称', prop: 'hostName' },
		{ label: '网卡名称', tdSlot: 'netName', align: 'center' },
		{ label: '链路聚合模式', tdSlot: 'link', align: 'center' },
		// { label: '负载模式', tdSlot: 'load', align: 'center' },
		// { label: 'IP', tdSlot: 'ip', align: 'center' },
		// { label: '子网掩码', tdSlot: 'mask', align: 'center' },
	], // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	tableData:[{
		hostName: '虚拟交换机',
		netName: [{name:'网卡1'}],
		link: '',
		load: '',
		ip: '',
		mask: ''
	}],
});
const rulesForm = reactive<FormRules>({
	vlanID: [
    { required: true, message: '必填项' },
    { validator: propVlan, trigger: "blur" }
  ],
  type: [{ required: true, message: '必选项', trigger: 'blur' }],
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async(resolve)=>{
    if(true) {
      return resolve({
				data: formItem.tableData, // 数据
				total: formItem.tableData.length, // 总数
			});
    }
	})
}
// 拆分网卡名
const names=(row:any)=>{
	return row.map((item:any)=>{
		return item.name
	}).toString()
}
watch(
	() => formItem.type,
	(val) => {
		if(val == 'yw') {
			formItem.columns = [
				{ label: '物理机名称', prop: 'hostName' },
				{ label: '网卡名称', tdSlot: 'netName', align: 'center' },
				{ label: '链路聚合模式', tdSlot: 'link', align: 'center' },
			]
		}else {
			formItem.columns = [
				{ label: '物理机名称', prop: 'hostName' },
				{ label: '网卡名称', tdSlot: 'netName', align: 'center' },
				{ label: '链路聚合模式', tdSlot: 'link', align: 'center' },
				{ label: 'IP', tdSlot: 'ip', align: 'center' },
				{ label: '子网掩码', tdSlot: 'mask', align: 'center' },
			]
		}
		formItem.show = false
		setTimeout(() => {
			formItem.show = true
		}, 50);
	}
);
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
watch(
	() => props.besicData.treeTable,
	(val) => {
		formItem.tableData = props.besicData.treeTable
		refresh()
	}
);
watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('netOK', formItem);
				}
			});
		}
	}
);
</script>
<style lang="scss" scoped>
.prompt-area {
  margin-bottom: 20px;
  p {
    color: #ccc;
    .el-icon {
      margin-right: 5px;
    }
  }
}
.net-config-area {
	width: 100%;
	height: 300px;
	position: relative;
}
</style>

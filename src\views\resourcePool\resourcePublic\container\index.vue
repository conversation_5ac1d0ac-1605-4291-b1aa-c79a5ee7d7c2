<template>
	<div class="resource-pool-container">
		<div class="tabs-btn-area">
			<div>
				<el-button type="primary" plain @click="refresh">刷新</el-button>
				<el-button type="primary" plain @click="newClick">新建容器</el-button>
				<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
			</div>
			<div>
				<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
					<template #append>
						<el-button :icon="Search" @click="refresh"></el-button>
					</template>
				</el-input>
			</div>
		</div>
		<div class="tabs-table-area">
			<my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
				@selectionChange="selectChange"
			>
				<!-- 容器名称 -->
				<!-- <template #name="{ row }">
              <el-tag :type="row.status == 'enabled'?'success':'info'">{{row.status == 'enabled'?'启用':'禁用'}}</el-tag>
						</template> -->
				<!-- 内存 -->
				<template #memory="{ row }">
					<span>{{ (row.memory / 1024).toFixed(1) }} GB</span>
				</template>
				<!-- 硬盘 -->
				<template #disk="{ row }">
					<span>{{ row.disk }} GB</span>
				</template>
				<!-- IP地址 -->
				<template #ip="{ row }">
					<span>{{ row.ip.toString() }} </span>
				</template>
				<!-- 状态 -->
				<template #status="{ row }">
					<span>{{ statusConvert(row.status) }}</span>
				</template>
				<!-- 任务 -->
				<template #task_state="{ row }">
					<!-- <div v-if="row.task_state!=='None'" class="table_schedule">
						{{ convertTask(row.task_state) }}
					</div>
        	<span v-else>无</span> -->
        	<span>无</span>
				</template>
				<!-- 操作 -->
				<template #operation="{ row }">
					<el-dropdown trigger="click" @command="commandItem($event, row)">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="bj" v-if="row.status!=='Error'">编辑</el-dropdown-item>
								<!-- <el-dropdown-item command="aqz">管理安全组</el-dropdown-item> -->
								<el-dropdown-item command="qd" v-if="row.status=='Stopped'||row.status=='Error'">启动</el-dropdown-item>
								<el-dropdown-item command="tz" v-if="row.status=='Running'">停止</el-dropdown-item>
								<el-dropdown-item command="zt" v-if="row.status=='Running'">暂停</el-dropdown-item>
								<el-dropdown-item command="hf" v-if="row.status!=='Paused'">恢复</el-dropdown-item>
								<el-dropdown-item command="cq" v-if="row.status!=='Paused'">重启</el-dropdown-item>
								<el-dropdown-item command="cj" v-if="row.status!=='Paused'">重建</el-dropdown-item>
								<el-dropdown-item command="zx" v-if="row.status=='Running'">执行命令</el-dropdown-item>
								<el-dropdown-item command="sw" v-if="row.status=='Running'">发送终止信号</el-dropdown-item>
								<el-dropdown-item command="sc" v-if="row.status!=='Stopped'" style="color: red" divided>删除</el-dropdown-item>
								<!-- <el-dropdown-item command="sc" v-if="row.status!=='Paused'" style="color: red">停止并删除</el-dropdown-item>
								<el-dropdown-item command="sc" v-if="row.status!=='Paused'" style="color: red">强制删除</el-dropdown-item> -->
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</my-table>
		</div>
		<TableNew ref="newRef" @returnOK="returnOK" />
    <TableEdit ref="editRef" @returnOK="returnOK" />
		<TableGeneral ref="generalRef" @returnOK="returnOK" />
		<TableRestart ref="restartRef" @returnOK="returnOK" />
		<TableReconstruct ref="rebuildRef" @returnOK="returnOK" />
		<TableCommand ref="commandRef" />
		<TableSignal ref="signalRef" @returnOK="returnOK" />
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK" />
	</div>
</template>
<script setup lang="ts" name="ClusterAlarm">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { containerColumns, statusConvert } from '/@/model/vm.ts'; // 表列、正则
import { containerQuery,containerDeleted } from '/@/api/ResourcePool/container'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./tableNew/index.vue'));
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'));
const TableGeneral = defineAsyncComponent(() => import('./TableGeneral.vue'));
const TableRestart = defineAsyncComponent(() => import('./TableRestart.vue'));
const TableReconstruct = defineAsyncComponent(() => import('./TableReconstruct.vue'));
const TableCommand = defineAsyncComponent(() => import('./TableCommand.vue'));
const TableSignal = defineAsyncComponent(() => import('./TableSignal.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
// 定义变量内容
const state = reactive({
	columns: containerColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	deleteTime: '',
});

interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = []
  if(true){
		let list = [
			{ id:'1', name: '容器1',image: '-',cpu:'1',memory: 3021,ip:['***********'],status:'Creating' },
			{ id:'2', name: '容器2',image: '-',cpu:'2',memory: 6024,ip:['***********','***********'],status:'Created' },
			{ id:'3', name: '容器3',image: '-',cpu:'3',memory: 7024,ip:['***********','***********'],status:'Running' },
			{ id:'4', name: '容器4',image: '-',cpu:'4',memory: 1024,ip:['***********','***********','***********','***********'],status:'Stopped' },
			{ id:'5', name: '容器5',image: '-',cpu:'5',memory: 2024,ip:['-'],status:'Restarting' },
			{ id:'6', name: '容器6',image: '-',cpu:'6',memory: 31024,ip:['-'],status:'Paused' },
			{ id:'7', name: '容器7',image: '-',cpu:'7',memory: 7999,ip:['-'],status:'-' },
		]
		return {
			data: list, // 数据
			total: list.length, // 总数
		};
  }
	return new Promise(async (resolve) => {
		containerQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页显示条数
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};

const editRef = ref(); //  编辑
const generalRef = ref(); //  通用
const restartRef = ref(); //  重启
const rebuildRef = ref(); //  重建
const commandRef = ref(); //  执行命令
const signalRef = ref(); //  发送死亡信号
// 表操作列
const commandItem = (event: string, row: never) => {
	switch (event) {
		case 'bj':
			editRef.value.openDialog(row);
			break;
		case 'qd':
			generalRef.value.openDialog({name:'启动',action:'start'},[row]);
			break;
		case 'tz':
			generalRef.value.openDialog({name:'停止',action:'stop'},[row]);
			break;
		case 'zt':
			generalRef.value.openDialog({name:'暂停',action:'pause'},[row]);
			break;
		case 'hf':
			generalRef.value.openDialog({name:'恢复',action:'unpause'},[row]);
			break;
		case 'cq':
			restartRef.value.openDialog(row);
			break;
		case 'cj':
			rebuildRef.value.openDialog(row);
			break;
		case 'zx':
			commandRef.value.openDialog(row);
			break;
		case 'sw':
			signalRef.value.openDialog(row);
			break;
		case 'sc':
			deleteClick([row]);
			break;
	}
};
// 新建
const newRef = ref();
const newClick = () => {
	newRef.value.openDialog();
};
// 删除
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '容器/' + new Date();
	}
};
// 返回数据
const returnOK = (item: any) => {
	if (item == 'delete') {
		containerDeleted({
			names: formDelet.tableNames,
			ids: formDelet.tableIDs,
		}).then((res) => {
			if (res.msg == 'ok') {
				refresh();
				ElMessage.success('删除容器操作完成');
			} else {
				ElMessage.error('删除容器操作失败');
			}
		});
	} else {
		refresh();
	}
};
onMounted(() => {});
</script>
<style scoped lang="scss">
.resource-pool-container {
	width: calc(100%);
	height: calc(100%);
	.tabs-btn-area {
		height: 40px;
		display: flex;
		justify-content: space-between;
	}
	.tabs-table-area {
		width: calc(100%);
		height: calc(100% - 50px);
		position: relative;
	}
}
</style>
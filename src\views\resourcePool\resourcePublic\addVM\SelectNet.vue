<template>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    title="选择网卡"
  >
    <div class="storage-pool-dialog">
      <div class="torage-pool-tree">
        <ul>
          <li :class="state.poolID== item.id?'exdisk_bg':''" v-for="item in state.poolData" :key="item.id" @click="existingClick(item)">
            <h5>{{item.name}}</h5>
            <!-- <h4>{{item.host}}</h4> -->
            <h3>{{ item.status }}</h3>
            <p> <span class="existing_totle">状态</span></p>
          </li>
        </ul>
      </div>
      <div class="storage-pool-table">
        <el-input v-model="state.tableSearch" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
        <my-table
          ref="tableRef"
          :pagination="state.pagination"
          :columns="state.columns"
          :request="getTableData"
        >
          <!-- 单选 -->
          <template #radio="{ row }">
            <el-radio-group v-model="state.tableID" @change="radioClick(row)">
              <el-radio :value="row.id"></el-radio>
            </el-radio-group>
          </template>
          <!-- 实际使用量 -->
					<template #allocation="{ row }">
						<span>{{ row.allocation }}</span>
					</template>
        </my-table>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="SelectNet">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { netSwitchQuery,portGroupQuery } from '/@/api/Network'; // 接口
import { portGroupColumns } from '/@/model/network.ts'; // 表列、正则

import { Search } from '@element-plus/icons-vue'
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  netDialog: {
    type: String,
    required: true
  }
});
interface poolList {
  isShow: boolean;
  columns: Array<any>;
  pagination: {
    show: boolean;
  };
  poolData: Array<any>;
  poolID: string;
  tableID: string;
  poolName: string;
  tableName: string;
  tableSearch: string;
}
const state = reactive<poolList>({
  isShow: false,
  columns: [
    { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
    { label: '端口组名称', prop: 'name', sortable: true, align: 'center', wrap: true },
    { label: '端口数', prop: 'pool', align: 'center' },
    { label: 'VLAN', prop: 'vlan_id', align: 'center' },
  ],
  pagination: {
		show: true,
	}, // 是否显示分页
  poolData: [],
  poolID: '',
  poolName: '',
  tableID: '',
  tableName: '',
  tableSearch: '',
});
// 查询交换机
const switchData = () => {
  netSwitchQuery({
    host_id: props.treeItem.id,
    page: 1,
    pagecount: 10,
    search_str: '',
    order_type: 'desc',
    order_by: '',
  }).then((res: any) => {
    state.poolData = res.data;
    state.poolID = res.data[0].id;
    state.poolName = res.data[0].name;
    refresh()
  })
}
// 点击交换机
const existingClick =(item:any)=>{
  state.poolID = item.id
  state.poolName = item.name
  refresh()
}
const getTableData = ( params: EmptyObjectType,page: EmptyObjectType) => {
  return new Promise(async(resolve)=>{
    portGroupQuery({
      id: state.poolID, // 交换机ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      state.tableID = res.total*1>0?res.data[0].id:''
      state.tableName = res.total*1>0?res.data[0].name:''
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {})
    
  })
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  // tableRef.value.handleSearch(); // 收索事件 表1页
  tableRef.value.refresh(); // 刷新事件 表当前
}
// 单选磁盘
const radioClick=(row:any)=>{
  state.tableID = row.id
  state.tableName = row.name
}
const emit = defineEmits(['netReturn']);
const confirm =()=>{
  state.isShow= false
  emit('netReturn', {
    poolName:state.poolName,
    tableName:state.tableName,
  });
}
watch(
  ()=> props.netDialog,
  (val)=>{
    state.isShow = true;
    switchData()
  }
);
</script>
<style lang="scss" scoped>
  .storage-pool-dialog {
    height: 650px;
    display: flex;
    .torage-pool-tree {
      width: 150px;
      height: 650px;
      padding-right: 10px;
      overflow: auto;
      ul {
        padding: 0; 
        margin: 0;
        list-style: none;
        li {
          padding: 5px;
          margin-bottom: 5px;
          width: 100%;
          height: 100px;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;
          cursor: pointer;
          h5 {
            text-align: center;
            color: rgb(209, 243, 209);
            width: 100%;
            white-space:nowrap; // 强制一行显示
            overflow:hidden; // 超出隐藏
            text-overflow:ellipsis; // 省略号
          }
          .existing_totle {
            color: #ccc;
          }
        }
      }
    }
    .storage-pool-table {
      position: relative;
      height: 600px;
      width: calc(100% - 150px);
    }
  }
  .exdisk_bg {
    background: #da4c18;
    color: #fff;
  }
</style>
<template>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    title="选择网卡"
    destroy-on-close
    :close-on-click-modal="false"
    class="dialog-1000"
  >
    <div class="network-selection-container">
      <!-- 左侧交换机列表 -->
      <div class="switch-list-panel">
        <div class="panel-header">
          <el-icon><Connection /></el-icon>
          <span>交换机列表</span>
        </div>
        <div class="switch-items">
          <div
            v-for="item in state.poolData"
            :key="item.id"
            class="switch-item"
            :class="{ 'active': state.poolID === item.id }"
            @click="existingClick(item)"
          >
            <div class="switch-icon">
              <el-icon class="switch-button-icon"><SwitchButton /></el-icon>
            </div>
            <div class="switch-info">
              <div class="switch-name" :title="item.name">{{ item.name }}</div>
              <div class="switch-status">
                <el-tag
                  :type="getStatusType(item.status)"
                  size="small"
                  effect="light"
                >
                  {{ item.status }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧端口组表格 -->
      <div class="port-group-panel">
        <div class="search-bar">
          <el-input
            v-model="state.tableSearch"
            placeholder="搜索端口组"
            clearable
            prefix-icon="Search"
            @clear="refresh"
            @keyup.enter="refresh"
          >
            <template #append>
              <el-button :icon="Search" @click="refresh"></el-button>
            </template>
          </el-input>
        </div>

        <div class="table-container">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            row-key="id"
            highlight-current-row
          >
            <!-- 单选 -->
            <template #radio="{ row }">
              <el-radio-group v-model="state.tableID" @change="radioClick(row)">
                <el-radio :value="row.id"></el-radio>
              </el-radio-group>
            </template>
            <!-- VLAN -->
            <template #vlan_id="{ row }">
              <el-tag type="info" effect="plain">{{ row.vlan_id }}</el-tag>
            </template>
          </my-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm" :disabled="!state.tableID">确认选择</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="SelectNet">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { localSwitchQuery, portGroupQuery } from '/@/api/Network'; // 接口
import { portGroupColumns } from '/@/model/network.ts'; // 表列、正则

import { Search, SwitchButton } from '@element-plus/icons-vue'
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  netDialog: {
    type: String,
    required: true
  }
});
interface poolList {
  isShow: boolean;
  columns: Array<any>;
  pagination: {
    show: boolean;
  };
  poolData: Array<any>;
  poolID: string;
  tableID: string;
  poolName: string;
  tableName: string;
  tableSearch: string;
}
// 获取状态类型
const getStatusType = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'online':
    case '在线':
    case 'active':
    case '活动':
      return 'success';
    case 'offline':
    case '离线':
      return 'info';
    case 'warning':
    case '警告':
      return 'warning';
    case 'error':
    case '错误':
      return 'danger';
    default:
      return 'info';
  }
};

const state = reactive<poolList>({
  isShow: false,
  columns: [
    { label: '选择', width: 80, tdSlot: 'radio', align: 'center' },
    { label: '端口组名称', prop: 'name', tdSlot: 'name', sortable: true, align: 'left', minWidth: 180 },
    { label: '端口数', prop: 'pool', align: 'center', minWidth: 100 },
    { label: 'VLAN ID', prop: 'vlan_id', tdSlot: 'vlan_id', align: 'center', minWidth: 120 },
  ],
  pagination: {
    show: true,
  }, // 是否显示分页
  poolData: [],
  poolID: '',
  poolName: '',
  tableID: '',
  tableName: '',
  tableSearch: '',
});
// 查询交换机
const switchData = () => {
  localSwitchQuery({
    host_id: props.treeItem.id,
    page: 1,
    pagecount: 10,
    search_str: '',
    order_type: 'desc',
    order_by: '',
  }).then((res: any) => {
    state.poolData = res.data;
    state.poolID = res.data[0].id;
    state.poolName = res.data[0].name;
    refresh()
  })
}
// 点击交换机
const existingClick =(item:any)=>{
  state.poolID = item.id
  state.poolName = item.name
  refresh()
}
const getTableData = ( params: EmptyObjectType,page: EmptyObjectType) => {
  return new Promise(async(resolve)=>{
    portGroupQuery({
      id: state.poolID, // 交换机ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      state.tableID = res.total*1>0?res.data[0].id:''
      state.tableName = res.total*1>0?res.data[0].name:''
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {})

  })
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  // tableRef.value.handleSearch(); // 收索事件 表1页
  tableRef.value.refresh(); // 刷新事件 表当前
}
// 单选磁盘
const radioClick=(row:any)=>{
  state.tableID = row.id
  state.tableName = row.name
}
const emit = defineEmits(['netReturn']);
const confirm =()=>{
  state.isShow= false
  emit('netReturn', {
    tableID:state.tableID,
    tableName:state.tableName,
    switchID:state.poolID
  });
}
watch(
  ()=> props.netDialog,
  ()=>{
    state.isShow = true;
    switchData()
  }
);
</script>
<style lang="scss" scoped>
.network-selection-container {
  display: flex;
  height: 600px;
  border-bottom: 1px solid #ebeef5;
}

.switch-list-panel {
  width: 220px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  background-color: #f9fafc;

  .panel-header {
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #f2f6fc;
  }

  .switch-items {
    flex: 1;
    overflow-y: auto;
    padding: 12px;

    .switch-item {
      display: flex;
      align-items: center;
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid #ebeef5;
      background-color: white;

      &:hover {
        background-color: #ecf5ff;
        transform: translateY(-2px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      }

      &.active {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
        box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.3);

        .switch-name {
          color: white;
          font-weight: 600;
        }
        .switch-icon {
          background-color: #8c8c8c;
        }
        .switch-status .el-tag {
          background-color: rgba(255, 255, 255, 0.2);
          border-color: transparent;
          color: white;
        }
      }

      .switch-icon {
        font-size: 24px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(64, 158, 255, 0.1);

        .switch-button-icon {
          color: #40d9ff;
        }
        .el-icon {
          color: #ffb440;
        }
        .active & {
          background-color: rgba(255, 255, 255, 0.3);
          .switch-button-icon {
            color: #ffffff;
          }
        }
      }

      .switch-info {
        flex: 1;
        min-width: 0;

        .switch-name {
          font-weight: 500;
          margin-bottom: 6px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.port-group-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 15px;

  .search-bar {
    margin-bottom: 16px;

    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px #dcdfe6 inset;
    }

    :deep(.el-input__inner) {
      height: 40px;
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
}

.dialog-footer {
  padding: 16px 20px;
  text-align: right;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;

  .el-table__header th {
    font-weight: 600;
    color: #606266;
  }

  .el-table__row {
    cursor: default;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }

    &.current-row {
      background-color: #ecf5ff;
    }

    .el-radio {
      cursor: pointer;
    }
  }
}

:deep(.el-radio) {
  margin-right: 0;

  .el-radio__inner {
    width: 18px;
    height: 18px;

    &::after {
      width: 8px;
      height: 8px;
    }
  }

  .el-radio__label {
    display: none;
  }
}

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-table__row.current-row) {
  background-color: #ecf5ff;

  td {
    background-color: #ecf5ff !important;
  }
}
</style>
<template>
  <div class="store-area">
      <div class="page-btn-area">
         <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh">刷新</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
          </div>
          <div>
            <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<template #vlanid="{ row }">
              <span>{{ row.vlanid?row.vlanid:'-' }}</span>
						</template>
            <!-- 关联主机数 -->
						<template #hosts="{ row }">
              <el-tooltip effect="dark" :disabled="row.hosts==0">
                <template #content>
                  <div class="tooltip-table-area">
                    <el-table :data="row.hosts" >
                      <el-table-column prop="cluster_name" label="集群" />
                      <el-table-column prop="hostname" label="宿主机" />
                      <el-table-column prop="ip" label="主机IP" />
                    </el-table>
                  </div>
                </template>
                <span>{{ row.hosts?.length }}</span>
              </el-tooltip>
						</template>
            <!-- 存储资源 -->
						<template #targets="{ row }">
              <el-tooltip effect="dark" :disabled="row.targets==0">
                <template #content>
                  <div class="tooltip-table-area">
                    <el-table :data="row.targets" >
                      <el-table-column prop="target_name" label="IQN名称" />
                    </el-table>
                  </div>
                </template>
                <span>{{ row.targets?.length }}</span>
              </el-tooltip>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-dropdown trigger="click" @command="commandItem($event,row)">
                <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="bj">编辑</el-dropdown-item>
                    <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
						</template>
          </my-table>
        </div>
      </div>
		<TableEdit ref="editRef" @returnOK="returnOK"></TableEdit>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';
import { distributedColumns } from '/@/model/storeManage.ts'; // 表列、正则
import { distributedTableQuery,distributedTableDelete } from '/@/api/StoreManage'; // 接口
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

// 定义变量内容
const state = reactive({
  columns: distributedColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(!true){
    let list = ['aaa','bbb','ccc','ddd','eee','fff']
		return {
			data: [
        {name: list[Math.round(Math.random() * 5)],ip:'***********:3206',share:false,id:'aa1'},
        {name: list[Math.round(Math.random() * 5)],ip:'**********:3206',share:true,id:'aa2'}
      ], // 数据
			total: 2, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    distributedTableQuery({
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
const editRef = ref();
// 表操作列
const commandItem = (item: string,row:any)=>{
  switch (item) {
    case 'bj':
      editRef.value.editDialog(row)
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '存储资源/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    distributedTableDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        setTimeout(()=>{
          refresh()
          ElMessage.success('删除操作完成');
        },1000)
      }else {
        ElMessage.error('删除操作失败');
      }
    })
  }else {
    refresh()
  }
}
const openDialog = async (row: any) => {
  console.log('接入传递',row)
  nextTick(() => {
		if(tableRef.value){
			refresh();
		}
  })
}
// 暴露变量
defineExpose({
	openDialog,
});
onMounted(() => {
})
</script>
<style scoped lang="scss">
.store-area {
  padding-top: 15px;
	width: calc(100%);
	height: calc(100%);
  .page-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.tooltip-table-area {
  width: 450px;
}
</style>
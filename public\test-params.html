<!DOCTYPE html>
<html>
<head>
    <title>测试参数传递</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
            border: 1px solid #ddd;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 150px;
        }
        label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试 window.open 参数传递</h1>
        
        <div class="section">
            <h3>📋 参数设置</h3>
            <p>设置要传递给新页面的参数：</p>
            
            <div>
                <label for="listInput">List:</label>
                <input type="text" id="listInput" value="************" placeholder="输入list值">
            </div>
            
            <div>
                <label for="portInput">Port:</label>
                <input type="text" id="portInput" value="5900" placeholder="输入port值">
            </div>
            
            <button onclick="openSpiceWithParams()">打开SPICE页面</button>
            <button onclick="openSpiceWithAutoConnect()">打开并自动连接</button>
        </div>

        <div class="section">
            <h3>🔗 生成的URL示例</h3>
            <div id="urlPreview" class="code">
                点击按钮查看生成的URL
            </div>
        </div>

        <div class="section">
            <h3>📊 操作日志</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h3>📝 代码示例</h3>
            <div class="code">
// Vue组件中的用法：
const consoleClick = () => {
  let list = '************'
  let port = '5900'
  
  // 构建带参数的URL
  const spiceUrl = `/spice-html5/spice.html?list=${list}&port=${port}&timestamp=${Date.now()}`
  
  // 打开新页面并传递参数
  window.open(spiceUrl, '_blank')
}

// spice.html中接收参数：
const params = getUrlParams();
let website = params.list;  // 接收list参数
let webport = params.port;  // 接收port参数
            </div>
        </div>
    </div>

    <script>
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function updateUrlPreview() {
            const list = document.getElementById('listInput').value;
            const port = document.getElementById('portInput').value;
            const url = `/spice-html5/spice.html?list=${list}&port=${port}&timestamp=${Date.now()}`;
            document.getElementById('urlPreview').textContent = url;
        }
        
        function openSpiceWithParams() {
            const list = document.getElementById('listInput').value;
            const port = document.getElementById('portInput').value;
            
            if (!list || !port) {
                alert('请输入list和port参数');
                return;
            }
            
            // 构建带参数的URL
            const spiceUrl = `/spice-html5/spice.html?list=${list}&port=${port}&timestamp=${Date.now()}`;
            
            addLog(`🚀 打开SPICE页面，传递参数: list=${list}, port=${port}`);
            addLog(`🔗 URL: ${spiceUrl}`);
            
            // 打开新页面
            const newWindow = window.open(spiceUrl, '_blank');
            
            if (newWindow) {
                addLog('✅ 新页面已打开');
            } else {
                addLog('❌ 无法打开新页面，可能被浏览器阻止');
            }
            
            updateUrlPreview();
        }
        
        function openSpiceWithAutoConnect() {
            const list = document.getElementById('listInput').value;
            const port = document.getElementById('portInput').value;
            
            if (!list || !port) {
                alert('请输入list和port参数');
                return;
            }
            
            // 构建带自动连接参数的URL
            const spiceUrl = `/spice-html5/spice.html?list=${list}&port=${port}&autoconnect=1&timestamp=${Date.now()}`;
            
            addLog(`🚀 打开SPICE页面并自动连接，传递参数: list=${list}, port=${port}`);
            addLog(`🔗 URL: ${spiceUrl}`);
            
            // 打开新页面
            const newWindow = window.open(spiceUrl, '_blank');
            
            if (newWindow) {
                addLog('✅ 新页面已打开，将自动连接');
            } else {
                addLog('❌ 无法打开新页面，可能被浏览器阻止');
            }
            
            updateUrlPreview();
        }
        
        // 监听输入框变化，实时更新URL预览
        document.getElementById('listInput').addEventListener('input', updateUrlPreview);
        document.getElementById('portInput').addEventListener('input', updateUrlPreview);
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', function() {
            addLog('📄 测试页面加载完成');
            updateUrlPreview();
        });
    </script>
</body>
</html>

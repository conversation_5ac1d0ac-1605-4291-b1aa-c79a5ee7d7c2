<template>
  <div class="resource-pool-container">
    <div class="left-information-area">
      <div class="colony-information-area">
        <p class="information-title"  @click="summaryQuery"><span>集群信息</span></p>
        <!-- <p class="information-title"><span>集群信息</span></p> -->
        <div class="colony-content">
          <div><span>UUID:</span><span></span><span>{{ state.uuid }}</span></div>
          <div><span>虚拟机状态:</span><span></span><span>{{ state.status }} 台</span></div>
          <div><span>物理机IP:</span><span></span><span>{{ state.ip }} 台</span></div>
          <div><span>控制台类型:</span><span></span><span>{{ state.type }}</span></div>
          <div><span>VNC/SPICE端口号:</span><span></span><span>{{ state.port }}</span></div>
          <div><span>远程控制台:</span><span></span><span style="cursor:pointer;color:green" @click="consoleClick"> 控制台 </span></div>
        </div>
        <div class="terminal-area">
          <!-- 连接类型选择 -->
          <div class="console-controls">
            <el-radio-group v-model="state.consoleType" @change="handleConsoleTypeChange" size="small">
              <el-radio-button value="vnc">VNC控制台</el-radio-button>
              <el-radio-button value="spice">SPICE控制台</el-radio-button>
            </el-radio-group>
            <div class="control-buttons">
              <el-button size="small" type="primary" @click="connectConsole" :loading="state.connecting">
                {{ state.connecting ? '连接中...' : '连接' }}
              </el-button>
              <el-button size="small" @click="disconnectConsole" :disabled="!state.connected">
                断开连接
              </el-button>
              <el-button size="small" @click="refreshConsole" :disabled="!state.connected">
                刷新
              </el-button>
            </div>
          </div>

          <!-- 连接状态显示 -->
          <div class="connection-status" v-if="state.showStatus">
            <el-alert
              :title="state.statusMessage"
              :type="state.statusType"
              :closable="false"
              show-icon
            />
          </div>

          <!-- VNC控制台 -->
          <div v-if="state.consoleType === 'vnc'" class="console-container">
            <iframe
              v-if="state.vncUrl"
              :src="state.vncUrl"
              frameborder="0"
              class="terminal-iframe"
              @load="handleIframeLoad"
            ></iframe>
            <div v-else class="console-placeholder">
              <el-empty description="请点击连接按钮启动VNC控制台" />
            </div>
          </div>

          <!-- SPICE控制台 -->
          <div v-if="state.consoleType === 'spice'" class="console-container">
            <div v-if="state.spiceConnected" class="spice-console">
              <!-- SPICE HTML5客户端容器 -->
              <div id="spice-area" class="spice-area"></div>
            </div>
            <div v-else class="console-placeholder">
              <div class="spice-info">
                <el-icon class="info-icon"><Monitor /></el-icon>
                <h3>SPICE远程控制台</h3>
                <p>SPICE协议提供更好的性能和用户体验</p>
                <div class="spice-options">
                  <el-button type="primary" @click="connectSpice" :loading="state.connecting">
                    启动SPICE控制台
                  </el-button>
                  <el-button @click="downloadSpiceClient">
                    下载SPICE客户端
                  </el-button>
                </div>
                <div class="spice-details">
                  <p><strong>连接信息:</strong></p>
                  <p>服务器: {{ state.spiceHost || state.ip }}</p>
                  <p>端口: {{ state.spicePort || state.port }}</p>
                  <p>协议: SPICE</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-information-area">
      <div class="strategy-information-area">
        <p class="information-title"><span>策略配置</span></p>
        <div class="information-content">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
          >
            <!-- 操作 -->
				    <template #operation="{ row }">
              <el-button type="primary" plain @click="refresh">配置</el-button>
            </template>
          </my-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="ResourceSummary">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch, onUnmounted } from 'vue';
import { Search, Monitor } from '@element-plus/icons-vue'
import { dayjs, ElMessage } from 'element-plus';
import { vmOverview } from '/@/api/ResourcePool'; // 接口
import { capacityConversion } from '/@/model/resource.ts'; // 表格 正则

// 类型定义
interface ConnectionInfo {
  host: string;
  port: string;
}

// 扩展Window类型以支持SPICE
declare global {
  interface Window {
    SpiceMainConn: any;
  }
}
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  columns: [
    { label: 'MAC地址', prop: 'mac', sortable: true, align: 'left' },
    { label: 'IP地址', tdSlot: 'ip' },
	  { label: '操作', tdSlot: 'operation', wrap: true },
  ] as Array<MyTableColumns>, // 表格表头配置
  pagination: {
		show: false,
	}, // 是否显示分页
  tableSelect: [],
  uuid: '-',
  status: '-',
  ip: '-',
  type: '-',
  port: '-',
  cpuRun: '-',
  stor: '-',
  storUsed: '-',

  // 控制台相关状态
  consoleType: 'vnc', // 控制台类型：vnc 或 spice
  connecting: false, // 是否正在连接
  connected: false, // 是否已连接
  showStatus: false, // 是否显示状态信息
  statusMessage: '', // 状态消息
  statusType: 'info' as 'success' | 'warning' | 'info' | 'error', // 状态类型

  // VNC相关
  vncUrl: '', // VNC连接URL
  vncHost: '', // VNC主机
  vncPort: '', // VNC端口

  // SPICE相关
  spiceConnected: false, // SPICE是否已连接
  spiceHost: '', // SPICE主机
  spicePort: '', // SPICE端口
  spiceClient: null as any, // SPICE客户端实例
});
// 概要 数据
const summaryQuery=()=>{
	// let libs = ['测试1', '测试2', '测试3', '测试4'];
  // state.uuid = libs[Math.round(Math.random() * 3)]
  vmOverview(props.treeItem.id).then(res=>{
    state.status = res.data.host_count
    state.ip = res.data.vm_count
  })
}
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async(resolve)=>{
    return resolve({
      data: [{mac:'192.168.100',ip:'***************'}], // 数据
      total: 1 // 总数
    })
    // await vmOverview({
    //   host_id: props.treeItem.id,
    //   page: page.pageNum, // 当前页
    //   pagecount: page.pageSize, // 每页显示条数
    //   order_type: page.order, // 排序规则
    //   order_by: page.sort, // 排序列
    //   // page: page.pageNum?page.pageNum:1, // 当前页
    //   // pagecount: page.pageSize?page.pageSize:10, // 每页显示条数
    //   // order_type: page.order?page.order:'asc', // 排序规则
    //   // order_by: page.sort?page.sort:'', // 排序列
    //   search_str: state.tableSearch // 搜索条件
    // }).then((res:any)=>{
    //   resolve({
    //     data: res.data, // 数据
    //     total: res.total*1 // 总数
    //   }) 
    // }).catch((err:any) => {})
  })
};
// 控制台类型切换
const handleConsoleTypeChange = (type: string) => {
  state.consoleType = type;
  disconnectConsole(); // 切换类型时断开当前连接
};

// 连接控制台
const connectConsole = async () => {
  if (state.consoleType === 'vnc') {
    await connectVNC();
  } else if (state.consoleType === 'spice') {
    await connectSpice();
  }
};

// 断开连接
const disconnectConsole = () => {
  if (state.consoleType === 'vnc') {
    disconnectVNC();
  } else if (state.consoleType === 'spice') {
    disconnectSPICE();
  }
};

// 刷新控制台
const refreshConsole = () => {
  if (state.connected) {
    disconnectConsole();
    setTimeout(() => {
      connectConsole();
    }, 1000);
  }
};

// VNC连接
const connectVNC = async () => {
  try {
    state.connecting = true;
    state.showStatus = true;
    state.statusMessage = '正在连接VNC控制台...';
    state.statusType = 'info';

    // 模拟获取VNC连接信息
    const vncInfo = await getVNCInfo();
    state.vncHost = vncInfo.host || state.ip;
    state.vncPort = vncInfo.port || state.port;
    state.vncUrl = `http://${state.vncHost}:${state.vncPort}/vnc.html`;

    state.connected = true;
    state.statusMessage = 'VNC控制台连接成功';
    state.statusType = 'success';

    setTimeout(() => {
      state.showStatus = false;
    }, 3000);

  } catch (error) {
    state.statusMessage = 'VNC控制台连接失败';
    state.statusType = 'error';
    ElMessage.error('VNC连接失败');
  } finally {
    state.connecting = false;
  }
};

// 断开VNC连接
const disconnectVNC = () => {
  state.vncUrl = '';
  state.connected = false;
  state.showStatus = true;
  state.statusMessage = 'VNC控制台已断开连接';
  state.statusType = 'warning';

  setTimeout(() => {
    state.showStatus = false;
  }, 2000);
};

// SPICE连接
const connectSpice = async () => {
  try {
    state.connecting = true;
    state.showStatus = true;
    state.statusMessage = '正在连接SPICE控制台...';
    state.statusType = 'info';

    // 检查SPICE HTML5客户端是否可用
    if (!window.SpiceMainConn) {
      await loadSpiceClient();
    }

    // 获取SPICE连接信息
    const spiceInfo = await getSPICEInfo();
    state.spiceHost = spiceInfo.host || state.ip;
    state.spicePort = spiceInfo.port || state.port;

    // 初始化SPICE客户端
    initSpiceClient();

    state.spiceConnected = true;
    state.connected = true;
    state.statusMessage = 'SPICE控制台连接成功';
    state.statusType = 'success';

    setTimeout(() => {
      state.showStatus = false;
    }, 3000);

  } catch (error) {
    state.statusMessage = 'SPICE控制台连接失败';
    state.statusType = 'error';
    ElMessage.error('SPICE连接失败: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    state.connecting = false;
  }
};

// 断开SPICE连接
const disconnectSPICE = () => {
  if (state.spiceClient) {
    state.spiceClient.stop();
    state.spiceClient = null;
  }

  state.spiceConnected = false;
  state.connected = false;
  state.showStatus = true;
  state.statusMessage = 'SPICE控制台已断开连接';
  state.statusType = 'warning';

  // 清空SPICE容器
  const spiceArea = document.getElementById('spice-area');
  if (spiceArea) {
    spiceArea.innerHTML = '';
  }

  setTimeout(() => {
    state.showStatus = false;
  }, 2000);
};

// 获取VNC连接信息
const getVNCInfo = async (): Promise<ConnectionInfo> => {
  // 这里应该调用实际的API获取VNC连接信息
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        host: state.ip || '************',
        port: state.port || '8123'
      });
    }, 1000);
  });
};

// 获取SPICE连接信息
const getSPICEInfo = async (): Promise<ConnectionInfo> => {
  // 这里应该调用实际的API获取SPICE连接信息
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        host: state.ip || '************',
        port: state.port || '5900'
      });
    }, 1000);
  });
};

// 加载SPICE HTML5客户端
const loadSpiceClient = () => {
  return new Promise((resolve, reject) => {
    if (window.SpiceMainConn) {
      resolve(true);
      return;
    }

    // 动态加载SPICE客户端脚本
    const scripts = [
      '/spice-html5/spicearraybuffer.js',
      '/spice-html5/spiceconn.js',
      '/spice-html5/spicemsg.js',
      '/spice-html5/spicetype.js',
      '/spice-html5/spiceutils.js',
      '/spice-html5/spice.js'
    ];

    let loadedCount = 0;

    scripts.forEach(src => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        loadedCount++;
        if (loadedCount === scripts.length) {
          resolve(true);
        }
      };
      script.onerror = () => {
        reject(new Error(`Failed to load SPICE client script: ${src}`));
      };
      document.head.appendChild(script);
    });
  });
};

// 初始化SPICE客户端
const initSpiceClient = () => {
  const spiceArea = document.getElementById('spice-area');
  if (!spiceArea || !window.SpiceMainConn) {
    throw new Error('SPICE客户端未准备就绪');
  }

  // 清空容器
  spiceArea.innerHTML = '';

  // 创建SPICE连接
  const uri = `ws://${state.spiceHost}:${state.spicePort}`;

  try {
    state.spiceClient = new window.SpiceMainConn({
      uri: uri,
      screen_id: "spice-area",
      dump_id: "debug-div",
      message_id: "message-div",
      password: "",
      onerror: (err) => {
        console.error('SPICE连接错误:', err);
        ElMessage.error('SPICE连接错误');
        disconnectSPICE();
      },
      onsuccess: () => {
        console.log('SPICE连接成功');
      }
    });
  } catch (error) {
    throw new Error('SPICE客户端初始化失败');
  }
};

// 下载SPICE客户端
const downloadSpiceClient = () => {
  const userAgent = navigator.userAgent;
  let downloadUrl = '';

  if (userAgent.includes('Windows')) {
    downloadUrl = 'https://www.spice-space.org/download/windows/spice-guest-tools/spice-guest-tools-latest.exe';
  } else if (userAgent.includes('Mac')) {
    downloadUrl = 'https://www.spice-space.org/download/osx/';
  } else {
    downloadUrl = 'https://www.spice-space.org/download.html';
  }

  window.open(downloadUrl, '_blank');
  ElMessage.info('正在打开SPICE客户端下载页面...');
};

// iframe加载完成
const handleIframeLoad = () => {
  console.log('VNC iframe加载完成');
};

// 原有函数
const consoleClick = () => {
  console.log('控制台',props.treeItem)
  if (state.consoleType === 'vnc') {
    window.open(state.vncUrl || 'http://************:8123/', '_blank')
  } else {
    connectSpice();
  }
}

const refresh = () => {
  console.log('操作')
}
onMounted(() => {
  console.log('加载')
  // summaryQuery()

  // 初始化控制台连接信息
  if (props.treeItem) {
    vmOverview(props.treeItem.uuid).then((res: any) => {
      state.uuid = res.uuid
      state.status = res.status
      state.ip = res.ip
      state.type = res.type
      state.port = res.port
      state.cpuRun = res.cpuRun
      state.stor = capacityConversion(res.stor)
      state.storUsed = capacityConversion(res.storUsed)
    }).catch((error) => {
      console.error('获取虚拟机信息失败:', error);
    });
  }
});

// 组件卸载时清理资源
onUnmounted(() => {
  disconnectConsole();
});
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    display: flex;
    justify-content: space-between;
    .left-information-area {
      width: 600px;
      height: calc(100%);
      padding: 5px;
      .colony-information-area {
        width: calc(100%);
        height: calc(100%);
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .colony-content {
          width: 100%;
          // height: calc(100% - 50px);
          height: 300px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          div {
            span {
              display: inline-block;
              width: 240px;
            }
            span:first-child {
              text-align: right;
            }
            span:nth-child(2) {
              width: 100px;
            }
            span:nth-child(3) {
              width: 240px;
            }
          }
        }
        .terminal-area {
          width: calc(100% - 20px);
          height: calc(100% - 350px);
          background: #414141;
          padding: 5px;
          margin-left: 10px;
          display: flex;
          flex-direction: column;

          .console-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;
            margin-bottom: 5px;
            border-radius: 4px;

            .control-buttons {
              display: flex;
              gap: 6px;
            }
          }

          .connection-status {
            padding: 8px 12px;
            margin-bottom: 5px;
          }

          .console-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .terminal-iframe {
              width: 100%;
              height: 100%;
              border: none;
            }

            .console-placeholder {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #2c2c2c;
              color: #fff;
              border-radius: 4px;
            }
          }

          .spice-console {
            flex: 1;
            display: flex;
            flex-direction: column;

            .spice-area {
              flex: 1;
              width: 100%;
              height: 100%;
              background-color: #000;
              position: relative;
              border-radius: 4px;

              canvas {
                width: 100% !important;
                height: 100% !important;
              }
            }
          }

          .spice-info {
            text-align: center;
            padding: 20px;
            background-color: #2c2c2c;
            color: #fff;
            border-radius: 4px;

            .info-icon {
              font-size: 36px;
              color: #409eff;
              margin-bottom: 15px;
            }

            h3 {
              color: #fff;
              margin-bottom: 8px;
              font-size: 16px;
              font-weight: 600;
            }

            p {
              color: #ccc;
              margin-bottom: 15px;
              font-size: 12px;
            }

            .spice-options {
              margin-bottom: 20px;

              .el-button {
                margin: 0 4px;
              }
            }

            .spice-details {
              background-color: #3c3c3c;
              border: 1px solid #555;
              border-radius: 4px;
              padding: 15px;
              text-align: left;
              max-width: 300px;
              margin: 0 auto;

              p {
                margin: 6px 0;
                font-size: 11px;

                &:first-child {
                  font-weight: 600;
                  color: #fff;
                  margin-bottom: 10px;
                }

                strong {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
    .right-information-area {
      width: calc(100% - 620px);
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .strategy-information-area {
        width: 100%;
        height: calc(100%);
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .information-content {
          padding-top: 30px;
          width: calc(100%);
          height: calc(100% - 100px);
          position: relative;
        }
      }
    }
    
  }
  .information-title {
    height: 40px;
    display: flex;
    align-items: flex-end;
    span {
      display: inline-block;
      text-align: center;
      width: 100px;
      line-height: 30px;
		  background-image: url('/@/assets/resource/title.jpg');
      background-size: 100% 100%;
    }
  }
  .general-content {
    height: 100px;
    .general-img {
      display: flex;
      img {
        width: 80px;
      }
      div {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        p {
          span:nth-child(2) {
            margin-left: 15px;
          }
        }
      }
    }
    .general-progress {
      display: flex;
      justify-content: space-between;
      .el-progress {
        width: 350px;
      }
    }
  }
</style>
<template>
	<div>
		<el-dialog v-model="state.isShow" append-to-body>
			<template #header="{ close, titleId, titleClass }">
				<span class="el-dialog__title">{{ props.tableRow.name }} 交换机-规则</span>
			</template>
			<div class="dialog-area">
				<div class="tabs-btn-area">
					<div>
						<el-button type="primary" plain @click="refresh">刷新</el-button>
						<el-button type="primary" plain @click="newClick">新建规则</el-button>
						<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
					</div>
					<div>
						<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
							<template #append>
								<el-button :icon="Search" @click="refresh"></el-button>
							</template>
						</el-input>
					</div>
				</div>
				<div class="table-area">
					<my-table ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData" @selectionChange="selectChange">
						<!-- 子网 -->
						<template #cidr="{ row }">
							<span>{{ row.cidr.toString() }}</span>
						</template>
						<!-- 操作 -->
						<template #operation="{ row }">
              <el-button type="danger" plain @click="deleteClick([row])">删除</el-button>
						</template>
					</my-table>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="state.isShow = false">关闭</el-button>
					<!-- <el-button type="primary" @click="confirm">确认</el-button> -->
				</div>
			</template>
		</el-dialog>
		<TableNew :newTime="state.newTime" :tableRow="props.tableRow" @returnOK="returnOK"></TableNew>
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>

<script lang="ts" setup name="PortGroup">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ruleTableQuery, ruleTableDelete } from '/@/api/Network'; // 接口
import { secureGroupColumns } from '/@/model/network.ts'; // 表列、正则
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));
const props = defineProps({
	ruleTime: {
		type: String,
		required: true,
	},
	tableRow: {
		type: Object,
		required: true,
	},
});
const state = reactive({
	isShow: false,
	listData: [
		{name: '测试规则1',cidr:['***********', '*************'],status:'active',network_type: 'vlan',vlanid: 1,id: 'aaa1'},
		{name: '测试规则2',cidr:['*************'],status:'list',network_type: 'vlan',vlanid: 1,id: 'aaa1'},
	],
	columns: secureGroupColumns,
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	tableRow: {},
	newTime: '',
	deleteTime: '',
});
interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = [];
	return new Promise(async (resolve) => {
		ruleTableQuery({})
			.then((res: any) => {
				resolve({
					data: res.data, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {
				resolve({
					data: state.listData, // 数据
					total: state.listData.length, // 总数
				});
			});
	});
};
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// const emit = defineEmits(['portReturn']);
const confirm = () => {
	state.isShow = false;
	// emit('portReturn', 'cg');
};
// 新建交换机
const newClick = () => {
	state.newTime = '' + new Date();
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 删除端口组
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '规则/' + new Date();
	}
};
// 返回数据
const returnOK = (item: any) => {
	if (item == 'delete') {
		ruleTableDelete({
			names: formDelet.tableNames,
			ids: formDelet.tableIDs,
		}).then((res: any) => {
			if (res.msg == 'ok') {
				refresh();
				ElMessage.success('删除规则操作完成');
			} else {
				ElMessage.error('删除规则操作失败');
			}
		});
	} else {
		state.listData.push(
			{name: 'list'+Math.floor(Math.random()*100),cidr:['************'],status:'active',network_type: 'vlan',vlanid: 1,id: '1aaa2'},
		)
		// refresh();
	}
};
watch(
	() => props.ruleTime,
	(val) => {
		state.isShow = true;
		if(tableRef.value){
			refresh();
		}
	}
);
</script>
<style lang="scss" scoped>
.dialog-area {
	height: 650px;
	width: 900px;
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.table-area {
		position: relative;
		height: calc(100% - 50px);
		width: 100%;
	}
}
</style>
<template>
	<div class="storage-area layout-padding">
    <el-card>
      <div class="storag-btn-area">
        <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh">刷新</el-button>
            <el-button type="primary" plain @click="newClick">新建本地交换机</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
          </div>
          <div>
            <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :searchParams="state.searchParams"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
            <!-- 集群 -->
            <template #colony="{ row }">
              <span>{{ row.cluster_name?row.cluster_name:'-' }}</span>
						</template>
            <!-- 宿主机 -->
            <template #host="{ row }">
              <span>{{ row.host_hostname?row.host_hostname:'-' }}</span>
						</template>
            <!-- 类型 -->
            <template #type="{ row }">
              <span>{{ row.vswitch_type=='local'?'本地交换机':'分布式交换机' }}</span>
						</template>
            <!-- 端口组 -->
            <template #port="{ row }">
              <el-button type="primary" link @click="portClick(row)"><el-icon><View /></el-icon> 端口组</el-button>
            </template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-dropdown @command="commandItem($event,row)">
                <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="bj">编辑</el-dropdown-item>
                    <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
    <PortGroup :portTime="state.portTime" :tableRow="state.tableRow"></PortGroup>
    <TableNew :newTime="state.newTime" :treeItem="{}" @returnOK="returnOK"></TableNew>
    <TableEdit :editTime="state.editTime" :tableRow="state.tableRow" @returnOK="returnOK"></TableEdit>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts" name="Switch">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { switchColumns } from '/@/model/network.ts'; // 表列、正则
import { netSwitchQuery,netSwitchDelete } from '/@/api/Network'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const PortGroup = defineAsyncComponent(() => import('./portGroup/index.vue'))
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'))
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

import { useRouter } from 'vue-router';
import { tr } from 'element-plus/es/locale';
const router = useRouter();
// 定义变量内容
const state = reactive({
  columns: switchColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  tableRow: {},
  newTime: '',
  portTime: '',
  editTime: '',
  deleteTime: '',
});

interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
	return new Promise(async(resolve)=>{
    netSwitchQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 端口组
const portClick = (row:any)=>{
  state.tableRow = row
  state.portTime = ''+new Date()
}
// 表操作列
const commandItem = (item: string,row:any)=>{
  state.tableRow = row
  switch (item) {
    case 'bj':
      state.editTime = ''+new Date()
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 新建
const newClick = () => {
  state.newTime = 'all/'+new Date()
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '虚拟交换机/'+new Date()    
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    netSwitchDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.code == 200){
        refresh()
        ElMessage.success('删除虚拟交换机操作完成');
      }else {
        ElMessage.error('删除虚拟交换机操作失败');
      }
    })
  }else {
    refresh()
  }
}
onMounted(() => {
})
</script>
<style scoped lang="scss">
.storage-area {
  padding-top: 0 !important;
	width: calc(100%);
	height: calc(100%);
  .storag-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
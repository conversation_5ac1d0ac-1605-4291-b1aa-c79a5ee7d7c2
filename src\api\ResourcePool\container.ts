import request from '/@/utils/request';
// 容器
// 查询
export const containerQuery = (data: object) => {
	return request({
		url: '/theapi/v1/containers',
		method: 'post',
		data,
	});
};
// 新建
export const containerNew = (data: object) => {
	return request({
		url: '/theapi/v1/containers/create',
		method: 'post',
		data,
	});
};
// 编辑
export const containerEdit = (data: object) => {
	return request({
		url: '/theapi/v1/containers/update',
		method: 'post',
		data,
	});
};
// 启动安全组
export const containerGroupStart = (data: object) => {
	return request({
		url: '/theapi/v1/containers/***',
		method: 'post',
		data,
	});
};
// 停止安全组
export const containerGroupStop = (data: object) => {
	return request({
		url: '/theapi/v1/containers/***',
		method: 'post',
		data,
	});
};
// 操作
export const containerOperate = (data: object) => {
	return request({
		url: '/theapi/v1/containers/action',
		method: 'post',
		data,
	});
};
// 执行命令
export const containerInstruction = (data: object) => {
	return request({
		url: '/theapi/v1/containers/action/execute',
		method: 'post',
		data,
	});
};
// 死亡信号
export const containerKill = (data: object) => {
	return request({
		url: '/theapi/v1/containers/action/kill',
		method: 'post',
		data,
	});
};
// 删除
export const containerDeleted = (data: object) => {
	return request({
		url: '/theapi/v1/containers/delete',
		method: 'post',
		data,
	});
};
// 停止并删除
export const containerStopDeleted = (data: object) => {
	return request({
		url: '/theapi/v1/containers/delete',
		method: 'post',
		data,
	});
};
// 停止并删除
export const containerForceDeleted = (data: object) => {
	return request({
		url: '/theapi/v1/containers/delete',
		method: 'post',
		data,
	});
};

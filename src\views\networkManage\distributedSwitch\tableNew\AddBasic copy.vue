<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<div class="prompt-area">
			<p><el-icon color="#fe944d"><WarningFilled /></el-icon>可以选择跨集群的宿主机物理网卡关联，作为分布式交换机的上行链路。</p>
			<p>支持宿主机上的虚拟机，在分布式交换机上的网络中按照规则通信。</p>
		</div>
		<el-form-item label="分布式交换机" prop="name">
			<el-input v-model="formItem.name" placeholder="请输入分布式交换机名称" />
		</el-form-item>
		<el-form-item label="物理网卡">
			<el-input v-model="formItem.card" placeholder="请输入物理机名称" />
		</el-form-item>
	</el-form>
	<ZtreePublick :type='formItem.type' :zNodes='formItem.zNodes'></ZtreePublick>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts';
import { propName } from '/@/model/resource.ts'; // 表列、正则
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['basicOK']);
const formItem = reactive({
	name: '',
	card: '',
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});
const rulesForm = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "blur" },
  ],
  card: [
    { required: true, message: '必填项', trigger: 'blur' },
  ],
})
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '主机1', pid: '0' },
		{ id: '2', name: '主机2', pid: '0' },
		{ id: '3', name: '主机3', pid: '0' },
		{ id: '4', name: '网卡1', pid: '1' },
		{ id: '5', name: '网卡2', pid: '1' },
		{ id: '6', name: '网卡3', pid: '2' },
		{ id: '7', name: '网卡4', pid: '2' },
		{ id: '8', name: '网卡5', pid: '3' },
		{ id: '9', name: '网卡6', pid: '3' },
	];
};
watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('basicOK', formItem);
				}
				
			});
		}
	}
);
setTimeout(() => {
	formItem.type = '分布式存储'+new Date()
	treeData()
}, 200);
</script>
<style lang="scss" scoped>
.prompt-area {
  margin-bottom: 20px;
  p {
    color: #ccc;
    padding: 5px 10px;
    .el-icon {
      margin-right: 5px;
    }
  }
	p:nth-child(2) {
		padding-left: 30px;
	}
}
</style>


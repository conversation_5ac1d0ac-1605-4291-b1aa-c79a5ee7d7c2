<template>
  <el-dialog
    v-model="formItem.isShow"
    title="删除子网"
    width="600"
  >
    <div class="form-switch-area">
      <el-form
        ref="ruleFormRef"
        :model="formItem"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="选择子网" prop="cidrid">
          <el-select v-model="formItem.cidrid" style="width: 100%" @change="subnetClick">
            <el-option v-for="item in formItem.cidrData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="netNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { netSubnetDelete } from '/@/api/Network'; // 接口
const props = defineProps({
  editTime: {
    type: String,
    required: true
  },
  tableRow: {
    type: Object,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  cidrData: [{ label: '', value: 0 }],
  cidrid: 0,
  cidrname: '',
  subnetData: [],
  subnetID: '',
});
const subnetClick = (value:any)=>{
  const selectedOption = formItem.cidrData.find(option => option.value === value);
  if (selectedOption) {
    formItem.cidrname = selectedOption.label;
    formItem.subnetID = formItem.subnetData[value];
  }
}
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        netSubnetDelete({
          subnet_id: formItem.subnetID,
          name: formItem.cidrname
        })
        .then(res => {
          if(res.msg == 'ok') {
            formItem.isShow = false;
            emit('returnOK', 'refresh');
            ElMessage.success('删除子网操作已完成');
          }else{
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}

watch(
  ()=> props.editTime,
  (val)=>{
    formItem.isShow = true;
    formItem.cidrData = props.tableRow.cidr.map((item:string, index:number) => {
      return {
        label: item,
        value: index
      };
    });
    formItem.cidrname = formItem.cidrData[0].label
    formItem.cidrid = formItem.cidrData[0].value
    formItem.subnetData = props.tableRow.subnets
    formItem.subnetID = formItem.subnetData[formItem.cidrid]
  }
);

const rules = reactive<FormRules>({
  cidrid: [{ required: true, message: '必选项', trigger: 'bulk' }],
})
</script>
<style lang="scss" scoped>
  .form-switch-area {
    width: 100%;
    overflow: auto;
    .route-input {
      width: 100%;
      display: flex;
    }
    .route-datas {
      height: 70px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .route-item {
        width: 49%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .route-wd {
          display: inline-block;
          width: 50%;
        }
        .route-xyt {
          display: inline-block;
          width: 42%;
        }
      }
    }
  }
</style>
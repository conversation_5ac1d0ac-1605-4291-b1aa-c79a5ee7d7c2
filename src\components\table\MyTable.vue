<template>
  <div class="page-box">
    <!-- table表格栏 -->
    <div class="table" :class="paginationConfig.show ? 'page-show' : 'page-show-false'">
      <el-table ref="mytable" v-loading="loading" :data="tableData" :show-header="showHeader" :row-key="rowKey" :tree-props="tree.treeProps"
        :lazy="tree.lazy" :load="tree.load" tooltip-effect="dark" stripe width="100%" height="100%" :border="border"
        @sort-change="handleSortChange" @selection-change="handleSelectionChange" @expand-change="handleExpandChange"
        @select="handleSelectClick">
        <el-table-column v-for="item in columns" :key="item.label" :filter-method="item.filters && filterHandler"
          :show-overflow-tooltip="!item.wrap" :sortable="item.sortable" v-bind="item">
          <template #header="scope" v-if="!!item.labelSlot">
            <slot :name="item.labelSlot" v-bind="scope"></slot>
          </template>
          <template #default="scope" v-else-if="!!item.tdSlot">
            <slot :name="item.tdSlot" v-bind="scope"></slot>
          </template>
          <template #default="props" v-else-if="item.type === 'expand'">
            <slot :name="item.expand" v-bind="props"></slot>
          </template>
        </el-table-column>
        <template #empty>
          <img src="../../assets/images/no-data.svg" alt="" class="minimg-wrap" />
        </template>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-pagination v-if="paginationConfig.show && total > 0" class="pagination" :style="paginationConfig.style"
      @size-change="handleSizeChange" v-model:currentPage="pageNum" @current-change="handleCurrentChange"
      :page-sizes="paginationConfig.pageSizes" v-model:pageSize="pageSize" :layout="paginationConfig.layout"
      :total="total"></el-pagination>
  </div>
</template>
<script lang="ts" name="MyTable">
import { TableColumnCtx } from "element-plus/es/components/table/src/table-column/defaults";
import { defineComponent, reactive, toRefs, onBeforeMount, ref, Ref } from "vue";

export default defineComponent({
  props: {
    // 请求数据的方法
    request: {
      type: Function,
    },
    border: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    // 表头配置
    columns: {
      type: Array<MyTableColumns>,
      default: function () {
        return [];
      },
    },
    // 行数据的Key，同elementUI的table组件的row-key
    rowKey: {
      type: String,
      default: "id",
    },
    // 默认排序字段，未传则为空
    sort: {
      type: String,
      default: "",
    },
    // 分页配置，false表示不显示分页
    pagination: {
      type: Object,
      default: () => ({}),
    },
    searchParams: Object,
    tree: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    // 请求列表数据
    const getTableData = async () => {
      state.loading = true;
      if (props.request) {
        try {
          const { data, total } = await props.request({
            ...props.searchParams,
          }, {
            pageNum: state.pageNum,
            pageSize: state.pageSize,
            sort: state.sort, //排序字段
            order: state.order, //排序类型，desc ,asc
          });
          state.loading = false;
          state.tableData = data;
          state.total = total;
        } catch (err) {
          state.loading = false;
          console.error(err);
        }
      }
    };
    const getData = async () => {
      if (props.request) {
        const { data, total } = await props.request({
          ...props.searchParams,
        }, {
          pageNum: state.pageNum,
          pageSize: state.pageSize,
          sort: state.sort, //排序字段
          order: state.order, //排序类型，desc ,asc
        });
        state.loading = false;
        state.tableData = data;
        state.total = total;
      }
    };
    onBeforeMount(() => {
      getTableData();
    });
    const state = reactive({
      mytable: {} as Ref,
      loading: false,
      tableData: reactive([]),
      order: 'asc',
      sort: props.sort,
      total: 0,
      pageNum: 1,
      pageSize: (!!props.pagination && props.pagination.pageSize) || 10,
      paginationConfig: {
        show: false,
        layout: [],
        pageSizes: [],
        style: {}
      },
      // 搜索
      handleSearch() {
        state.pageNum = 1;
        getTableData();
      },
      // 刷新
      refresh() {
        getTableData();
      },
      // 当前页变化
      handleCurrentChange(page: number) {
        getTableData();
      },
      // 改变每页size数量
      handleSizeChange(value: number) {
        state.pageNum = 1;
        getTableData();
      },
      clearSelection() {
        state.mytable?.clearSelection();
      },
      // 全选
      handleSelectionChange(arr: EmptyArrayType) {
        emit("selectionChange", arr);
      },
      //展开扩展事件
      handleExpandChange(row: EmptyObjectType) {
        emit("handleExpandChange", row);
      },
      // 选中事件
      handleSelectClick(row: EmptyObjectType) {
        emit("selectClick", row);
      },
      // 排序
      handleSortChange(params: EmptyObjectType) { //{ column, prop, order }
        state.sort = params.prop;
        if (params.order === 'descending') {
          state.order = 'desc';
        } else {
          state.order = 'asc';
        }
        getTableData();
      },
      // 过滤方法
      filterHandler(value: string, row: EmptyObjectType, column: TableColumnCtx<EmptyObjectType>) {
        const property = column["property"];
        return row[property] === value;
      },
      getData() {
        getData();
      },
    });
    if (typeof props.pagination === "object") {
      const { layout, pageSizes, style, show } = props.pagination;
      state.paginationConfig = {
        show: show || false,
        layout: layout || "total, sizes, ->,prev, pager, next, jumper",
        pageSizes: pageSizes || [10, 20, 30, 40, 50, 100],
        style: style || {},
      };
    }
    return {
      ...toRefs(state),
    };
  },
});
</script>
<style lang="scss" scoped>
.page-box {
  width: calc(100%);
  box-sizing: border-box;
  height: calc(100%);
  position: absolute;
  .search {
    padding: 20px 20px 0;
    background: #fff;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    .el-form-item {
      margin-bottom: 20px;
    }
    .search-btn {
      margin-left: auto;
    }
    ::v-deep(.el-input-number .el-input__inner) {
      text-align: left;
    }
  }
  .head {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    padding: 20px 20px 0;
    background: #fff;
    position: relative;
    .title {
      font-size: 16px;
      border-bottom: #ccc;
    }
    .toolbar {
      position: absolute;
      right: 20px;
      bottom: 0px;
    }
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
  .table {
    height: calc(100% - 240px);
    background: #fff;
    overflow: hidden;
    :deep(th) {
      background: var(--el-table-header-th);
    }
    :deep(.el-scrollbar__view) {
      width: 100%;
    }
    :deep(.el-scrollbar) {
      --el-scrollbar-bg-color: var(--el-color-primary);
      --el-scrollbar-hover-bg-color: var(--el-color-primary);

      .el-scrollbar__thumb {
        opacity: 1;
      }
    }
    :deep(.el-table__body) {
      width: 100%;
      --el-table-row-hover-bg-color: transparent;
      .hover-row {
        background: linear-gradient(to right, var(--next-color-hover-tr1) 0%, var(--next-color-hover-tr2) 50%, var(--next-color-hover-tr3) 100%);
      }
      tr {
        &:hover {
          background: linear-gradient(to right, var(--next-color-hover-tr1) 0%, var(--next-color-hover-tr2) 50%, var(--next-color-hover-tr3) 100%);
        }
      }
      .el-table-column--selection {
        .cell {
          display: block;
        }
      }
    }
    :deep(.el-table__cell) {
      height: 56px;
      border-right: none;
    }
    :deep(.el-table__header) {
      .el-table__cell {
        height: 44px;
        background: var(--next-bg-main-color);
        color: var(--el-text-color-primary);
      }
    }
  }
  .page-show {
    height: calc(100% - 62px);
  }
  .page-show-false {
    height: calc(100%);
  }
  .pagination {
    background: red;
    padding: 20px 20px 10px 10px;
    background: var(--el-fill-color-blank);
    text-align: right;
    justify-content: right;
    :last-child {
      margin-right: 0;
    }
  }
  :deep(.el-popper) {
    font-size: 14px;
    max-width: 50%
  }
  :deep(.el-table__empty-text) {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
  }
  .minimg-wrap {
    height: 50%;
  }
  .no-datas-wrap {
    font-size: 16px;
  }
}
</style>

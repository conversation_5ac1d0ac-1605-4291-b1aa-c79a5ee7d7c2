<template>
	<div class="storag-btn-area">
		<div class="tabs-btn-area">
			<div>
				<el-button type="primary" plain @click="refresh">刷新</el-button>
				<el-button type="primary" plain @click="newClick">新建分布式交换机</el-button>
				<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
			</div>
			<div>
				<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
					<template #append>
						<el-button :icon="Search" @click="refresh"></el-button>
					</template>
				</el-input>
			</div>
		</div>
		<div class="tabs-table-area">
			<my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
				@selectionChange="selectChange"
			>
				<!-- 集群 -->
				<template #colony="{ row }">
					<span>{{ row.cluster_name ? row.cluster_name : '-' }}</span>
				</template>
				<!-- 宿主机 -->
				<template #host="{ row }">
					<span>{{ row.host_hostname ? row.host_hostname : '-' }}</span>
				</template>
				<!-- 类型 -->
				<template #type="{ row }">
					<span>{{ row.vswitch_type == 'local' ? '本地交换机' : '分布式交换机' }}</span>
				</template>
				<!-- 端口组 -->
				<template #port="{ row }">
					<el-button type="primary" link @click="portClick(row)"
						><el-icon><View /></el-icon> 端口组</el-button
					>
				</template>
				<!-- 操作 -->
				<template #operation="{ row }">
					<el-dropdown trigger="click" @command="commandItem($event, row)">
						<el-button type="primary"
							>操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon
						></el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="bj" disabled>编辑</el-dropdown-item>
								<el-dropdown-item command="sc" style="color: red" divided>删除</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</my-table>
		</div>
		<PortGroup ref="portRef" />
		<TableNew ref="newRef" @returnOK="returnOK" />
    <TableEdit ref="editRef" @returnOK="returnOK" />
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>
<script setup lang="ts" name="Switch">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { switchColumns } from '/@/model/network.ts'; // 表列、正则
import { distributedSwitchQuery, distributedSwitchDelete } from '/@/api/Network'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const PortGroup = defineAsyncComponent(() => import('/@/views/networkManage/distributedSwitch/portGroup/index.vue'))
const TableNew = defineAsyncComponent(() => import('/@/views/networkManage/distributedSwitch/tableNew/index.vue'))
const TableEdit = defineAsyncComponent(() => import('/@/views/networkManage/distributedSwitch/TableEdit.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

import { useRouter } from 'vue-router';
import { tr } from 'element-plus/es/locale';
const router = useRouter();
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
// 定义变量内容
const state = reactive({
	columns: switchColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	deleteTime: '',
});

interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = [];
	return new Promise(async (resolve) => {
		distributedSwitchQuery({
      cluster_id: props.treeItem.id,
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		})
			.then((res: any) => {
				resolve({
					data: res.data, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {
				resolve({
					data: [], // 数据
					total: 0, // 总数
				});
			});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 端口组
const portRef = ref()
const portClick = (row: any) => {
  portRef.value.openDialog(row);
};
// 表操作列
const editRef = ref()
const commandItem = (item: string, row: any) => {
	switch (item) {
		case 'bj':
      editRef.value.openDialog(row);
			break;
		case 'sc':
			deleteClick([row]);
			break;
	}
};
// 新建
const newRef = ref()
const newClick = () => {
  newRef.value.openDialog(props.treeItem)
};
// 删除
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '虚拟交换机/' + new Date();
	}
};
// 返回数据
const returnOK = (item: any) => {
	if (item == 'delete') {
		distributedSwitchDelete({
			names: formDelet.tableNames,
			ids: formDelet.tableIDs,
		}).then((res) => {
			if (res.code == 200) {
				refresh();
				ElMessage.success('删除虚拟交换机操作完成');
			} else {
				ElMessage.error('删除虚拟交换机操作失败');
			}
		});
	} else {
		refresh();
	}
};
onMounted(() => {});
</script>
<style scoped lang="scss">
.storag-btn-area {
	width: calc(100%);
	height: calc(100%);
	.tabs-btn-area {
		height: 40px;
		display: flex;
		justify-content: space-between;
	}
	.tabs-table-area {
		width: calc(100%);
		height: calc(100% - 50px);
		position: relative;
	}
}
</style>
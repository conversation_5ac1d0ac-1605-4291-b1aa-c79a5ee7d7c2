<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    width="500"
  >
    <div>
      <span>是否对下列虚拟机进行 <span :style="{color:props.config.color}">{{ props.config.type }}</span> 操作？</span>
      <p :style="{color:props.config.color,'word-wrap': 'break-word'}">{{ props.names.toString() }}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="TableDelete">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';

const props = defineProps({
  names: {
    type: Array,
    required: true
  },
  config: {
    type: Object,
    required: true
  },
});
const formItem = reactive({
  isShow: false,
  title: '删除',
});

const emit = defineEmits(['generalOK']);
const confirm =()=>{
  emit('generalOK', props.config.type);
  formItem.isShow = false;
}
watch(
  ()=> props.config.time,
  (val)=>{
    formItem.isShow = true;
    formItem.title = props.config.type+'虚拟机'
  }
);
</script>
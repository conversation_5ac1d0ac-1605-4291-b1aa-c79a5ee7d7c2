<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    class="dialog-500"
  >
    <div>
      <span>是否对下列虚拟机进行 <span :style="{color:formItem.color}">{{ formItem.type }}</span> 操作？</span>
      <p :style="{color:formItem.color,'word-wrap': 'break-word'}">{{ formItem.names.toString() }}</p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
const formItem = reactive({
  isShow: false,
  title: '删除',
  type: '删除',
  color: 'green',
  names: []
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  emit('returnOK', formItem.type);
  formItem.isShow = false;
}
// 打开弹窗
const openDialog = async (type:string,color:string,names:any) => {
  formItem.isShow = true;
  nextTick(() => {
    formItem.title = type+'虚拟机'
    formItem.type = type
    formItem.color = color
    formItem.names = names
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
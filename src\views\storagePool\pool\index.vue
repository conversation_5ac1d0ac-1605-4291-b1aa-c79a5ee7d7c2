<template>
	<div class="pool-tree-area">
		<div class="pool-btn">
			<el-tooltip effect="light" content="新建分组" placement="top">
				<el-button type="primary" circle @click="newClick" :icon="CirclePlus"></el-button>
			</el-tooltip>
			<el-tooltip effect="light" content="编辑分组" placement="top">
				<el-button type="primary" circle @click="editClick" :icon="Edit"></el-button>
			</el-tooltip>
			<el-tooltip effect="light" content="刷新分组" placement="top">
				<el-button type="primary" circle @click="treePoolQuery('refresh')" :icon="RefreshRight"></el-button>
			</el-tooltip>
			<el-tooltip effect="light" content="删除分组" placement="top">
				<el-button type="primary" circle @click="deletedClick" :icon="Delete"></el-button>
			</el-tooltip>
		</div>
		<div class="tree-area">
			<el-tree
				ref="treeRef"
				node-key="id"
				:highlight-current="true"
				:default-expand-all="true"
				:expand-on-click-node="false"
				:data="state.treeData"
				:props="defaultProps"
				@node-click="handleNodeClick"
			>
				<template #default="{ node, data }">
					<span style="display: flex; align-items: center">
						<el-icon v-if="data.icon"><component :is="data.icon" /></el-icon>
						<span style="margin-left: 8px">{{ node.label }}</span>
					</span>
				</template>
			</el-tree>
			<!-- <ul id="treeDemo" class="ztree"></ul> -->
		</div>
		<GroupOperate ref="operateRef" @groupOK="groupOK"></GroupOperate>
		<TableDelete :names='[state.treeName]' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { CirclePlus, Edit, RefreshRight, Delete } from '@element-plus/icons-vue'; // ICON
import { dayjs } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
const GroupOperate = defineAsyncComponent(() => import('./GroupOperate.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const defaultProps = {
	children: 'children',
	label: 'label',
};
const state = reactive({
	// treeData: [id: '', label: '', children: [] ]
	treeData: [
		{ id: 'a1', label: '根节点', children: [
			{ id: 'a2', label: '一级1', icon: 'Edit', children: [
				{ id: 'a4', label: '二级1' },
				{ id: 'a5', label: '二级2' },
			]},
			{ id: 'a3', label: '一级2' },
		]},
	],
	treeID: '',
	treeName: '',
	deleteTime: '',
});

const operateRef = ref();
// 新建组
const newClick = () => {
  operateRef.value.openDialog({type:'new'})
}
// 编辑组
const editClick = () => {
  operateRef.value.openDialog({type:'edit',id:state.treeID,name:state.treeName})
}
// 删除组
const deletedClick = () => {
  state.deleteTime = '存储池分组/'+new Date()
}
const emit = defineEmits(['poolOK']);
const treeRef = ref();
const treePoolQuery = async (type:string) => {
	// state.treeData = [
	// 	{ id: 'a1', label: '根节点', children: [
	// 		{ id: 'a2', label: '一级1', icon: 'Edit', children: [
	// 			{ id: 'a4', label: '二级1' },
	// 			{ id: 'a5', label: '二级2' },
	// 		]},
	// 		{ id: 'a3', label: '一级2' },
	// 	],},
	// ];
	if(type == 'reset') {
		state.treeID = state.treeData[0].id;
		state.treeName = state.treeData[0].label;
	}
	if (treeRef.value && state.treeData.length > 0) {
    treeRef.value.setCurrentKey(state.treeID); // 选中第一行
		emit('poolOK', state.treeData[0]);
  }
};

interface Tree {
	label: string;
	id: string;
	icon?: any;
	children?: Tree[];
}
const handleNodeClick = (data: Tree) => {
	state.treeID = data.id
	state.treeName = data.label;
	emit('poolOK', data);
};
const groupOK = (item: string) => {
	state.treeData.push({
		id: ''+new Date(),
		label: 'list',
		children: []
	})
	treePoolQuery('refresh')
	console.log('返回参数',item);
}
// 删除返回参数
const returnOK = (item: string) => {
	if(item == 'delete') {
		treePoolQuery('reset')
    // nasTableDelete({
    //   names: formDelet.tableNames,
    //   ids: formDelet.tableIDs,
    // })
    // .then(res => {
    //   if(res.msg == 'ok'){
    //     treePoolQuery('reset')
    //     ElMessage.success('删除操作完成');
    //   }else {
    //     ElMessage.error('删除操作失败');
    //   }
    // })
  }else {
    // treePoolQuery('reset')
  }
}
// 页面加载时
onMounted(() => {
	setTimeout(() => {
		treePoolQuery('reset');
	}, 500);
});
</script>
<style scoped lang="scss">
.pool-tree-area {
	width: 100%;
	height: 100%;
	.pool-btn {
		display: flex;
		justify-content: space-evenly;
		margin-bottom: 15px;
	}
	.tree-area {
		width: 100%;
		height: calc(100% - 50px);
		overflow: auto;
	}
}
</style>
      
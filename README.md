#### 🏭 环境支持

| Edge      | Firefox      | Chrome      | Safari      |
| --------- | ------------ | ----------- | ----------- |
| Edge ≥ 88 | Firefox ≥ 78 | Chrome ≥ 87 | Safari ≥ 13 |

> 由于 Vue3 不再支持 IE11，故而 ElementPlus 也不支持 IE11 及之前版本。

#### ⚡ 使用说明

建议使用 cnpm，因为 yarn 有时会报错。<a href="http://nodejs.cn/" target="_blank">node 版本 > 14.18+/16+</a>

> Vite 不再支持 Node 12 / 13 / 15，因为上述版本已经进入了 EOL 阶段。现在你必须使用 Node 14.18+ / 16+ 版本。

```bash

# 安装依赖
cnpm install

# 运行项目
cnpm run dev

# 打包发布
cnpm run build
```

#### 📚 开发文档

- 查看开发文档：<a href="https://lyt-top.gitee.io/vue-next-admin-doc-preview" target="_blank">vue-next-admin-doc</a>

#### 目录结构说明

```

├── USM-WEB
	├── src
		├──  api // 存放接口文件
		├── assets // 存放静态资源，图片等
		├── components // 通用组件
		├── directive // vue指令文件
		├── i18n // 国际化
		├── layout // 布局
		├── router // 路由
		├── stores // 使用pinia，类似vue-store
		└── theme // 默认的，一些基础样式，包含字体文件
		└── types // typescript类型声明文件
		└── utils // 工具方法
		└── views // 界面
			└── Overview 首页
			└── login 登录
		└── App.vue // 主界面入口
		└── main.ts // 入口文件，引入第三方组件等
```

#### 其他

1.修改界面布局在 /layout/main/classic.vue，包含左侧列表，头部。
2.主题颜色为橘黄色，在设置样式时，可以采用

```
	color: var(--el-color-primary);
	background: var(--el-color-primary);
```

3.添加了防抖节流自定义指令。例子：

```
	<el-button size="default" type="primary" v-antiShake="onSearch"> 查询 </el-button>	

	v-antiShake="onSearch"
	
	onSearch为点击函数
	注意，目前不支持点击函数的传参
```

4. 字体图标文件在/src/theme/iconfont里,
	defaulticonfont为框架自带图标，使用方式
	```
		<i class="iconfont icon-wancheng"></i>
	```
	font_xxxx_xxxx为项目图标，为防止冲突，使用方式为
	```
		<i class="iconfont-xxx icon-wancheng-copy"></i>
	```
5. 版本号修改在package.json文件，因为存在部分缓存，如果代码升级后，可能需要清除缓存，可以修改版本号来让用户清除。
	```
		"version": "2.4.34",
	```	

#### x86分支  

<template>
<div>
	<el-form ref="formRef" label-position="left" label-width="150" :model="formModel">
		<div v-for="(item, index) in diskData" :key="'disk'+index">
			<el-form-item :prop="'disk' + index" :rules="[diskRules(index)]">
				<template #label>
					<div class="vm-new-label" @click="item.typeShow = !item.typeShow">
						<el-icon><ArrowDown v-show="item.typeShow" /><ArrowRight v-show="!item.typeShow" /></el-icon>
						<span>{{ index == 0 ? '磁盘' : '磁盘' + index }}</span>
					</div>
				</template>
				<el-input v-model="item.disk" type="number" :disabled="item.source == 'existing'">
					<template #append>
						<el-select v-model="item.unit" style="width: 80px" :disabled="item.source == 'existing'">
							<el-option label="KB" value="KB" />
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
						<el-button v-if="item.remove" @click="removeDisk(index)" :icon="Delete" style="margin-left: 20px" />
					</template>
				</el-input>
			</el-form-item>
			<div v-show="item.typeShow">
				<el-form-item label="总线类型">
					<el-select v-model="item.bus" style="width: 100%">
						<el-option label="高速硬盘" value="high" />
						<el-option label="IDE硬盘" value="ide" />
						<el-option label="SCSI硬盘" value="scsi" />
						<el-option label="SATA硬盘" value="sata" />
						<el-option label="USB硬盘" value="usb" />
					</el-select>
				</el-form-item>
				<el-form-item label="缓存方式">
					<el-select v-model="item.cache" style="width: 100%">
						<el-option label="directsync" value="directsync" />
						<el-option label="writethrough" value="writethrough" />
						<el-option label="writeback" value="writeback" />
						<el-option label="none" value="none" />
					</el-select>
				</el-form-item>
				<el-form-item label="系统类型">
					<el-radio-group v-model="item.source">
						<el-radio value="newbuilt" border>新建磁盘</el-radio>
						<el-radio value="existing" border>现有磁盘</el-radio>
					</el-radio-group>
				</el-form-item>
				<!-- 新建磁盘 -->
				<div v-if="item.source == 'newbuilt'">
					<el-form-item label="目标存储池" :prop="'newPoolName' + index" :rules="[newDiskNameRules(index)]">
						<el-input v-model="item.newPoolName" disabled>
              <template #append>
								<el-button @click="selectStoragePool(index)" :icon="Search" />
							</template>
            </el-input>
					</el-form-item>
					<el-form-item label="磁盘名称" :prop="'newDiskName ' + index" :rules="[poolNameRules(index)]">
						<el-input v-model="item.newDiskName"/>
					</el-form-item>
					<el-form-item label="存储格式">
						<el-select v-model="item.newFormat" style="width: 100%">
							<el-option v-for="em in formData.formatData" :key="em.name" :label="em.name" :value="em.name"/>
							<!-- <el-option label="qcow2" value="qcow2" />
							<el-option label="gs" value="gs" /> -->
						</el-select>
					</el-form-item>
					<el-form-item label="置备类型">
						<el-select v-model="item.preparation" style="width: 100%">
							<el-option label="精简置备" value="jj" />
							<el-option label="厚置备延迟置零" value="yc" />
							<el-option label="厚置备置零" value="zl" />
						</el-select>
					</el-form-item>
				</div>
				<!-- 现有磁盘 -->
				<div v-if="item.source == 'existing'">
					<el-form-item label="磁盘名称" :prop="'haveName' + index" :rules="[haveNameRules(index)]">
						<el-input v-model="item.existingDiskName" disabled placeholder="选择现有磁盘">
							<template #append>
								<el-button @click="selectDisk(index)" :icon="Search" />
							</template>
						</el-input>
					</el-form-item>
				</div>
				<el-form-item></el-form-item>
				<el-form-item label="开启共享">
					<el-switch v-model="item.share" inline-prompt active-text="开启" inactive-text="关闭" />
				</el-form-item>
				<el-form-item label="IO悬挂超时时长">
					<el-input v-model="item.timeout" :min="1" type="number">
						<template #append>秒</template>
					</el-input>
				</el-form-item>
				<el-form-item label="备注">
					<el-input v-model="item.distNotes" :rows="3" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息" />
				</el-form-item>
        <el-form-item></el-form-item>
			</div>
		</div>
	</el-form>
	<SelectStoragePool :storageTime="formData.storageTime" :treeItem="props.treeItem" @storage-return="storageReturn"></SelectStoragePool>
	<SelectExisting :existingTime="formData.existingTime" :treeItem="props.treeItem" @existing-return="existingReturn"></SelectExisting>
</div>

</template>
<script lang="ts" setup name="AddDisk">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search,Delete } from '@element-plus/icons-vue'
import { diskFormat } from '/@/api/ResourcePool/storage.js'; // 接口

const SelectStoragePool = defineAsyncComponent(() => import('./SelectStoragePool.vue'));
const SelectExisting = defineAsyncComponent(() => import('./SelectExisting.vue'));

const props = defineProps({
	treeItem: {
    type: Object,
    required: true
  },
  diskAdd: {
		type: Number,
		required: true,
	},
	diskTime: {
		type: String,
		required: true,
	},
});
const formModel = ref({}); // 可以动态填充你的表单数据
const formRef = ref<FormInstance>();
const emit = defineEmits(['diskOK']);
const formData = reactive({
	storageTime: '',
	existingTime: '',
	diskIndex: 0,
	formatData: [],
})
// 查询存储格式
const formatQuery = () => {
  diskFormat().then((res:any)=>{
    formData.formatData = res
  })
}
formatQuery()
// 硬件信息-磁盘
let diskData = reactive([
  {
    typeShow: false,
    remove: false,
    disk: 20,
    unit: 'GB',
    bus: 'high',
    cache: 'none',
    source: 'newbuilt',
    newPoolName: '',
    newDiskName: '',
		newPath: '',
    newFormat: 'qcow2',
    preparation: 'jj',
    existingPoolName: '',
    existingDiskName: '',
    existingPath: '',
		existingType: '',
    share: false,
    timeout: 120,
    distNotes: '',
  }
])
// 目标存储池
const selectStoragePool = (item: any)=>{
	formData.storageTime = " "+new Date()
	formData.diskIndex = item
}
// 目标存储池选中
const storageReturn = (item: any)=>{
  diskData[formData.diskIndex].newPoolName = item.tableName
  diskData[formData.diskIndex].newPath = item.tablePath
}

// 现有磁盘选择
const selectDisk = (item: any)=>{
	formData.existingTime = " "+new Date()
	formData.diskIndex = item
}
// 现有磁盘选择选中
const existingReturn = (item: any)=>{
  diskData[formData.diskIndex].existingPoolName = item.poolName
  diskData[formData.diskIndex].existingDiskName = item.tableName
  diskData[formData.diskIndex].existingPath = item.tablePath
  diskData[formData.diskIndex].existingType = item.tableType
  diskData[formData.diskIndex].disk = item.tableSize.match(/\d+/g)[0]
  diskData[formData.diskIndex].unit = item.tableSize.match(/[a-zA-Z]+/g)[0];
}
// 判断磁盘输入
const diskRules=(index:number)=>{
  let regex = /^[1-9]\d*$/;
  return {
    validator: (rule: any, value: any, callback: any) => {
      if (!regex.test(diskData[index].disk.toString())) {
        callback(new Error("请输入正确的磁盘容量"));
      } else {
        callback();
      }
    },
    trigger: "change", // 校验触发时机
  };
}
// 判断存储池是否为空
const newDiskNameRules=(index:number)=>{
  return {
    validator: (rule: any, value: any, callback: any) => {
      if (diskData[index].newPoolName=="") {
        callback(new Error("未选择存储池"));
      } else {
        callback();
      }
    },
    trigger: "change", // 校验触发时机
  };
}
// 判断新建磁盘是否为空
const poolNameRules=(index:number)=>{
  const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/
  return {
    validator: (rule: any, value: any, callback: any) => {
      if (!regex.test(diskData[index].newDiskName)) {
        callback(new Error("2-32 个中文、英文、数字、特殊字符@_.-"));
      } else {
        callback();
      }
    },
    trigger: "change", // 校验触发时机
  };
}
// 判断现有磁盘是否为空
const haveNameRules=(index:number)=>{
  return {
    validator: (rule: any, value: any, callback: any) => {
      if (diskData[index].existingDiskName=="") {
        callback(new Error("未选择磁盘"));
      } else {
        callback();
      }
    },
    trigger: "change", // 校验触发时机
  };
}
watch(
  ()=> props.diskTime,
  async (val)=>{
    if (formRef.value) {
      try {
        // 执行表单验证
        await formRef.value.validate();
        // 如果验证通过，触发事件
        emit('diskOK', diskData);  // 取消注释以触发事件
      } catch (error) {
        // 如果验证失败，标记失败项并输出错误
        diskData.forEach((item, index) => {
          // 这里可以增加对特定字段的验证，标记 `typeShow` 为 `true`
					emit('diskOK', false)
          item.typeShow = true;
        });
      }
    }
  } 
);
watch(
	() => props.diskAdd,
	(val) => {
		diskData.push({
      typeShow: false,
      remove: true,
      disk: 20,
      unit: 'GB',
      bus: 'high',
      cache: 'none',
      source: 'newbuilt',
      newPoolName: '',
      newDiskName: '',
      newPath: '',
      newFormat: 'qcow2',
      preparation: 'jj',
      existingPoolName: '',
      existingDiskName: '',
      existingPath: '',
			existingType: '',
      share: false,
      timeout: 120,
      distNotes: '',
    })
	}
);
// 删除磁盘
const removeDisk = (item: any)=>{
  diskData.splice(item,1)
}
</script>
<style lang="scss" scoped>
.vm-new-label {
	display: flex;
	align-items: center;
	cursor: pointer;
	> span {
		padding-left: 10px;
	}
}
</style>
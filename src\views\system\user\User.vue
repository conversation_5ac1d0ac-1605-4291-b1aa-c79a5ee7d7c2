<template>
	<div class="system-area layout-padding">
		<el-card>
      <div class="system-content-area">
        <div class="tabs-btn-area">
          <el-button type="primary" plain @click="refresh">刷新</el-button>
          <el-button type="primary" plain @click="newClick">新建用户</el-button>
          <el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<!-- 密码有效期 -->
						<template #expiredday="{ row }">
              <span v-if="tableDisabled(row.username)">永久有效</span>
              <span v-else>{{ row.expiredday }}</span>
						</template>
            <!-- 角色 -->
						<template #role_name="{ row }">
              {{ translationRole(row.role_name) }}
						</template>
            <!-- 用户状态 -->
            <template #status="{ row }">
              <span v-if="tableDisabled(row.username)">-</span>
              <el-switch v-else v-model="row.status" inline-prompt active-text="启用" inactive-text="禁用" :before-change="() => beforeChange(row)"/>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
							<el-dropdown trigger="click" @command="commandItem($event,row)">
								<el-button type="primary">操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="czmm">重置密码</el-dropdown-item>
										<el-dropdown-item :disabled='tableDisabled(row.username)' command="xgyh">修改用户</el-dropdown-item>
										<!-- <el-dropdown-item command="qxsz">权限设置</el-dropdown-item> -->
										<el-dropdown-item :disabled='tableDisabled(row.username)' command="sc" style="color:red" divided>删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</template>
          </my-table>
        </div>
      </div>
		</el-card>
    <UserNew :newTime="state.newTime" @returnOK="returnOK"></UserNew>
    <UserEdit :editTime="state.editTime" :tableRow="state.tableRow" @returnOK="returnOK"></UserEdit>
    <UserPermission :permissionTime="state.permissionTime" :tableRow="state.tableRow"></UserPermission>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts" name="User">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox,ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
import { defaultTime } from '/@/model/logManage.ts';
import { userColumns,translationRole,tableDisabled } from '/@/model/system.ts'; // 表列、正则
import { userQuery,userStatus,userReset,userDelete } from '/@/api/System'; // 接口
const UserNew = defineAsyncComponent(() => import('../user/UserNew.vue'));
const UserEdit = defineAsyncComponent(() => import('../user/UserEdit.vue'));
const UserPermission = defineAsyncComponent(() => import('../user/UserPermission.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const searchRef = ref();
// 定义变量内容
const state = reactive({
  columns: userColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
  tableSelect: [],
  selectRow: [],
  tableRow: {},
  newTime: '',
  editTime: '',
  permissionTime: '',
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  return new Promise(async(resolve)=>{
    userQuery().then((res:any)=>{
      resolve({
        data: res, // 数据
        total: res.length // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 新建用户
const newClick = ()=>{
  state.newTime = ''+new Date()
}
// 表操作列
const commandItem = (item: string,row:any)=>{
	state.tableRow = row
  switch (item) {
    case 'czmm':
			ElMessageBox.confirm(
        `是否对 <span style="font-weight: 800">${row.username}</span> 登录账号进行重置密码操作？`,
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
      .then(() => {
        userReset({username: row.username})
        .then(res=>{
          if(res.msg == 'ok'){
            ElMessage.success('重置密码成功')
          }else {
            ElMessage.error('重置密码失败')
          }
        })
      })
      .catch(() => {})
      break;
		case 'xgyh':
      state.editTime = ''+new Date()
      break;
		case 'qxsz':
      state.permissionTime = ''+new Date()
      break;
		case 'sc':
      deleteClick([row])
      break;
  }
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 状态切换
const beforeChange = (row:any)=>{
  return new Promise<boolean>((resolve) => {
    let statusText = row.status ? '禁用' : '启用';
    let statusColor = row.status ? 'red' : 'green';
    ElMessageBox.confirm(
      `是否修改用户 <span style="font-weight: 800">${row.username}</span> 状态为 <span style="color:${statusColor}">${statusText}</span>？`,
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    .then(() => {
      userStatus({username:row.username,status:row.status?'off':'on'})
      .then(res=>{
        if(res.msg == 'ok'){
          ElMessage.success('切换完成')
          resolve(true); 
        }else {
          ElMessage.error('修改用户状态失败')
        }
      })
    })
    .catch(() => {})
  })
}
// 删除用户
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }
  else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '用户/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    userDelete({
      usernames: formDelet.tableNames,
      userids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除用户操作完成');
      }else {
        ElMessage.error('删除用户操作失败');
      }
    })
  }else {
    refresh()
  }
}

onMounted(() => {})
</script>
<style scoped lang="scss">
.system-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .system-content-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>

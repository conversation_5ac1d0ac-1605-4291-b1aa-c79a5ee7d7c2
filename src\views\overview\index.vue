<template>
	<div class="overview-area layout-padding">
    <!-- 物理机、虚拟机、存储总量 -->
		<div class="overview-summary">
      <div class="overview-summary-item" v-for="item in state.summaryData" :key="item.name" :style="{backgroundImage:`url(${item.bg})`}">
        <div class="overview-summary-item-text">
          <span>{{ item.name }}（ {{ item.unit!=='台'?byteUnitConversion('unit',item.number):item.unit }}）</span>
          <span class="overview-summary-item-number">{{ byteUnitConversion('byte',item.number) }}</span>
        </div>
        <img :src="item.imgs" alt="">
      </div>
    </div>
    <!-- 使用率 -->
    <div class="overview-circle">
      <!-- cpu -->
      <div class="overview-circle-usage-rate">
        <div class="overview-circle-title">
          <p class="usage-rate-title">
            <span class="usage-rate-area">
              <span :class="state.cpuSY" @click="cpuSwitch('sy')">CPU使用率</span>
              <span :class="state.cpuFP" @click="cpuSwitch('fp')">CPU分配比</span>
            </span>
          </p>
          <span>
            <el-tooltip>
              <el-icon :size="20" color="#ccc"><QuestionFilled /></el-icon>
              <template #content>
                <p>CPU使用率：显示集群中的物理CPU平均使用率</p>
                <p>CPU分配比：集群中所有虚拟机已分配的CPU核数占集群所有CPU总核数的比例</p>
                <p>物理CPU总数(核)：显示集群中的物理CPU总核数，即各主机物理CPU核数数量总和</p>
                <p>VCPU总数(核)：显示集群中的虚拟CPU总核数，即各主机虚拟CPU核数数量总和</p>
                <p>已分配VCPU(核)：显示集群中已经分配给虚拟机使用的虚拟CPU总核数</p>
              </template>
            </el-tooltip>
          </span>
        </div>
        <div class="overview-circle-content">
          <el-progress :stroke-width="15" :width="200" type="circle" :percentage="state.cpuSY=='ordinary'?state.cpuPercentageRate:state.cpuPercentageThan" />
          <div class="overview-circle-text">
            <div v-if="state.cpuSY == 'occupy'">
              <h3>{{ state.cpuUsedNumberThan }}</h3>
              <p>已分配VCPU (核)</p>
            </div>
            <div>
              <h3>{{ state.cpuSY == 'ordinary'?state.cpuTotalNumberRate:state.cpuTotalNumberThan }}</h3>
              <p>{{ state.cpuSY == 'ordinary'?'物理CPU总数 (核)':'VCPU总数 (核)' }}</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 内存 -->
      <div class="overview-circle-usage-rate">
        <div class="overview-circle-title">
          <p class="usage-rate-title">
            <span class="usage-rate-area">
              <span :class="state.memSY" @click="memSwitch('sy')">内存使用率</span>
              <span :class="state.memFP" @click="memSwitch('fp')">内存分配比</span>
            </span>
          </p>
          <span>
            <el-tooltip>
              <el-icon :size="20" color="#ccc"><QuestionFilled /></el-icon>
              <template #content>
                <p>已使用内存：显示集群中已使用的物理内存</p>
                <p>内存总数：显示集群中物理内存总容量</p>
                <p>已分配内存：显示集群中已经分配给虚拟机使用的内存总数</p>
                <p>内存分配比：已分配内存/内存总数 × 100%</p>
                <p>内存使用率：显示集群中的物理内存平均使用率</p>
              </template>
            </el-tooltip>
          </span>
        </div>
        <div class="overview-circle-content">
          <el-progress :stroke-width="15" :width="200" type="circle" :percentage="state.memSY=='ordinary'?state.memPercentageRate:state.memPercentageThan" />
          <div class="overview-circle-text">
            <div>
              <h3>{{ state.memSY=='ordinary'?byteUnitConversion('byte',state.memUsedNumberRate):byteUnitConversion('byte',state.memUsedNumberThan) }}</h3>
              <p>{{ state.memSY == 'ordinary'?'已使用内存':'已分配内存'}}（{{state.memSY=='ordinary'?byteUnitConversion('unit',state.memUsedNumberRate):byteUnitConversion('unit',state.memUsedNumberThan)}}）</p>
            </div>
            <div>
              <h3>{{ state.memSY=='ordinary'?byteUnitConversion('byte',state.memTotalNumberRate):byteUnitConversion('byte',state.memTotalNumberThan) }}</h3>
              <p>内存总数（{{ state.memSY=='ordinary'?byteUnitConversion('unit',state.memTotalNumberRate):byteUnitConversion('unit',state.memTotalNumberThan) }}）</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 存储 -->
      <div class="overview-circle-usage-rate">
        <div class="overview-circle-title">
          <p class="usage-rate-title">
            <span class="usage-rate-area">
              <span>存储使用率</span>
            </span>
          </p>
          <span>
            <el-tooltip>
              <el-icon :size="20" color="#ccc"><QuestionFilled /></el-icon>
              <template #content>
                <p>已用存储：显示集群中已使用的存储总量</p>
                <p>存储总量:显示集群中可使用的存储总量</p>
                <p>存储使用率：已用存储/存储总量 × 100%</p>
              </template>
            </el-tooltip>
          </span>
        </div>
        <div class="overview-circle-content">
          <el-progress :stroke-width="15" :width="200" type="circle" :percentage="state.stoPercentageRate" />
          <div class="overview-circle-text">
            <div>
              <h3>{{ byteUnitConversion('byte',state.stoUsedNumber) }}</h3>
              <p>已用存储（{{ byteUnitConversion('unit',state.stoUsedNumber) }}）</p>
            </div>
            <div>
              <h3>{{ byteUnitConversion('byte',state.stoTotalNumber) }}</h3>
              <p>存储总量（{{byteUnitConversion('unit',state.stoTotalNumber)}}）</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 图表 -->
    <div class="overview-chart">
      <div class="overview-chart-dashboard">
        <div class="overview-circle-title">
          <p class="usage-rate-title">
            <span class="usage-rate-area">
              <span @click="getHomeData">系统状态</span>
            </span>
          </p>
          <span>
            <el-tooltip>
              <el-icon :size="20" color="#ccc"><QuestionFilled /></el-icon>
              <template #content>
                <p>系统监控页面中的系统健康度是对系统整体运行状况的一种量化评估，用于反映系统当前的状态是否稳定、可靠，以及是否存在潜在的性能或故障问题</p>
              </template>
            </el-tooltip>
          </span>
        </div>
        <div class="overview-chart-data">
          <SystemStateModule :systemNumber="state.systemNumber"></SystemStateModule>
        </div>
      </div>
      <div class="overview-chart-table">
        <div class="overview-circle-title">
          <p class="usage-rate-title">
            <span class="usage-rate-area">
              <span>告警提醒</span>
            </span>
          </p>
        </div>
        <div class="overview-circle-table-content">
          <AlarmModule></AlarmModule>
        </div>
      </div>
    </div>
	</div>
</template>

<script setup lang="ts" name="Overview">
	import { defineAsyncComponent, reactive, onMounted, nextTick, ref,markRaw } from 'vue';
	import { Search } from '@element-plus/icons-vue';
	import { dayjs } from 'element-plus';
  // 引入组件
  const SystemStateModule = defineAsyncComponent(() => import('./SystemStateModule.vue'));
  const AlarmModule = defineAsyncComponent(() => import('./AlarmModule.vue'));
  import { homeDefaultQuery,largeScreenMachineNumber,alarmDataQuery  } from '/@/api/Overview';
  
  import mkwlj from '../../assets/home/<USER>';
  import mkxnj from '../../assets/home/<USER>';
  import mkrws from '../../assets/home/<USER>';
  import bgwlj from '../../assets/home/<USER>';
  import bgxnj from '../../assets/home/<USER>';
  import bgrws from '../../assets/home/<USER>';
	// 引入组件
	// 定义变量内容
	const state = reactive({
		summaryData: [
      { name: '物理机数量', unit: '台', number: 0, imgs: mkwlj, bg: bgwlj },
      { name: '虚拟机数量', unit: '台', number: 0, imgs: mkxnj, bg: bgxnj },
      { name: '存储总量', unit: 'GB', number: 0, imgs: mkrws, bg: bgrws },
    ],
    cpuPercentageRate: 0, // 率百分比
    cpuTotalNumberRate: 0, // 物理CPU总数-率
    cpuPercentageThan: 0, // 比百分比
    cpuUsedNumberThan: 0,  // 已分配VCPU-比
    cpuTotalNumberThan: 0, // VCPU总数-比

    memPercentageRate: 0, // 率百分比
    memUsedNumberRate: 0, // 已使用内存-率
    memTotalNumberRate: 0, // 内存总数-率
    memPercentageThan: 0, // 比百分比
    memUsedNumberThan: 0, // 已分配内存-比
    memTotalNumberThan: 0, // 内存总数-比

    stoPercentageRate: 0, // 率百分比
    stoUsedNumber : 0, // 已用存储
    stoTotalNumber : 0, // 存储总量
    systemNumber: 1, // 系统状态

    cpuSY: 'ordinary',
    cpuFP: 'occupy',
    memSY: 'ordinary',
    memFP: 'occupy',
    
	});
	const getHomeData = () => {
    homeDefaultQuery().then(res=>{
      state.summaryData[0].number = res.count
      state.summaryData[1].number = res.running_vms
      state.summaryData[2].number = res.ceph_pool_max

      state.cpuPercentageRate = Math.floor(res.usage_percent_cpu*100) // 率百分比
      state.cpuTotalNumberRate = res.vcpus // 物理CPU总数-率
      state.cpuPercentageThan = Math.floor(res.vcpus_used/(res.vcpus*4)*100) // 比百分比
      state.cpuUsedNumberThan = res.vcpus_used // 已分配VCPU-比
      state.cpuTotalNumberThan = res.vcpus*4 // VCPU总数-比

      state.memPercentageRate = Math.floor(res.usage_percent_ram*100) // 率百分比
      state.memUsedNumberRate = res.usage_ram_used/1024 // 已使用内存-率
      state.memTotalNumberRate = res.usage_ram_total/1024 // 内存总数-率
      state.memPercentageThan = Math.floor(res.memory_mb_used/res.memory_mb *100) // 比百分比
      state.memUsedNumberThan = res.memory_mb_used/1024 // 已分配内存-比
      state.memTotalNumberThan = res.memory_mb/1024 // 内存总数-比

      state.stoPercentageRate = Math.floor(res.ceph_pool_stored/res.ceph_pool_max*100) //  率百分比
      state.stoUsedNumber = res.ceph_pool_stored // 已用存储
      state.stoTotalNumber = res.ceph_pool_max // 已用存储
      state.systemNumber = Math.floor(res.sys_status*100)
      // largeScreenMachineNumber().then(vm=>{
      //   state.summaryData[1].number = vm.vms
      // })
    })
	};
  // cpu切换标题
  const cpuSwitch = (val: string) => {
    if(val === 'sy'){
      state.cpuSY ='ordinary'
      state.cpuFP ='occupy'
    }else {
      state.cpuSY ='occupy'
      state.cpuFP ='ordinary'
    }
  };
  // 内存切换标题
  const memSwitch = (val: string) => {
    if(val === 'sy'){
      state.memSY ='ordinary'
      state.memFP ='occupy'
    }else {
      state.memSY ='occupy'
      state.memFP ='ordinary'
    }
  };
  // 字节+单位转换
  const byteUnitConversion=(type:string,size:number) =>{
    const units = ['GB', 'TB', 'PB'];
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    if(type=="byte") {
      return Math.floor(size * 100) / 100 
    }else if(type=="unit") {
      return units[unitIndex] 
    }else {
      return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
    }
  };
	// 页面加载时
	onMounted(() => {
    getHomeData()
    // window.addEventListener('resize', () => {
        // state.chartOption = getOption();
    // })
  });
</script>

<style scoped lang="scss">
	.overview-area {
		width: calc(100%);
		height: calc(100%);
		// min-width: 1166px;
		// min-height: 600px;
		// padding: 15px;
		// position: absolute;
		// overflow: auto;
    // display: flex;
    // flex-direction: column;
    // justify-content: space-between;
    .overview-summary {
		  height: 200px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .overview-summary-item {
        // width: 540px;
        width: 615px;
        height: 200px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .overview-summary-item-text {
          display: flex;
          flex-direction: column;
          font-size: 18px;
          color: var(--next-bg-color);
          .overview-summary-item-number {
            padding-top: 15px;
            font-size: 30px;
          }
        }
      }
      img {
        height: 80%;
      }
    }
    .overview-circle {
      height: 320px;
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .overview-circle-usage-rate {
        // width: 540px;
        width: 615px;
        height: 320px;
        background: var(--next-color-white);
        border-radius: 15px;
        padding: 15px;
        .overview-circle-title {
          height: 30px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: relative;
          .usage-rate-title::before {
            content: " ";
            position: absolute;
            width: 4px;
            height: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--el-color-primary);
            left: 0;
          }
          .usage-rate-area {
            padding-left: 15px;
            .ordinary {
              cursor: pointer;
              margin-right: 10px;
              color: var(--el-color-primary);
              display: inline-block;
              background-image: url(../../assets/home/<USER>
              background-size: 100% 100%;
              line-height: 30px;
            }

            .occupy {
              cursor: pointer;
              margin-right: 10px;
            }
          }
        }
        .overview-circle-content {
          height: 260px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          .overview-circle-text {
            width: 180px;
            >div{
              width: 180px;
              h3 {
                text-align: center;
                font-size: 30px;
                font-weight: 400;
              }
              p {
                text-align: center;
                padding: 5px 0 20px 0 ;
                color: var(--menu-text-color);
                font-size: 18px;
              }
            }
          }
        }
      }
    }
    .overview-chart {
      height: 350px;
      display: flex;
      justify-content: space-between;
      .overview-circle-title {
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        .usage-rate-title::before {
          content: " ";
          position: absolute;
          width: 4px;
          height: 20px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--el-color-primary);
          left: 0;
        }
        .usage-rate-area {
          padding-left: 15px;
        }
      }
      .overview-chart-dashboard {
        // width: 540px;
        width: 615px;
        height: 340px;
        background: var(--next-color-white);
        border-radius: 15px;
        padding: 15px;
        .overview-chart-data {
          width: 100%;
          height: calc(100% - 30px);
        }
      }
      .overview-chart-table {
        // width: 1100px;
        width: calc(100% - 640px);
        height: 340px;
        background: var(--next-color-white);
        border-radius: 15px;
        padding: 15px;
        .overview-circle-table-content {
          width: 100%;
          height: 280px;
          position: relative;
        }
      }
    }
	}
  .el-icon {
    font-size: 20px!important;
  }
</style>

<!DOCTYPE html>
<html>
<head>
    <title>测试无限滚动表格</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .table-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: auto;
            background: white;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .table th {
            background: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .table tr:hover {
            background: #f5f5f5;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        .no-more {
            text-align: center;
            padding: 15px;
            color: #999;
            background: #f5f7fa;
            border-top: 1px solid #eee;
            font-size: 13px;
        }
        .controls {
            margin-bottom: 15px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log {
            height: 150px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 无限滚动表格测试</h1>
        
        <div class="demo-section">
            <h3>📋 功能说明</h3>
            <ul>
                <li>✅ 滚动到表格底部80%时自动加载更多数据</li>
                <li>✅ 显示加载状态和完成状态</li>
                <li>✅ 支持重置和手动加载</li>
                <li>✅ 模拟真实的分页数据加载</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>🎮 控制面板</h3>
            <div class="controls">
                <button onclick="resetTable()">重置表格</button>
                <button onclick="loadMore()" id="loadBtn">手动加载更多</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="status">
                <div>当前页码: <span id="currentPage">1</span></div>
                <div>已加载数据: <span id="loadedCount">0</span> 条</div>
                <div>加载状态: <span id="loadingStatus">就绪</span></div>
                <div>是否还有更多: <span id="hasMore">是</span></div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 无限滚动表格</h3>
            <div class="table-container" id="tableContainer">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>时间</th>
                            <th>用户名</th>
                            <th>操作</th>
                            <th>结果</th>
                            <th>消息</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- 数据将在这里动态插入 -->
                    </tbody>
                </table>
                <div id="loadingIndicator" class="loading" style="display: none;">
                    正在加载更多数据...
                </div>
                <div id="noMoreIndicator" class="no-more" style="display: none;">
                    已加载全部数据
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📝 操作日志</h3>
            <div id="log" class="log">等待操作...</div>
        </div>
    </div>

    <script>
        // 模拟数据状态
        let currentPage = 1;
        let pageSize = 20;
        let totalRecords = 500;
        let loadedData = [];
        let isLoading = false;
        let hasMoreData = true;

        // 日志功能
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '日志已清空...\n';
        }

        // 更新状态显示
        function updateStatus() {
            document.getElementById('currentPage').textContent = currentPage;
            document.getElementById('loadedCount').textContent = loadedData.length;
            document.getElementById('loadingStatus').textContent = isLoading ? '加载中...' : '就绪';
            document.getElementById('hasMore').textContent = hasMoreData ? '是' : '否';
            document.getElementById('loadBtn').disabled = isLoading || !hasMoreData;
        }

        // 生成模拟数据
        function generateData(page, size) {
            const actions = ['login', 'logout', 'create', 'update', 'delete', 'view'];
            const results = ['成功', '失败'];
            const users = ['admin', 'user1', 'user2', 'operator', 'guest'];
            
            const data = [];
            const startIndex = (page - 1) * size;
            
            for (let i = 0; i < size; i++) {
                const index = startIndex + i;
                if (index >= totalRecords) break;
                
                const randomDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
                const action = actions[Math.floor(Math.random() * actions.length)];
                const result = results[Math.floor(Math.random() * results.length)];
                
                data.push({
                    id: `log_${index + 1}`,
                    time: randomDate.toLocaleString(),
                    username: users[Math.floor(Math.random() * users.length)],
                    action: action,
                    result: result,
                    message: `${action}操作${result} - 第${index + 1}条日志`
                });
            }
            
            return data;
        }

        // 渲染表格数据
        function renderTableData(data, append = false) {
            const tbody = document.getElementById('tableBody');
            
            if (!append) {
                tbody.innerHTML = '';
            }
            
            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.id}</td>
                    <td>${item.time}</td>
                    <td>${item.username}</td>
                    <td>${item.action}</td>
                    <td><span style="color: ${item.result === '成功' ? 'green' : 'red'}">${item.result}</span></td>
                    <td>${item.message}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示/隐藏加载指示器
        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
        }

        function showNoMore(show) {
            document.getElementById('noMoreIndicator').style.display = show ? 'block' : 'none';
        }

        // 加载更多数据
        async function loadMore() {
            if (isLoading || !hasMoreData) {
                addLog('⏸️ 跳过加载：正在加载中或没有更多数据');
                return;
            }

            addLog(`📥 开始加载第${currentPage}页数据`);
            isLoading = true;
            showLoading(true);
            updateStatus();

            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 800));

            try {
                const newData = generateData(currentPage, pageSize);
                
                if (newData.length > 0) {
                    loadedData.push(...newData);
                    renderTableData(newData, true);
                    currentPage++;
                    
                    // 检查是否还有更多数据
                    hasMoreData = loadedData.length < totalRecords;
                    
                    addLog(`✅ 成功加载${newData.length}条数据，总计${loadedData.length}条`);
                } else {
                    hasMoreData = false;
                    addLog('📄 没有更多数据了');
                }
                
                if (!hasMoreData) {
                    showNoMore(true);
                }
                
            } catch (error) {
                addLog(`❌ 加载失败: ${error.message}`);
            } finally {
                isLoading = false;
                showLoading(false);
                updateStatus();
            }
        }

        // 重置表格
        function resetTable() {
            addLog('🔄 重置表格');
            currentPage = 1;
            loadedData = [];
            hasMoreData = true;
            isLoading = false;
            
            document.getElementById('tableBody').innerHTML = '';
            showLoading(false);
            showNoMore(false);
            updateStatus();
            
            // 自动加载第一页
            loadMore();
        }

        // 滚动监听
        function setupScrollListener() {
            const container = document.getElementById('tableContainer');
            
            container.addEventListener('scroll', () => {
                const { scrollTop, scrollHeight, clientHeight } = container;
                const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
                
                // 当滚动到底部80%时触发加载
                if (scrollPercentage > 0.8 && !isLoading && hasMoreData) {
                    addLog(`🔽 触发滚动加载，滚动百分比: ${Math.round(scrollPercentage * 100)}%`);
                    loadMore();
                }
            });
            
            addLog('📋 滚动监听器已设置');
        }

        // 页面初始化
        window.addEventListener('DOMContentLoaded', function() {
            addLog('📄 页面加载完成');
            setupScrollListener();
            updateStatus();
            
            // 自动加载第一页数据
            setTimeout(() => {
                resetTable();
            }, 500);
        });
    </script>
</body>
</html>

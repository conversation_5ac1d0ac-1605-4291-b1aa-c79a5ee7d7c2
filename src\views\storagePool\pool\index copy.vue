<template>
	<div class="pool-tree-area">
		<div class="pool-btn">
			<el-tooltip effect="light" content="新建分组" placement="top">
				<el-button type="primary" circle @click="treeData" :icon="CirclePlus"></el-button>
			</el-tooltip>
			<el-tooltip effect="light" content="编辑分组" placement="top">
				<el-button type="primary" circle @click="treeData" :icon="Edit"></el-button>
			</el-tooltip>
			<el-tooltip effect="light" content="刷新分组" placement="top">
				<el-button type="primary" circle @click="treeData" :icon="RefreshRight"></el-button>
			</el-tooltip>
			<el-tooltip effect="light" content="删除分组" placement="top">
				<el-button type="primary" circle @click="treeData" :icon="Delete"></el-button>
			</el-tooltip>
			<!-- <el-input placeholder="搜索节点" v-model="state.searchValue" @input="onSearch"></el-input> -->
		</div>
		<div class="tree-area">
			<ul id="treeDemo" class="ztree"></ul>
		</div>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { CirclePlus, Edit, RefreshRight, Delete } from '@element-plus/icons-vue'; // ICON
import { dayjs } from 'element-plus';
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
const state = reactive({
	treeItem: {
		id: '1',
	},
	searchValue: '',
});
const emit = defineEmits(['poolOK']);
import ic0 from '/@/assets/resource/title.jpg';
import ic1 from '/@/assets/resource/cc.jpg';
import ic2 from '/@/assets/resource/jq.png';
import ic3 from '/@/assets/resource/wlj.jpg';
import ic4 from '/@/assets/resource/xnj.jpg';
// 定义图标
const customizeIcon = (treeId: string, treeNode: { tId: string; level: number }) => {
	let $icon = $('#' + treeNode.tId + '_ico');
	const icons = [ic0, ic1, ic2, ic3, ic4];
	const icon = icons[treeNode.level]; // 根据 level 获取对应图标
	if (icon) {
		$icon.css({
			background: `url(${icon}) no-repeat center center`,
			backgroundSize: '100% 100%',
		});
	}
};
// 点击节点
const onNodeClick = (event: any, treeId: string, treeNode: any) => {
	state.treeItem = treeNode;
	emit('poolOK', state.treeItem);
};

const setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: 'id',
			pIdKey: 'pid',
			rootPId: 0,
		},
	},
	view: {
		showIcon: false, // 显示图标
		addDiyDom: customizeIcon, // 自定义图标
		// expandSpeed: "fast",
		fontCss: (treeId: any, treeNode: any) => (treeNode.highlight ? { color: 'red', 'font-weight': 'bold' } : { color: '#333' }),
	},
	callback: {
		onClick: onNodeClick,
	},
};
interface Node {
	id: string;
	name: string;
	pid: string;
}
let zNodes: Node[] = [];
const treeData = (item: string) => {
	state.searchValue = '';
	if (true) {
		zNodes = [
			{ id: '1', name: '存储池', pid: '0' },
			{ id: '2', name: '存储分组', pid: '1' },
			{ id: '3', name: 'list', pid: '2' },
		];
		init(item);
	} else {
		resourceTreeQuery()
			.then((res) => {
				zNodes = res.data;
				init(item);
			})
			.catch((error) => {});
	}
};
const init = (item: string) => {
	let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
	zTreeObj.expandAll(true); // 默认展开所有树的分支
	let treeNode = zTreeObj.getNodeByParam('id', state.treeItem.id);
	if (item == 'delete') {
		treeNode = zTreeObj.getNodeByParam('id', zNodes[0].id);
	} else {
		treeNode = zTreeObj.getNodeByParam('id', state.treeItem.id);
	}
	// 高亮选中根节点
	zTreeObj.selectNode(treeNode);
	state.treeItem = treeNode;
	emit('poolOK', state.treeItem);
};
const onSearch = () => {
	let zTreeObj = window.$.fn.zTree.init($('#treeDemo'), setting, zNodes); // 初始化树形控件
	if (!zTreeObj) return;
	const value = state.searchValue.trim();
	const allNodes = zTreeObj.transformToArray(zTreeObj.getNodes());

	// 清除所有节点的高亮状态
	allNodes.forEach((node: any) => {
		node.highlight = false;
		zTreeObj.updateNode(node);
	});

	// 如果搜索值为空，直接返回
	if (!value) return;

	// 获取匹配的节点
	const matchedNodes = zTreeObj.getNodesByParamFuzzy('name', value);

	// 高亮匹配的节点并展开父节点
	matchedNodes.forEach((node: any) => {
		node.highlight = true;
		zTreeObj.updateNode(node);
		zTreeObj.expandNode(node.getParentNode(), true);
	});
};

// 页面加载时
onMounted(() => {
	setTimeout(() => {
		treeData('');
	}, 500);
});
</script>
<style scoped lang="scss">
.pool-tree-area {
	width: 100%;
	height: 100%;
	.pool-btn {
		display: flex;
		justify-content: space-evenly;
		margin-bottom: 15px;
	}
	.tree-area {
		width: 100%;
		height: calc(100% - 50px);
		overflow: auto;
	}
}
</style>
      
<template>
	<div class="securityroup-area layout-padding">
		<el-card>
			<div class="content-area">
				<div class="navi-area">
					<el-card>
						<div class="navi-btn">
							<el-button :icon="Plus" circle @click="editClick('新建/')" />
							<el-button :icon="Edit" circle @click="editClick('编辑/')" />
							<el-button :icon="RefreshRight" circle @click="getGroupData" />
							<el-button :icon="Delete" circle @click="removeClick" />
						</div>
						<div class="navi-content">
							<el-menu :default-active="state.defaultActive" @select="menuClick">
								<el-menu-item index="default"> default </el-menu-item>
								<el-menu-item v-for="item in state.groupData" :index="item.id" :key="item.id">{{ item.name }}</el-menu-item>
							</el-menu>
						</div>
					</el-card>
				</div>
				<div class="table-area">
					<div class="tabs-btn-area">
						<el-button type="primary" plain @click="refresh">刷新</el-button>
						<el-button type="primary" plain @click="newClick">新建规则</el-button>
						<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除规则</el-button>
					</div>
					<my-table
						ref="tableRef"
						:pagination="state.pagination"
						:searchParams="state.searchParams"
						:columns="state.columns"
						:request="getTableData"
						@selectionChange="selectChange"
					>
						<!-- 子网 -->
						<template #cidr="{ row }">
							<span>{{ row.cidr.toString() }}</span>
						</template>
						<!-- 操作 -->
						<template #operation="{ row }">
							<el-button type="primary" @click="deleteClick([row])">删除规则</el-button>
						</template>
					</my-table>
				</div>
			</div>
		</el-card>
		<GroupEdit :editTime="state.editTime" :editRow="state.editRow" @groupOK="groupOK"></GroupEdit>
		<TableNew :newTableTime="state.newTableTime" :editRow="state.editRow" @groupOK="groupOK"></TableNew>
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>
<script setup lang="ts" name="ClusterAlarm">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Plus, Edit, RefreshRight, Delete } from '@element-plus/icons-vue';
import { secureGroupColumns } from '/@/model/network.ts'; // 表列、正则
import { secureGroupQuery, secureGroupDelete, ruleTableQuery, ruleTableDelete } from '/@/api/Network'; // 接口
import { ElMessageBox, ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const GroupEdit = defineAsyncComponent(() => import('./GroupEdit.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

import { useRouter } from 'vue-router';
import { tr } from 'element-plus/es/locale';
const router = useRouter();
// 定义变量内容
const state = reactive({
	columns: secureGroupColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	defaultActive: 'default',
	groupData: [{ id: 'aadasd', name: '测试组' }],
	editTime: '',
	editRow: {
		id: 'default',
		name: 'default',
	},

	tableSelect: [],
	deleteTime: '',
	newTableTime: '',
});
interface FormDelet {
	tableNames: string[];
	tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
	tableNames: [],
	tableIDs: [],
};
// 导航数据获取
const getGroupData = () => {
	// return new Promise(async(resolve)=>{
	//   secureGroupQuery().then((res:any)=>{
	//     resolve({
	//       data: res.data, // 数据
	//     })
	//   }).catch((err:any) => {
	//     resolve({
	//       data: [], // 数据
	//     })
	//   })
	// })
  
	let libs = ['测试1', '测试2', '测试3', '其它'];
	let libs1 = ['测1', '测2', '测3', '其1'];
	state.groupData = [
		{ id: 'aaaaa', name: libs[Math.round(Math.random() * 3)] },
		{ id: 'ccccc', name: libs1[Math.round(Math.random() * 3)] },
	];
};
// 导航选择
const menuClick = (item: string) => {
	const selectedOption = state.groupData.find((option) => option.id === item);
	if (selectedOption) {
		state.defaultActive = item;
		state.editRow.id = item;
		state.editRow.name = selectedOption.name;
		refresh();
	}
};
// 组修改
const editClick = (item: string) => {
	state.editTime = item + new Date();
};
// 组删除
const removeClick = () => {
	ElMessageBox.confirm(`是否删除安全组 <span style="font-weight: 800">${state.editRow.name}</span>？`, {
		dangerouslyUseHTMLString: true,
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			secureGroupDelete({ name: state.editRow.name, id: state.editRow.id }).then((res) => {
				if (res.msg == 'ok') {
					state.defaultActive = 'default';
					getGroupData();
					ElMessage.success('删除安全组操作完成');
				} else {
					ElMessage.error('删除安全组操作失败');
				}
			});
		})
		.catch(() => {});
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = [];
	if (true) {
		let libs = ['测试', '临时', 'NEW', '其它'];
		const list = new Array(20).fill({}).map((item, index) => {
			item = {
				name: '网络: ' + libs[Math.round(Math.random() * 3)],
				cidr: ['192.168.1.1', '192.168.10.10'],
				status: index == 0 ? 'list' : 'active',
				network_type: 'vlan',
				vlanid: index == 0 ? null : Math.round(Math.random() * 1000),
				id: 'lists' + index,
			};
			return item;
		});

		return {
			data: list, // 数据
			total: 100, // 总数
		};
	}
	return new Promise(async (resolve) => {
		ruleTableQuery({})
			.then((res: any) => {
				resolve({
					data: res.data, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {
				resolve({
					data: [], // 数据
					total: 0, // 总数
				});
			});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 新建规则
const newClick = () => {
	state.newTableTime = '' + new Date();
};
// 返回数据
const groupOK = (item: any) => {
	getGroupData();
};
// 删除
const deleteClick = (arr: any) => {
	if (arr.length == 0) {
		ElMessage.warning('未选择数据');
	} else {
		let names: any[] = [];
		let ids: any[] = [];
		arr.forEach((item: any) => {
			names.push(item.name);
			ids.push(item.id);
		});
		formDelet.tableNames = names;
		formDelet.tableIDs = ids;
		state.deleteTime = '规则/' + new Date();
	}
};

// 返回数据
const returnOK = (item: any) => {
	if (item == 'delete') {
		ruleTableDelete({
			names: formDelet.tableNames,
			ids: formDelet.tableIDs,
		}).then((res) => {
			if (res.msg == 'ok') {
				refresh();
				ElMessage.success('删除规则操作完成');
			} else {
				ElMessage.error('删除规则操作失败');
			}
		});
	} else {
		refresh();
	}
};
onMounted(() => {});
</script>
<style scoped lang="scss">
.securityroup-area {
	padding-top: 0 !important;
	width: calc(100%);
	height: calc(100%);
	.content-area {
		display: flex;
		width: calc(100%);
		height: calc(100%);
		.navi-area {
			width: 230px;
			height: calc(100%);
			.navi-btn {
				width: 195px;
				height: 50px;
				display: flex;
				justify-content: space-evenly;
			}
			.navi-content {
				width: 195px;
				height: calc(100% - 50px);
			}
		}
		.table-area {
			.tabs-btn-area {
				height: 50px;
			}
			margin-left: 15px;
			width: calc(100% - 215px);
			height: calc(100% - 50px);
			position: relative;
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
.el-menu .el-menu-item.is-active {
	color: var(--next-color-white) !important;
}
.el-menu {
	width: 200px;
}
</style>
<template>
	<el-form ref="formRef" label-position="left" :model="formBasic" :rules="rulesForm" label-width="150">
		<el-form-item label="虚拟机名称" prop="name">
			<el-input v-model="formBasic.name" placeholder="请输入虚拟机名称" />
		</el-form-item>
		<el-form-item label="系统类型">
			<el-radio-group v-model="formBasic.systemType">
				<el-radio value="Linux">Linux</el-radio>
				<el-radio value="Windows">Windows</el-radio>
				<el-radio value="qita">其它</el-radio>
			</el-radio-group>
		</el-form-item>
    <el-form-item label="虚拟化类型">
			<el-radio-group v-model="formBasic.virtualizationType">
				<el-radio value="qemu">qemu</el-radio>
				<el-radio value="kvm">kvm</el-radio>
			</el-radio-group>
		</el-form-item>
		<el-form-item label="系统版本">
			<el-select v-model="formBasic.systemVersion" style="width: 100%">
				<el-option v-for="item in formItem.versionData" :key="item.label" :label="item.label" :value="item.label" />
			</el-select>
		</el-form-item>
		<el-form-item label="备注">
			<el-input v-model="formBasic.notes" :rows="3" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息" />
		</el-form-item>
		<a class="vm-new-leve" @click="formItem.advancedOptions = !formItem.advancedOptions"
			>
       <span>高级选项</span>
			<el-icon v-if="!formItem.advancedOptions"><ArrowRightBold /></el-icon>
			<el-icon v-if="formItem.advancedOptions"><ArrowDownBold /></el-icon>
		</a>
		<div v-show="formItem.advancedOptions">
      <el-form-item label="VNC协议">
				<el-select v-model="formBasic.vnc" style="width: 100%">
					<el-option label="不使用" value="none" />
					<el-option label="spice" value="spice" />
					<el-option label="vnc" value="vnc" />
				</el-select>
			</el-form-item>
      <el-form-item label="外部设备">
				<el-checkbox v-model="formBasic.mouse" label="鼠标" />
				<el-checkbox v-model="formBasic.keyboard" label="键盘" />
			</el-form-item>
			<el-form-item label="自动启动虚拟机">
				<el-switch v-model="formBasic.selfStart" inline-prompt active-text="开启" inactive-text="关闭" />
			</el-form-item>
			<el-form-item label="是否加密">
				<el-switch v-model="formBasic.encryption" inline-prompt active-text="开启" inactive-text="关闭" />
			</el-form-item>
			<el-form-item label="自动迁移">
				<el-switch v-model="formBasic.migrate" inline-prompt active-text="开启" inactive-text="关闭" />
			</el-form-item>
		</div>
		<el-form-item label=""></el-form-item>
	</el-form>
</template>
<script lang="ts" setup name="AddBasic">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts';
import { propName } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  besicTime: {
    type: String,
    required: true
  }
});
const formRef = ref<FormInstance>()
const emit = defineEmits(['basicOK']);
// 基本信息
const formItem = reactive({
  advancedOptions: false,
  versionData: linuxData,
});
const formBasic = reactive({
  name: '',
  systemType: 'Linux',
  virtualizationType: 'qemu',
  systemVersion: '',
  notes: '',
  mouse: false,
  keyboard: false,
  vnc: 'none',
  selfStart: true,
  encryption: false,
  migrate: true,
});
formBasic.systemVersion = linuxData[0].label
const rulesForm = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "change" },
  ],
})
watch(
  ()=> props.besicTime,
  (val)=>{
    if (formRef.value) {
      formRef.value.validate(val=>{
        if (val) {
          emit('basicOK', formBasic);
        }
      })
    }
  }
);
watch(
  ()=> formBasic.systemType,
  (val)=>{
    if(val==='Linux') {
      formItem.versionData = linuxData
      formBasic.systemVersion = linuxData[0].label
    }else if(val==='Windows') {
      formItem.versionData = windowsData
      formBasic.systemVersion = windowsData[0].label
    }else if(val==='qita') {
      formItem.versionData = qitaData
      formBasic.systemVersion = qitaData[0].label
    }
  }
);
</script>
<style lang="scss" scoped>
  .vm-new-leve {
    display: flex;
    align-items: center;
    font-size: 20px;
    margin-bottom: 20px;
    cursor: pointer;
    >span {
      margin-right: 10px;
    }
  }
</style>
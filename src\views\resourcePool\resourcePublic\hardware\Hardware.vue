<template>
	<div class="resource-pool-container">
		<div class="tabs-btn-area">
			<h2 @click="deviceListClick">硬件设备清单</h2>
			<el-button type="primary" plain :icon="Refresh" :disabled="state.synchronous" @click="synchronousClick">同步数据</el-button>
		</div>
		<div class="tabs-table-area">
			<div v-for="(card, index) in cards" :key="index" class="colItem">
				<HardwareCard :title="card.title" :icon="card.icon" :items="hardwareData[card.key]" :show-config="card.show" @configure="openConfig" />
			</div>
			<!-- <el-row :gutter="20">
				<el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(card, index) in cards" :key="index" class="colItem">
					<HardwareCard :title="card.title" :icon="card.icon" :items="hardwareData[card.key]" :show-config="card.show" @configure="openConfig" />
				</el-col>
			</el-row> -->
		</div>
		<PhysicalConfig v-model:show="showConfig" :network="selectedNetwork" @save="handleNetworkSave" />
	</div>
</template>
<script setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import { synchronousHardware, deviceListQuery } from '/@/api/ResourcePool'; // 接口
const HardwareCard = defineAsyncComponent(() => import('./HardwareCard.vue'));
const PhysicalConfig = defineAsyncComponent(() => import('./PhysicalConfig.vue'));
const props = defineProps({
	treeItem: {
		type: Object,
		required: true,
	},
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
	synchronous: false,
});
const cards = [
	{ title: '物理网卡', icon: 'Phone', key: 'networkCard', show: true },
	{ title: '本地磁盘', icon: 'Collection', key: 'storage' },
	{ title: 'PCI设备', icon: 'Setting', key: 'pciDevices' },
	{ title: 'USB设备', icon: 'Setting', key: 'usbDevices' },
	{ title: 'CPU', icon: 'Cpu', key: 'cpu' },
	{ title: '内存', icon: 'Collection', key: 'memory' },
	{ title: 'GPU', icon: 'Monitor', key: 'gpu' },
	{ title: 'HBA', icon: 'Setting', key: 'hba' },
];

const hardwareData = ref({
	networkCard: [],
	storage: [],
	pciDevices: [{ name: '暂无数据' }],
	usbDevices: [{ name: '暂无数据' }],
	cpu: [],
	memory: [],
	gpu: [],
	hba: [{ name: '暂无数据' }],
});

const showConfig = ref(false);
const selectedNetwork = ref(null);

const openConfig = (network) => {
	selectedNetwork.value = network;
	showConfig.value = true;
};

const handleNetworkSave = (config) => {
	ElMessage.success('配置已保存');
};
// 同步数据
const synchronousClick = async () => {
	state.synchronous = true;
	setTimeout(() => {
		state.synchronous = false;
	}, 1500);
	synchronousHardware(props.treeItem.id).then((res) => {
		deviceListClick();
	});
};
// 查询清单
const deviceListClick = () => {
	deviceListQuery(props.treeItem.id).then((res) => {
		// 物理网卡
		hardwareData.value.networkCard = res.data.network_cards?.map((em) => ({
			name: em.name, // 名称
			ip: em.ip_address, // ip
			mac: em.mac, // mac
			speed:em.speed, // 速度
			mtu:em.mtu, // 最大传输单元
			duplex:em.duplex, // 模式("Full":全双工，半双工)
			linkStatus: em.link_status, // 链接状态
			activityStatus: em.status, // 活动状态
			vendor: em.vendor==''?'未知':em.vendor, // 厂商
		}));
		// 本地磁盘
		hardwareData.value.storage = res.data.disks?.map((em) => ({
			name: em.device, // 名称
			model: em.model=='unknown'?'未知型号': em.model, // 型号
			size: em.size, // 容量
			status: em.status, // 可用状态
			vendor: em.vendor=='unknown'?'未知':em.vendor, // 厂商
		}));
		// PCI设备
		hardwareData.value.pciDevices = res.data.pci_devices?.map(em => ({
		  name:em.name=='unknown'?'未知':em.name, // 名称
			type:em.type=='unknown'?'未知':em.type, // 类型
			status:em.status, // 活动状态
		}));
		// USB设备
		hardwareData.value.usbDevices = res.data.usbs?.map(em => ({
		  name:em.name, // 名称
		  device:em.device, // 设备
		  description:em.description, // 描述
			status:em.status, // 活动状态
		}));
		// CPU
		hardwareData.value.cpu = res.data.cpu_info?.map(em => ({
		 	name: em.model, // 型号
			cores: em.cores, // 核心
			threads: em.threads, // 线程
			pciPath:em.pci_path, // PCI路径
			frequency: em.frequency, // 频率
		}));
		
		// 内存
		hardwareData.value.memory = res.data.memory_info?.map(em => ({
		  name: em.name, // 名称
			memSize: em.size, // 容量
			vendor: em.manufacturer=='Unknown'?'未知':em.manufacturer, // 厂商
			type: em.type, // 类型
		}));
		// GPU
		hardwareData.value.gpu = res.data.gpus?.map(em => ({
		  deviceID: em.device_id, // 设备ID
			gpuModel: em.model, // 型号
			vendor: em.vendor==''?'未知':em.vendor, // 厂商
		}));
		// HBA
		// hardwareData.value.hba = res.data.hba_devices?.map(em => ({
		//   name: em.device_id,
		// 	gpuModel: em.model,
		// 	vendor: em.vendor==''?'未知':em.vendor,
		// }));
	});
};
onMounted(() => {deviceListClick()});
</script>
<style lang="scss" scoped>
.resource-pool-container {
	width: calc(100%);
	height: calc(100%);
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.tabs-table-area {
		width: calc(100%);
		height: calc(100% - 50px);
		// position: relative;
		display: flex;
    flex-wrap: wrap;
		align-content: space-around;
		justify-content: space-around;
		align-items: center;
		.colItem {
			height: 330px;
			width: 385px;
		}
	}
}
</style>
<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="添加虚拟机"
    class="dialog-900"
  >
    <div class="vm-new-area">
      <div class="vm-step-area">
        <el-steps :active="formItem.current" align-center>
          <el-step title="基本信息" :icon="Tickets" />
          <el-step title="硬件信息" :icon="Setting" />
          <el-step title="汇总信息" :icon="DocumentCopy" />
        </el-steps>
        <!-- <el-steps :active="formItem.current" simple>
          <el-step title="基本信息" :icon="Tickets" />
          <el-step title="硬件信息" :icon="Setting" />
          <el-step title="汇总信息" :icon="DocumentCopy" />
        </el-steps> -->
      </div>
      <div class="vm-new-content">
        <!-- 基本信息 -->
        <div class="vm-new-common" v-show="formItem.current==0">
          <AddBasic v-if="formItem.isShow" :besicTime="formItem.besicTime" @basicOK="basicOK"></AddBasic>
        </div>
        <!-- 硬件信息 -->
        <div class="vm-new-common" v-show="formItem.current==1">
          <!-- CPU总数 -->
          <AddCPU v-if="formItem.isShow" :cpuTime="formItem.cpuTime" @cpuOK="cpuOK"></AddCPU>
          <!-- 内存 -->
          <AddMEM v-if="formItem.isShow" :memTime="formItem.memTime" @memOK="memOK"></AddMEM>
          <!-- 磁盘 -->
          <AddDisk v-if="formItem.isShow" :treeItem="props.treeItem" :diskAdd="formItem.diskAdd" :diskTime="formItem.diskTime" @diskOK="diskOK"></AddDisk>
          <!-- 网卡 -->
          <AddNet v-if="formItem.isShow" :treeItem="props.treeItem" :netAdd="formItem.netAdd" :netTime="formItem.netTime" @netOK="netOK"></AddNet>
          <!-- 光驱 -->
          <AddDrive v-if="formItem.isShow" :treeItem="props.treeItem" :driveAdd="formItem.driveAdd" @driveOK="driveOK"></AddDrive>
        </div>
        <!-- 汇总信息 -->
        <div class="vm-new-common summary-area" v-if="formItem.current==2">
          <h2>基本信息</h2>
          <div>
            <ul>
              <li><span>虚拟机名称</span><span>{{formItem.besicdata.name}}</span></li>
              <li><span>系统类型</span><span>{{formItem.besicdata.systemType}}</span></li>
              <li><span>系统版本</span><span>{{formItem.besicdata.systemVersion}}</span></li>
              <li><span>备注</span><span>{{formItem.besicdata.notes}}</span></li>
              <li><span>自动启动虚拟机</span><span>{{formItem.besicdata.selfStart?'开启':'关闭'}}</span></li>
              <li><span>是否加密</span><span>{{formItem.besicdata.encryption?'开启':'关闭'}}</span></li>
              <li><span>自动迁移</span><span>{{formItem.besicdata.migrate?'开启':'关闭'}}</span></li>
            </ul>
          </div>
          <h2>硬件信息</h2>
          <div>
            <!-- CPU配置 -->
            <div class="hardware-group">
              <div class="group-title">CPU配置</div>
              <div class="group-content">
                <ul>
                  <li><span>CPU总数</span><span>{{formItem.cpudata.vcpus || '-'}}</span></li>
                  <li><span>CPU最大值</span><span>{{formItem.cpudata.cpuMax || '-'}}</span></li>
                  <li><span>CPU架构</span><span>{{formItem.cpudata.framework || '-'}}</span></li>
                  <li><span>CPU工作模式</span><span>{{formItem.cpudata.mode || '-'}}</span></li>
                  <li><span>CPU预留(%)</span><span>{{formItem.cpudata.reserve || '-'}}</span></li>
                  <li><span>CPU限制(%)</span><span>{{formItem.cpudata.limit || '-'}}</span></li>
                </ul>
              </div>
            </div>

            <!-- 内存配置 -->
            <div class="hardware-group">
              <div class="group-title">内存配置</div>
              <div class="group-content">
                <ul>
                  <li><span>内存大小</span><span>{{formItem.memdata.memory || '-'}} {{formItem.memdata.unit || ''}}</span></li>
                  <li><span>内存预留百分比</span><span>{{formItem.memdata.reserveMem || '-'}}</span></li>
                  <li><span>内存分配策略</span><span>{{formItem.memdata.strategy || '-'}}</span></li>
                  <li><span>绑定NUMA NODE</span><span>{{formItem.memdata.bind || '-'}}</span></li>
                </ul>
              </div>
            </div>

            <!-- 磁盘配置 -->
            <div v-if="formItem.diskdata && formItem.diskdata.length > 0" class="hardware-group">
              <div class="group-title">磁盘配置</div>
              <div class="group-content">
                <div v-for="(item, index) in formItem.diskdata" :key="'disk'+index" class="sub-group">
                  <h4 class="sub-title">{{index==0?'磁盘':'磁盘'+(index)}}</h4>
                  <ul>
                    <li><span>磁盘大小</span><span>{{item.disk || '-'}}{{item.unit || ''}}</span></li>
                    <li><span>总线类型</span><span>{{item.bus || '-'}}</span></li>
                    <li><span>缓存方式</span><span>{{item.cache || '-'}}</span></li>
                    <li><span>系统类型</span><span>{{item.source || '-'}}</span></li>
                    <li><span>开启共享</span><span>{{item.share?'开启':'关闭'}}</span></li>
                    <li><span>置备类型</span><span>{{item.preparation || '-'}}</span></li>
                    <li><span>IO悬挂超时时长</span><span>{{item.timeout || '-'}}</span></li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 网卡配置 -->
            <div v-if="formItem.netdata && formItem.netdata.length > 0" class="hardware-group">
              <div class="group-title">网卡配置</div>
              <div class="group-content">
                <div v-for="(item, index) in formItem.netdata" :key="'net'+index" class="sub-group">
                  <h4 class="sub-title">{{index==0?'网卡':'网卡'+(index)}}</h4>
                  <ul>
                    <li><span>网络</span><span>{{item.net || '-'}}</span></li>
                    <li><span>MAC地址</span><span>{{item.mac || '-'}}</span></li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 光驱配置 -->
            <div v-if="formItem.drivedata && formItem.drivedata.length > 0" class="hardware-group">
              <div class="group-title">光驱配置</div>
              <div class="group-content">
                <div v-for="(item, index) in formItem.drivedata" :key="'drive'+index" class="sub-group">
                  <h4 class="sub-title">{{index==0?'光驱':'光驱'+(index)}}</h4>
                  <ul>
                    <li><span>光驱</span><span>{{item.drive || '-'}}</span></li>
                    <li><span>光驱存储池</span><span>{{item.drivestorage || '-'}}</span></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-dropdown placement="top" trigger="click" @command="commandItem" v-if="formItem.current==1" style="float:left;">
          <el-button type="primary" >
          添加硬件<el-icon class="el-icon--right"><ArrowUpBold /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="disk">磁盘</el-dropdown-item>
              <el-dropdown-item command="net">网卡</el-dropdown-item>
              <el-dropdown-item command="drive">光驱</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button v-if="formItem.current>0" type="primary" @click="formItem.current--">上一步</el-button>
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button v-if="formItem.current<2" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="formItem.current==2" type="primary" @click="confirm" :loading="formItem.loading">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="AddVM">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance } from 'element-plus'
import { Tickets, Setting, DocumentCopy } from '@element-plus/icons-vue'
import { vmNew } from '/@/api/ResourcePool/vm'; // 接口
const AddBasic = defineAsyncComponent(() => import('./AddBasic.vue'));
const AddCPU = defineAsyncComponent(() => import('./AddCPU.vue'));
const AddMEM = defineAsyncComponent(() => import('./AddMEM.vue'));
const AddDisk = defineAsyncComponent(() => import('./AddDisk.vue'));
const AddNet = defineAsyncComponent(() => import('./AddNet.vue'));
const AddDrive = defineAsyncComponent(() => import('./AddDrive.vue'));


const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  addVmTime: {
    type: String,
    required: true
  }
});
const hardwareREF = ref<FormInstance>()
interface BasicData {
  name?: string;
  vnc?: string;
  mouse?: boolean;
  virtualizationType?: string;
  keyboard?: boolean;
}
interface CpuData {
  vcpus?: number; 
}
interface MemData {
  memory?: number;
  unit?: string;
}
interface forminter {
  isShow: boolean,
  loading: boolean,
  current: number,
  besicTime: string,
  besicdata: BasicData,
  cpuTime: string,
  cpudata: CpuData,
  memTime: string,
  memdata: MemData,
  diskTime: string,
  diskAdd: number,
  diskdata: Array<object>,
  netTime: string,
  netAdd: number,
  netdata: Array<object>,
  // driveTime: string,
  regularCPU: boolean,
  regularMEM: boolean,
  regularDISK: boolean,
  regularNET: boolean,
  driveAdd: number,
  drivedata: Array<object>,
}
const formItem = reactive<forminter>({
  isShow: false,
  loading: false,
  current: 0,
  besicTime: '',
  besicdata: {},
  cpuTime: '',
  cpudata: {},
  memTime: '',
  memdata: {},
  diskTime: '',
  diskAdd: 1,
  diskdata: [],
  netTime: '',
  netAdd: 1,
  netdata: [],
  // driveTime: '',
  regularCPU: false,
  regularMEM: false,
  regularDISK: false,
  regularNET: false,
  driveAdd: 1,
  drivedata: [],
});
// 基本信息下一步
const nextStep = () => {
  if(formItem.current == 0) {
    formItem.besicTime = ""+new Date()
  }else if(formItem.current == 1) {
    formItem.cpudata = {}
    formItem.memdata = {}
    formItem.diskdata = []
    formItem.netdata = []
    formItem.drivedata = []
    formItem.cpuTime = ""+new Date()
    formItem.memTime = ""+new Date()
    formItem.diskTime = ""+new Date()
    formItem.netTime = ""+new Date()
    // formItem.driveTime = ""+new Date()
  }
}
// 基本信息数据
const basicOK = (data: any) => {
  formItem.besicdata = data
  formItem.current++
}
// 硬件信息-CPU
const cpuOK = (data: any) => {
  if(!data) {
    formItem.regularCPU = false
  }else {
    formItem.cpudata = data
    formItem.regularCPU = true
  }
}
// 硬件信息-内存
const memOK = (data: any) => {
  if(!data) {
    formItem.regularMEM = false
  }else {
    formItem.memdata = data
    formItem.regularMEM = true
  }
}
// 硬件信息-磁盘
const diskOK = (data: any) => {
  if(!data) {
    formItem.regularDISK = false
  }else {
    formItem.diskdata = data
    formItem.regularDISK = true
  }
}
// 硬件信息-网卡
const netOK = (data: any) => {
  if(!data) {
    formItem.regularNET = false
  }else {
    formItem.netdata = data
    formItem.regularNET = true
  }
  setTimeout(() => {
    if(formItem.regularCPU&&formItem.regularMEM&&formItem.regularDISK&&formItem.regularNET) {
      formItem.current ++
    }
  }, 200);
}
// 硬件信息-光驱
const driveOK = (data: any) => {
  formItem.drivedata = data
}
// 添加硬件
const commandItem = (item: string)=>{
  if(item == 'disk') {
    formItem.diskAdd ++
  }else if(item == 'net'){
    formItem.netAdd ++
  }else if(item == 'drive'){
    formItem.driveAdd ++
  }
}

const emit = defineEmits(['returnOK']);
interface DiskData {
  pool: string;
  disk_type: string;
  size: number;
  disk_unit_type: string;
  path: string;
  storage_type_code: string;
  disk_name: string;
  pool_id: string;
  disk_id: string;
}

interface InterfaceData {
  interface_type: string; // 'network'
  mac: string;
  network: string;
  net_id: string;
  model: string; // 'virtio'
  switch_id: string;
}
const confirm =()=>{

  formItem.loading = true;
  let data = {
    host_ip: props.treeItem.ip,
    host_pid: props.treeItem.pid,
    vm_name: formItem.besicdata.name, // 虚拟机名称
    virtualization: formItem.besicdata.virtualizationType,
    memory_unit: formItem.memdata.memory,
    memory_unit_type: formItem.memdata.unit,
    vcpu_unit: formItem.cpudata.vcpus,
    ...(formItem.besicdata.vnc!=='none'?{display_protocol:formItem.besicdata.vnc}:null),
    input_devices: {
      ...(formItem.besicdata.mouse?{mouse:'usb'}:null),
      ...(formItem.besicdata.keyboard?{keyboard:'usb'}:null),
    },
    disk: [] as DiskData[], // 初始化为一个空数组
    interface: [] as InterfaceData[],
  }
  formItem.diskdata.forEach((em:any) => {
    data.disk.push({
      pool: em.source=='newbuilt'?em.newPoolName:em.existingPoolName,
      disk_type: 'file', // 写死
      size: em.disk,
      disk_unit_type: em.unit,
      path: em.source=='newbuilt'?em.newPath:em.existingPath,
      storage_type_code: em.source=='newbuilt'?em.newFormat:em.existingType,
      disk_name: em.source=='newbuilt'?em.newDiskName:em.existingDiskName,
      pool_id:em.poolID,
      disk_id:em.source=='newbuilt'? undefined:em.diskID
    })
  });
  formItem.netdata.forEach((em:any) => {
    data.interface.push({
      interface_type: "network", // 写死
      mac: em.mac,
      network: em.net,
      net_id: em.netID,
      model: 'virtio', // 写死
      switch_id: em.switchID
    })
  })
  vmNew(data)
  .then(res => {
    formItem.isShow = false;
    emit('returnOK', 'refresh');
  })
  .catch(error => {
    formItem.loading = false;
  })
}
watch(
  ()=> props.addVmTime,
  ()=>{
    formItem.isShow = true;
    formItem.loading = false;
    formItem.current = 0
  }
);

</script>
<style lang="scss" scoped>
  .vm-new-area {
    width: 100%;
    height: 600px;
    .vm-step-area {
      margin-bottom: 10px;
      padding: 0 15%;
      height: 70px;
    }
    .vm-new-content {
      width: 100%;
      height: calc(100% - 70px);
      overflow: auto;

      .vm-new-common {
        width: 100%;
        height: 100%;
        padding: 10px 30px;
        
        .vm-new-leve {
          display: block;
          font-size: 20px;
          margin-bottom: 20px;
          cursor: pointer;
        }
      }
      .summary-area {
        padding: 0 50px;
        h2 {
          color: #409eff;
          border-bottom: 1px solid #409eff;
          font-weight: 600;
          padding: 10px 0;
          margin-bottom: 20px;
          font-size: 18px;
        }
        h3 {
          color: #606266;
          font-size: 14px;
          font-weight: 600;
          margin: 15px 0 10px 0;
        }
        ul {
          padding: 10px;
          margin: 0 0 30px 0;
          list-style: none;
          li {
            padding: 8px 0;
            margin: 0;
            list-style: none;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            h3 {
              display: inline-block;
              font-weight: 600;
              width: 200px;
              color: #606266;
              font-size: 14px;
              margin: 0;
            }
            span:first-child {
              display: inline-block;
              width: 200px;
              font-weight: bold;
              color: #606266;
            }
            span:last-child {
              color: #303133;
            }
            .summary_notes {
              display: inline-block;
              width: 400px;
            }
          }
        }

        // 硬件配置分组样式
        .hardware-group {
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          padding: 15px;
          margin-bottom: 15px;

          .group-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
            font-size: 15px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
          }

          .group-content {
            ul {
              margin: 0;
              padding: 0;

              li {
                padding: 6px 0;
                border-bottom: 1px solid #e9ecef;

                &:last-child {
                  border-bottom: none;
                }

                span:first-child {
                  color: #6c757d;
                  font-weight: 500;
                  display: inline-block;
                  width: 150px;
                }

                span:last-child {
                  color: #495057;
                }
              }
            }

            // 子组样式（用于多个磁盘、网卡、光驱）
            .sub-group {
              margin-bottom: 15px;

              &:last-child {
                margin-bottom: 0;
              }

              .sub-title {
                font-weight: 600;
                color: #6c757d;
                font-size: 13px;
                margin: 0 0 8px 0;
                padding: 5px 10px;
                background-color: #e9ecef;
                border-radius: 4px;
                display: inline-block;
              }

              ul {
                margin: 0;
                padding: 0 0 0 15px;

                li {
                  padding: 4px 0;
                  border-bottom: 1px solid #dee2e6;

                  &:last-child {
                    border-bottom: none;
                  }

                  span:first-child {
                    color: #6c757d;
                    font-weight: 500;
                    display: inline-block;
                    width: 130px;
                    font-size: 13px;
                  }

                  span:last-child {
                    color: #495057;
                    font-size: 13px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
</style>
<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="新建网络"
    class="dialog-500"
  >
    <div class="form-switch-area">
      <el-form
        ref="ruleFormRef"
        :model="formItem"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="网络名称" prop="name">
          <el-input v-model="formItem.name"  placeholder="请输入网络名称"/>
        </el-form-item>
        <el-form-item label="网络类型" prop="type">
          <el-select v-model="formItem.type" style="width: 100%">
            <el-option label="VLAN" value="vlan" />
            <el-option label="FLAT" value="flat" />
          </el-select>
        </el-form-item>
         <el-form-item label="VLAN" prop="vlan" v-if="formItem.type=='vlan'">
          <el-input v-model="formItem.vlan" type="number" :min=1 placeholder="有效值1-4096"/>
        </el-form-item>
        <el-form-item label="子网" prop="subnet">
          <el-input v-model="formItem.subnet" placeholder="例：***********/24"/>
        </el-form-item>
        <el-form-item label="IP版本" prop="version">
          <el-select v-model="formItem.version" style="width: 100%">
            <el-option label="IPV4" value="ipv4" />
            <el-option label="IPV6" value="ipv6" />
          </el-select>
        </el-form-item>
        <el-form-item label="网关地址" prop="gateway">
          <el-input v-model="formItem.gateway" placeholder="请输入网关地址"/>
        </el-form-item>
        <el-form-item label="启用DHCP">
				  <el-checkbox v-model="formItem.dhcp" />
        </el-form-item>
        <div v-if="formItem.dhcp" style="display: flex;justify-content: space-between">
          <el-form-item label="起始IP" prop="start" >
            <el-input v-model="formItem.start" placeholder="例：***********"/>
          </el-form-item>
          <el-form-item label="结束IP" prop="end">
            <el-input v-model="formItem.end" placeholder="例：*************"/>
          </el-form-item>
        </div>
        <el-form-item label="DNS">
          <el-input v-model="formItem.dns" placeholder="请输入DNS"/>
        </el-form-item>
        <el-form-item label="主机路由">
          <div class="route-input">
            <el-input v-model="formItem.destination" placeholder="网段例：***********/24"/>
            <el-input v-model="formItem.nexthop" placeholder="吓一跳例：********"/>
            <el-button :icon="Plus" circle @click="routeAdd"/>
          </div>
        </el-form-item>
        <div class="route-datas" v-if="formItem.routeData.length>0">
          <div v-for="(item,index) in formItem.routeData" :key="index" class="route-item">
            <span class="route-wd">{{ item.destination }}</span>
            <span class="route-xyt">{{ item.nexthop }}</span>
            <el-button :icon="Delete" text type="danger" @click="routeDelete(index)"/>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="netNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { Plus,Delete } from '@element-plus/icons-vue'
import { netNew } from '/@/api/Network'; // 接口
import { propName,propVlan,propSub,ipInSub,subnetCheck,startEnd } from '/@/model/network.ts'; // 表列、正则

const props = defineProps({
  newTime: {
    type: String,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name: '',
  type: 'vlan',
  vlan: '',
  subnet: '',
  version: 'ipv4',
  gateway: '',
  dhcp: false,
  start: '',
  end: '',
  dns: '',
  destination: '',
  nexthop: '',
  routeData: [{destination:'',nexthop:''}],
});

// 添加主机路由
const routeAdd = ()=>{
  if(formItem.destination!==''&& formItem.nexthop!=='') {
    formItem.routeData.push({
      destination: formItem.destination,
      nexthop: formItem.nexthop
    })
    formItem.destination = '';
    formItem.nexthop = '';
  }else {
    ElMessage.warning('请输入正确的主机路由');
  }
}
// 删除主机路由
const routeDelete = (index: number)=>{
  formItem.routeData.splice(index,1);
}
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        netNew({
          name: formItem.name,
          network_type: formItem.type,
          ...(formItem.type == "vlan"?{segmentation_id:formItem.vlan}:null),
          cidr: formItem.subnet,
          gateway_ip: formItem.gateway,
          enable_dhcp: formItem.dhcp,
          ...(formItem.dhcp?{startip:formItem.start}:null),
          ...(formItem.dhcp?{endip:formItem.end}:null),
          ...(formItem.dns!==''?{dns_nameservers:formItem.dns}:null),
          host_routes: formItem.routeData
        })
        .then(res => {
          if(res.msg == 'ok') {
            formItem.isShow = false;
            emit('returnOK', 'refresh');
            ElMessage.success('新建网络操作已完成');
          }else if(res.msg == '409'){
            ElMessage.error('网关地址不能出现在DHCP地址范围内');
          }else{
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}
watch(
  ()=> props.newTime,
  (val)=>{
    formItem.isShow = true;
    formItem.dhcp = false;
    formItem.dns = '';
    formItem.destination = '';
    formItem.nexthop = '';
    formItem.routeData = [];

    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
  }
);
const propGateway = (rule: any, value: any, callback: any) => {
  let regex = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
  if (regex.test(value)) {
		ipInSub(value, formItem.subnet) ? callback() : callback(new Error("与子网不匹配"));
	} else {
		callback(new Error('无效网关地址'));
	}
}
const propStart = (rule: any, value: any, callback: any) => {
  if (subnetCheck(formItem.gateway,value)) {
		callback()
	} else {
		callback(new Error('请输入同一网段的不同IP'));
	}
}
const propEnd = (rule: any, value: any, callback: any) => {
  if (startEnd(formItem.start,value)) {
		callback()
	} else {
		callback(new Error('结束IP应大于起始IP'));
	}
}
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" }  
  ],
  type: [{ required: true, message: '必选项', trigger: 'blur' }],
  vlan: [
    { required: true, message: '必填项' },
    { validator: propVlan, trigger: "blur" }
  ],
  subnet: [
    { required: true, message: '必填项' },
    { validator: propSub, trigger: "blur" }
  ],
  version: [{ required: true, message: '必选项', trigger: 'blur' }],
  gateway: [
    { required: true, message: '必填项' },
    { validator: propGateway, trigger: "blur" }
  ],
  start: [
    { required: true, message: '必填项' },
    { validator: propStart, trigger: "blur" }
  ],
  end: [
    { required: true, message: '必填项' },
    { validator: propEnd, trigger: "blur" }
  ],
})
</script>
<style lang="scss" scoped>
  .form-switch-area {
    width: 100%;
    overflow: auto;
    .route-input {
      width: 100%;
      display: flex;
    }
    .route-datas {
      height: 70px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .route-item {
        width: 49%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .route-wd {
          display: inline-block;
          width: 50%;
        }
        .route-xyt {
          display: inline-block;
          width: 42%;
        }
      }
    }
  }
</style>
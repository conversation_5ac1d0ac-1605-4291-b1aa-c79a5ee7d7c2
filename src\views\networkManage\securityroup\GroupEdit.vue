<template>
	<el-dialog v-model="formItem.isShow" append-to-body :title="formItem.title" class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="安全组名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入安全组名称"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { secureGroupNew,secureGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const props = defineProps({
	editTime: {
		type: String,
		required: true,
	},
  editRow: {
    type: Object,
    required: true,
  }
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  title: '',
	name: '',
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
});
const emit = defineEmits(['groupOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        if(formItem.title == '新建安全组') {
          secureGroupNew({
            name: formItem.name,
          }).then((res) => {
            if(res.msg == 'ok') {
              formItem.isShow = false;
              ElMessage.success(formItem.title+'操作完成');
              emit('groupOK', 'refresh');
            }else {
              ElMessage.error(res.msg);
            }
          });
        }else {
          secureGroupEdit({
            name: formItem.name,
          }).then((res) => {
            if(res.msg == 'ok') {
              formItem.isShow = false;
              ElMessage.success(formItem.title+'操作完成');
              emit('groupOK', 'refresh');
            }else {
              ElMessage.error(res.msg);
            }
          });
        }
			}
		});
	}
};
watch(
	() => props.editTime,
	(val) => {
		formItem.isShow = true;
    formItem.title = val.split('/')[0]+'安全组'
    if(formItem.title == '新建安全组') {
      if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
        ruleFormRef.value.resetFields();
      }
    }else {
      formItem.name = props.editRow.name;
    }
	}
);
</script>
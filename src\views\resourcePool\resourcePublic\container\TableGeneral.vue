<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    class="dialog-500"
  >
    <template #header="{ close, titleId, titleClass }">
				<span class="el-dialog__title">{{ formItem.title }}容器</span>
			</template>
    <div>
      <span>是否 <strong>{{ formItem.title }}</strong>  下列下列容器？</span>
      <p :style="{color: formItem.title == '启动'||formItem.title == '恢复'?'green':'red','word-wrap':'break-word'}">
        {{ formItem.names.toString() }}
      </p>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="TableDelete">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { containerOperate } from '/@/api/ResourcePool/container'; // 接口

const formItem = reactive({
  isShow: false,
  title: '-',
  action: '',
  names: [],
  ids: [],
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  let list = {
    name: formItem.names[0],
    id: formItem.ids[0],
    action: formItem.action,
  }
  formItem.isShow = false;
  containerOperate(list)
  .then(res => {
    if(res.data.msg == "ok") {
      setTimeout(() => {
        ElMessage.success(formItem.title+'容器操作完成')
        emit('returnOK', 'refresh');
      }, 500);
    }else {
      ElMessage.error(formItem.title+'容器操作失败')
    }
  })
}
// 打开弹窗
const openDialog = async (type: any,row: any) => {
	nextTick(() => {
		formItem.isShow = true;
    formItem.title = type.name
    formItem.action = type.action
    formItem.names = row.map((item: any) => item.name)
    formItem.ids = row.map((item: any) => item.id)
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
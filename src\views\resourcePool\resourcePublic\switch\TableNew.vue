<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="新建本地交换机"
    class="dialog-500"
  >
    <div class="form-switch-area">
      <el-form
        ref="ruleFormRef"
        :model="formItem"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="交换机名称" prop="name">
          <el-input v-model="formItem.name"  placeholder="请输入网络名称"/>
        </el-form-item>
        <el-form-item label="宿主机" prop="host">
          <el-select v-model="formItem.host" style="width: 100%">
            <el-option v-for="item in formItem.hostData" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="MTU(字节)" prop="mtu">
          <el-input v-model="formItem.mtu" type="number" :min=1 />
        </el-form-item>
       <el-form-item label="端口组名称" prop="portName">
          <el-input v-model="formItem.portName"  placeholder="请输入网络名称"/>
        </el-form-item>
        <el-form-item label="VLAN_ID" prop="vlan" >
          <el-input v-model="formItem.vlan" type="number" :min=1 placeholder="有效值1-4096"/>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="netNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { hostAllQuery,netSwitchNew } from '/@/api/Network'; // 接口
import { propName,propVlan } from '/@/model/network.ts'; // 表列、正则

const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name: '',
  host: '',
  hostData: [{ name: '主机1', id: '1111111' }],
  mtu: '1500',
  portName: '',
  vlan: '',
});
// 获取主机集群数据
const hostColnyQuery = ()=>{
  let hostData: any[] = []
  hostAllQuery()
  .then(res => {
    res?.forEach((em:any) => {
      hostData.push({
        name: em.name, 
        id: em.id
      })
    });
    formItem.hostData = hostData
    formItem.host = hostData[0].id
  })
}
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        netSwitchNew({
          name: formItem.name,
          host_id:formItem.host,
          mtu: formItem.mtu,
          port_group_name: formItem.portName,
          vlan_id: formItem.vlan,
        })
        .then(res => {
          if(res.code == 200) {
            formItem.isShow = false;
            emit('returnOK', 'refresh');
            ElMessage.success('新建虚拟交换机操作已完成');
          }else{
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}


const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" }  
  ],
  host: [{ required: true, message: '必选项', trigger: 'blur' }],
  mtu: [{ required: true, message: '必选项', trigger: 'blur' }],
  portName: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" }  
  ],
  vlan: [
    { required: true, message: '必填项' },
    { validator: propVlan, trigger: "change" }
  ],
})
// 打开弹窗
const openDialog = async (treeItem: any) => {
  formItem.isShow = true;
	nextTick(() => {
		if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
    hostColnyQuery()
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
  .form-switch-area {
    width: 100%;
    overflow: auto;
    .route-input {
      width: 100%;
      display: flex;
    }
    .route-datas {
      height: 70px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .route-item {
        width: 49%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .route-wd {
          display: inline-block;
          width: 50%;
        }
        .route-xyt {
          display: inline-block;
          width: 42%;
        }
      }
    }
  }
</style>
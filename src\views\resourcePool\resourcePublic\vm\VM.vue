<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
        <el-button type="primary" plain @click="refresh">刷新</el-button>
        <el-button type="primary" plain @click="groupOperation('qd')">启动</el-button>
        <el-button type="primary" plain @click="groupOperation('gb')">关闭</el-button>
        <el-button type="danger" plain  @click="deleteClick(state.tableSelect)">删除</el-button>
        <el-dropdown @command="groupOperation">
          <el-button type="primary" style="margin-left: 10px">
            更多<el-icon class="el-icon--right"><ArrowDownBold /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="qzcq">强制重启</el-dropdown-item>
              <el-dropdown-item command="gbdy">关闭电源</el-dropdown-item>
              <el-dropdown-item command="zt">暂停</el-dropdown-item>
              <el-dropdown-item command="hf">恢复</el-dropdown-item>
              <el-dropdown-item command="wqkl">完全克隆</el-dropdown-item>
              <el-dropdown-item command="ljkl">链接克隆</el-dropdown-item>
              <el-dropdown-item command="qy">迁移</el-dropdown-item>
              <el-dropdown-item command="qyaaaa">其他</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div>
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:searchParams="state.searchParams"
				:columns="state.columns"
				:request="getTableData"
        @selectionChange='selectChange'
			>
				<template #level="{ row }">
					<span class="status-warn">{{ row.levelstr }}</span>
				</template>
        <!-- 状态 -->
        <template #status="{ row }">
					<span :style="{color:vmStatus('color',row.status)}">{{ vmStatus('text',row.status) }}</span>
				</template>
				<!-- 操作 -->
        <template #operation="{ row }">
          <el-dropdown @command="operateItem($event,row)">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="bj">编辑</el-dropdown-item>
                <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
			</my-table>
    </div>
    <VMgeneral :names="state.tableSelect.map(i => i.name)" :config="state.config"  @generalOK="generalOK"/>
    <VmDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" :treeItem="props.treeItem" @deleteOK="deleteOK"></VmDelete>
  </div>
</template>
<script setup lang="ts" name="VM">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { tabsVmQuery,recycleVmDelete,vmMoveinRecycle,vmOpen,vmClose,vmRestart,vmPause,vmRestore } from '/@/api/ResourcePool/vm'; // 接口
import { vmColumns,vmStatus } from '/@/model/resource.ts'; // 表列、正则

import { dayjs,ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const VMgeneral = defineAsyncComponent(() => import('../vmOperate/VMgeneral.vue'));
const VmDelete = defineAsyncComponent(() => import('./VmDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  columns: vmColumns as Array<MyTableColumns>, // 表格表头配置
  tableSelect: [],
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableRow: {},
  selectRow: [],
  editTime: '',
  deleteTime: '',

  config: {
    type: '',
    color: 'green',
    time: '',
  },

});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(!true){
    const list = new Array(page.pageSize).fill({}).map((item, index) => {
			item = {
        name: 'list'+index,
				ip: '192.168.1.' + index,
        vcpu: index,
        memory: 20,
				pool: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        status: 'running',
				id: Math.random().toString()+index,
        is_ha: true,
			};
			return item;
		});
		return {
			data: list, // 数据
			total: 100, // 总数
		};
  }
  return new Promise(async(resolve)=>{
    let type = ''
    switch (props.treeItem.level) {
      case 1:
        type = 'pool'
      break;
      case 2:
        type = 'cluster'
      break;
      case 3:
        type = 'host'
      break;
    }
    tabsVmQuery({
      type: type, // 树节点
      _id: props.treeItem.id, // 树ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      }) 
    })
  })
};
const tableRef = ref();
// 刷新
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
  let names:any[] = [];
  let ids:any[] = [];
  row.forEach((item:any)=>{
    names.push(item.name);
    ids.push(item.id);
  })
  formDelet.tableNames = names
  formDelet.tableIDs = ids
}
// 表格群操作
const groupOperation = (item: string)=>{
  if(state.tableSelect.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.selectRow = state.tableSelect
    switch (item) {
      case "qd":
        state.config.type = '启动'
        state.config.color = 'green'
        state.config.time = ''+new Date()
      break;
      case "gb":
        state.config.type = '关闭'
        state.config.color = 'red'
        state.config.time = ''+new Date()
      break;
      case "qzcq":
        state.config.type = '强制重启'
        state.config.color = 'red'
        state.config.time = ''+new Date()
      break;
      case "gbdy":
        state.config.type = '关闭电源'
        state.config.color = 'red'
        state.config.time = ''+new Date()
      break;
      case "zt":
        state.config.type = '暂停'
        state.config.color = 'red'
        state.config.time = ''+new Date()
      break;
      case "hf":
        state.config.type = '恢复'
        state.config.color = 'green'
        state.config.time = ''+new Date()
      break;
      case "wqkl":
        console.log('完全克隆')
      break;
      case "ljkl":
        console.log('链接克隆')
      break;
      case "qy":
        console.log('迁移')
      break;
      default:
        console.log('其他')
    }
  }
}
// 表操作列
const operateItem = (item: string,row: never)=>{
  state.tableRow = row
  switch (item) {
    case "bj":
      state.editTime = ''+new Date()
    break;
    case "sc":
      state.selectRow = [row]
      state.deleteTime = ''+new Date()
    break;
  }
}
// 操作返回
const generalOK = (item: string)=>{
  console.log('item',item)
}
// 删除操作
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.deleteTime = '虚拟机/'+new Date()
  }
}
const emit = defineEmits(['returnOK']);
// 删除返回
const deleteOK = (item:string)=>{
  if(item == 'delete'){
    recycleVmDelete({
      host_ip: props.treeItem.ip,
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('彻底删除虚拟机操作完成');
        emit('returnOK', 'refresh');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    vmMoveinRecycle({
      host_ip: props.treeItem.ip,
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除虚拟机移入回收站操作完成');
        emit('returnOK', 'refresh');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }
}
// 返回数据
const returnOK = (item:any)=>{
  refresh()
}
onMounted(() => {
  setTimeout(() => {
    refresh()
  }, 500);
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>
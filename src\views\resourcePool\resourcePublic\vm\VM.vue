<template>
  <div class="resource-pool-container">
    <div class="tabs-btn-area">
      <div>
        <el-button type="primary" plain @click="refresh">刷新</el-button>
        <el-button type="primary" plain @click="groupOperation('qd')">开机</el-button>
        <el-button type="primary" plain @click="groupOperation('gb')">关机</el-button>
        <el-button type="danger" plain  @click="groupOperation('sc')">删除</el-button>
        <el-dropdown trigger="click" @command="groupOperation">
          <el-button type="primary" style="margin-left: 10px">
            更多<el-icon class="el-icon--right"><ArrowDownBold /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="qzcq">强制重启</el-dropdown-item>
              <el-dropdown-item command="gbdy">关闭电源</el-dropdown-item>
              <el-dropdown-item command="zt">暂停</el-dropdown-item>
              <el-dropdown-item command="hf">恢复</el-dropdown-item>
              <el-dropdown-item command="wqkl">完全克隆</el-dropdown-item>
              <el-dropdown-item command="ljkl">链接克隆</el-dropdown-item>
              <el-dropdown-item command="qy">迁移</el-dropdown-item>
              <el-dropdown-item command="qyaaaa">其他</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div>
        <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
      </div>
    </div>
    <div class="tabs-table-area">
      <my-table
				ref="tableRef"
				:pagination="state.pagination"
				:columns="state.columns"
				:request="getTableData"
        @selectionChange='selectChange'
			>
				<template #level="{ row }">
					<span class="status-warn">{{ row.levelstr }}</span>
				</template>
        <!-- 状态 -->
        <template #status="{ row }">
					<span :style="{color:vmStatus('color',row.status)}">{{ vmStatus('text',row.status) }}</span>
				</template>
				<!-- 操作 -->
        <template #operation="{ row }">
          <el-dropdown trigger="click" @command="operateItem($event,row)">
            <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="kzt">控制台</el-dropdown-item>
                <el-dropdown-item command="bj">编辑</el-dropdown-item>
                <el-dropdown-item command="sc" style="color:red" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
			</my-table>
    </div>
    <VMgeneral  ref="generalRef" @returnOK="returnOK"/>
    <VMmigrate ref="migrateRef" @returnOK="returnOK"/>
    <VMedit  ref="editRef" @returnOK="returnOK"/>
    <VmDelete ref="deleteRef" @deleteOK="deleteOK"/>
  </div>
</template>
<script setup lang="ts" name="VM">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { tabsVmQuery,recycleVmDelete,vmMoveinRecycle } from '/@/api/ResourcePool/vm'; // 接口
import { vmColumns,vmStatus } from '/@/model/resource.ts'; // 表列、正则
import { dayjs,ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const VMgeneral = defineAsyncComponent(() => import('../vmOperate/VMgeneral.vue'));
const VMmigrate = defineAsyncComponent(() => import('../vmOperate/VMmigrate.vue'));
const VMedit = defineAsyncComponent(() => import('../vmOperate/VMedit.vue'));
const VmDelete = defineAsyncComponent(() => import('../vmOperate/VmDelete.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  columns: vmColumns as Array<MyTableColumns>, // 表格表头配置
  tableSelect: [],
  pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  return new Promise(async(resolve)=>{
    let type = ''
    switch (props.treeItem.level) {
      case 1:
        type = 'pool'
      break;
      case 2:
        type = 'cluster'
      break;
      case 3:
        type = 'host'
      break;
    }
    tabsVmQuery({
      type: type, // 树节点
      _id: props.treeItem.id, // 树ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      }) 
    })
  })
};
const tableRef = ref();
// 刷新
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
const generalRef = ref(); // 通用操作
const migrateRef = ref(); // 迁移
// 表格群操作
const groupOperation = (item: string)=>{
  if(state.tableSelect.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    switch (item) {
      case "qd":
        generalRef.value.openDialog('开机','green',state.tableSelect);
      break;
      case "gb":
        generalRef.value.openDialog('关机','red',state.tableSelect);
      break;
      case "sc":
        deleteClick(state.tableSelect)
      break;
      case "qzcq":
        generalRef.value.openDialog('强制重启','red',state.tableSelect);
      break;
      case "gbdy":
        generalRef.value.openDialog('关闭电源','red',state.tableSelect);
      break;
      case "zt":
        generalRef.value.openDialog('暂停','red',state.tableSelect);
      break;
      case "hf":
        generalRef.value.openDialog('恢复','green',state.tableSelect);
      break;
      case "wqkl":
        console.log('完全克隆')
      break;
      case "ljkl":
        console.log('链接克隆')
      break;
      case "qy":
        migrateRef.value.openDialog(formDelet.tableNames,formDelet.tableIDs,props.treeItem);
      break;
      default:
        console.log('其他')
    }
  }
}
// 表操作列
const deleteRef = ref();
const editRef = ref();
const operateItem = (item: string,row: any)=>{
  switch (item) {
    case "kzt":
      // 构建带参数的URL
      const spiceUrl = `/spice-html5/spice.html?list=${props.treeItem.ip}&port=${row.spice_port}`
      window.open(spiceUrl, '_blank')
      console.log('控制台',spiceUrl)
    break;
    case "bj":
      editRef.value.openDialog(row);
    break;
    case "sc":
      deleteClick([row])
    break;
  }
}
// 删除操作
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    deleteRef.value.openDialog(formDelet.tableNames,props.treeItem);
  }
}
const emit = defineEmits(['returnOK']);
// 删除返回
const deleteOK = (item:string)=>{
  if(item == 'delete'){
    recycleVmDelete({
      host_ip: props.treeItem.ip,
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('彻底删除虚拟机操作完成');
        emit('returnOK', 'refresh');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    vmMoveinRecycle({
      host_ip: props.treeItem.ip,
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除虚拟机移入回收站操作完成');
        emit('returnOK', 'refresh');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'refresh'){
    refresh()
  }
  console.log('item',item)
  // refresh()
}
onMounted(() => {
  setTimeout(() => {
    refresh()
  }, 500);
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    .tabs-btn-area {
      height: 40px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
</style>
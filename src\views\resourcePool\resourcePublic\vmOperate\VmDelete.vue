<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.title"
    class="dialog-500"
  >
    <div>
      <span>是否删除下列虚拟机？</span>
      <p style="color:red;word-wrap: break-word">{{ formItem.names.toString() }}</p>
      <div class="chec-area">
        <el-checkbox v-model="formItem.status" label="彻底删除" /> <span :style="{color: formItem.status?'#000':'#ccc'}">启用后将彻底删除虚拟机，否则将移入回收站。</span>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="TableDelete">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';

const formItem = reactive({
  isShow: false,
  status: false,
  title: '删除',
  names: [],
});

const emit = defineEmits(['deleteOK']);
const confirm =()=>{
  formItem.isShow = false;
  formItem.status?emit('deleteOK', 'delete'):emit('deleteOK', 'movein');
}
// 打开弹窗
const openDialog = async (names:any,treeItem:any) => {
  formItem.isShow = true;
  formItem.status = false;
  nextTick(() => {
    formItem.title = '删除虚拟机'
    formItem.names = names;
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
  .chec-area {
    display: flex;
    align-items: center;
    padding: 15px 0;
    span {
      margin-left: 5px;
    }
    
  }
</style>
<template>
	<el-dialog v-model="formItem.isShow" title="新建存储池" append-to-body  class="dialog-700">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储池名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入端口组名称" />
			</el-form-item>
			<el-form-item label="存储设备" prop="device">
				<el-select v-model="formItem.device" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3" placeholder="请选择分布式交换机">
					<el-option v-for="item in formItem.deviceData" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="选择主机">
				<ZtreePublick :type='formItem.type' :zNodes='formItem.zNodes'></ZtreePublick>
			</el-form-item>
			<el-form-item label="存储资源">
				<el-radio-group v-model="formItem.contents">
					<el-radio value="scan">扫描目录</el-radio>
					<el-radio value="appoint">指定目录</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="已选存储资源" prop="storage" v-if="formItem.contents=='appoint'">
				<el-input v-model="formItem.appoint" placeholder="示例：/var/list"/>
			</el-form-item>
      <el-form-item label="已选存储资源" prop="storage" v-if="formItem.contents=='scan'">
				<el-input v-model="formItem.storage" disabled/>
			</el-form-item>
		</el-form>
		<div class="table-area" v-if="formItem.contents=='scan'">
			<my-table ref="tableRef" :pagination="formItem.pagination" :columns="formItem.columns" :request="getTableData" @selectionChange="selectChange">
			</my-table>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { storeDocking,storageResourcesQuery } from '/@/api/StoreManage/index.ts'; // 接口
import { portGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
import { iscsiSorageColumns } from '/@/model/storeManage.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	columns: iscsiSorageColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
  tableID: [''],
	name: '',
	device: '',
	contents: 'scan',
	appoint: '',
	deviceData: [{ name: '主机1', id: '1111111' }],
  storage: '',
  selectNodes: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'change' },
	],
	storage: [{ required: true, message: '必选项', trigger: 'change' }],
});
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0' },
		{ id: '2', name: '主机池1', pid: '1' },
		{ id: '3', name: '集群1', pid: '2' },
		{ id: '4', name: '主机1', pid: '3' },
		{ id: '5', name: '主机2', pid: '3' },
		{ id: '6', name: '主机3', pid: '3' },
		{ id: '7', name: 'vm1', pid: '4' },
		{ id: '8', name: 'vm2', pid: '4' },
		{ id: '9', name: 'vm3', pid: '5' },
		{ id: '11', name: 'vm4', pid: '5' },
		{ id: '12', name: 'vm5', pid: '5' },
		{ id: '13', name: 'vm3', pid: '5' },
		{ id: '14', name: 'vm3', pid: '5' },
		{ id: '31', name: 'aaaaa', pid: '5' },
		{ id: '32', name: 'vm4', pid: '5' },
		{ id: '63', name: 'vm2', pid: '5' },
	];
}
// 打开弹窗
const openDialog = async () => {
	formItem.isShow = true;
	nextTick(() => {
    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
    setTimeout(() => {
			formItem.type = 'NAS-新建'
    	treeData()
		}, 200);
	});
};
// 获取表数据
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  formItem.tableSelect = []
  if(true) {
    return {
      data: [{name: '测试1'}], // 数据
      total: 1 // 总数
    }
  }
	return new Promise(async(resolve)=>{
    storageResourcesQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: formItem.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 表格选中变化
const selectChange = (row: any)=>{
  formItem.tableSelect = row
  let names:any[] = [];
  let ids:any[] = [];
  row.forEach((item:any)=>{
    names.push(item.name);
    ids.push(item.id);
  })
  formItem.storage = names.toString()
  formItem.tableID = ids
}
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				portGroupEdit({
					name: formItem.name,
				}).then((res: any) => {
					emit('returnOK', 'refresh');
				});
			}
		});
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.table-area {
	height: 200px;
	width: 100%;
	position: relative;
}
</style>
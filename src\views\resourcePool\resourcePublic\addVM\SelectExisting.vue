<template>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    title="选择磁盘"
  >
    <div class="storage-pool-dialog">
      <div class="torage-pool-tree">
        <ul>
          <li :class="state.poolID== item.id?'exdisk_bg':''" v-for="item in state.poolData" :key="item.id" @click="existingClick(item)">
            <h5>{{item.name}}</h5>
            <!-- <h4>{{item.type_code_display}}</h4> -->
            <h3>{{byteUnitConversion(item.capacity)}}</h3>
            <p> <span class="existing_totle">总容量</span></p>
          </li>
        </ul>
      </div>
      <div class="storage-pool-table">
        <el-input v-model="state.tableSearch" placeholder="请输入搜索内容">
          <template #append>
            <el-button :icon="Search" @click="refresh"></el-button>
          </template>
        </el-input>
        <my-table
          ref="tableRef"
          :pagination="state.pagination"
          :columns="state.columns"
          :request="getTableData"
        >
          <!-- 单选 -->
          <template #radio="{ row }">
            <el-radio-group v-model="state.tableID" @change="radioClick(row)">
              <el-radio :value="row.id"></el-radio>
            </el-radio-group>
          </template>
          <!-- 已用 -->
          <template #allocation="{ row }">
            <span>{{ byteUnitConversion(row.allocation) }}</span>
          </template>
          <!-- 总量 -->
          <template #capacity="{ row }">
            <span>{{ byteUnitConversion(row.capacity) }}</span>
          </template>
        </my-table>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="SelectExisting">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { storagePollQuery,DiskQuery } from '/@/api/ResourcePool/storage.ts'; // 接口
import { diskVMColumns,byteUnitConversion } from '/@/model/storage.ts';

import { Search } from '@element-plus/icons-vue'
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  existingTime: {
    type: String,
    required: true
  }
});
interface poolList {
  isShow: boolean;
  columns: Array<any>;
  pagination: {
    show: boolean;
  };
  poolData: Array<any>;
  poolID: string;
  tableID: string;
  poolName: string;
  tableName: string;
  tablePath: string;
  tableType: string;
  tableSize: string;
  tableSearch: string;
}
const state = reactive<poolList>({
  isShow: false,
  columns: diskVMColumns,
  pagination: {
		show: true,
	}, // 是否显示分页
  poolData: [],
  poolID: '',
  poolName: '',
  tableID: '',
  tableName: '',
  tablePath: '',
  tableType: '',
  tableSize: '',
  tableSearch: '',
});
// 查询存储池
const storagePollData = () => {
  storagePollQuery({
    _id: props.treeItem.id,
    type: 'host',
    page: 0,
    pagecount: 10,
    search_str: '',
    order_type: 'desc',
    order_by: '',
  }).then((res: any) => {
    state.poolData = res.data;
    state.poolID = res.data[0].id;
    state.poolName = res.data[0].name;
    refresh()
  })
}
// 点击存储池
const existingClick =(item:any)=>{
  state.poolID = item.id
  state.poolName = item.name
  refresh()
}
const getTableData = ( params: EmptyObjectType,page: EmptyObjectType) => {
  return new Promise(async(resolve)=>{
    DiskQuery({
      pool_id: state.poolID, // 存储池ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
        state.tableID = res.total*1>0?res.data[0].id:''
        state.tableName = res.total*1>0?res.data[0].name:''
        state.tablePath = res.total*1>0?res.data[0].path:''
        // state.tableFormat = res.total*1>0?res.data[0].format:''
        state.tableSize = res.total*1>0?byteUnitConversion(res.data[0].capacity):''
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      }) 
    }).catch((err:any) => {})
    
  })
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  // tableRef.value.handleSearch(); // 收索事件 表1页
  tableRef.value.refresh(); // 刷新事件 表当前
}
// 单选磁盘
const radioClick=(row:any)=>{
  state.tableID = row.id
  state.tableName = row.name
  state.tablePath = row.path
  state.tableType = row.type_code

  state.tableSize = byteUnitConversion(row.capacity)
}
const emit = defineEmits(['existingReturn']);
const confirm =()=>{
  state.isShow= false
  emit('existingReturn', {
    poolName:state.poolName,
    tableName:state.tableName,
    tablePath:state.tablePath,
    tableType:state.tableType,
    tableSize:state.tableSize
  });
}
watch(
  ()=> props.existingTime,
  (val)=>{
    state.isShow = true;
    storagePollData()
  }
);
</script>
<style lang="scss" scoped>
  .storage-pool-dialog {
    height: 650px;
    display: flex;
    .torage-pool-tree {
      width: 150px;
      height: 650px;
      padding-right: 10px;
      overflow: auto;
      ul {
        padding: 0; 
        margin: 0;
        list-style: none;
        li {
          padding: 5px;
          margin-bottom: 5px;
          width: 100%;
          height: 100px;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;
          cursor: pointer;
          h5 {
            text-align: center;
            color: rgb(209, 243, 209);
            width: 100%;
            white-space:nowrap; // 强制一行显示
            overflow:hidden; // 超出隐藏
            text-overflow:ellipsis; // 省略号
          }
          .existing_totle {
            color: #ccc;
          }
        }
      }
    }
    .storage-pool-table {
      position: relative;
      height: 600px;
      width: calc(100% - 150px);
    }
  }
  .exdisk_bg {
    background: #da4c18;
    color: #fff;
  }
</style>
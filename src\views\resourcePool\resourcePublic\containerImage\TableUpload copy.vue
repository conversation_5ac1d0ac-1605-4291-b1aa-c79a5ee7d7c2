<template>
  <el-dialog
    v-model="formItem.isShow"
    title="拉取容器镜像"
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="目标主机" prop="host">
        <el-input v-model="formItem.host"  placeholder="请输入集群名称"/>
      </el-form-item>
      <el-form-item label="镜像仓库" prop="repo">
        <el-input v-model="formItem.repo"  placeholder="请输入仓库，如：tianwen1:5000/ubuntu"/>
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-input v-model="formItem.tag"  placeholder="请输入标签，如：20.05"/>
      </el-form-item>
      <el-form-item label="上传磁盘" prop="file">
				<input type="file" @change="handleFileChange" :disabled="formItem.disabled" />
				<el-progress
					v-if="formItem.progressAll !== 0"
					:percentage="parseFloat((formItem.progressUsed/formItem.progressAll*100).toFixed(1))"
					:status="formItem.status"
				/>
			</el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { containerEdit } from '/@/api/ResourcePool/container'; // 接口
import { diskSlice, diskUpload } from '/@/api/ResourcePool/storage.ts'; // 接口
import pLimit from 'p-limit';

const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  host: '',
  repo: '',
  tag: '',
  file: null as File | null,
  fileSize: 0,
  progressAll: 0,
  progressUsed: 0,
  status: '',
  disabled: false,
  storagePoolId: '', // 存储池ID，需要从外部传入
});

const propFile = (rule: any, value: any, callback: any) => {
  if (!formItem.file) {
    callback(new Error('请选择文件'));
  } else {
    callback();
  }
};

const rules = reactive<FormRules>({
  host: [{ required: true, message: '必选项', trigger: "blur" }],
  repo: [{ required: true, message: '必填项', trigger: "blur" }],
  tag: [{ required: true, message: '必填项', trigger: "blur" }],
  file: [
    { required: true, message: '必选项' },
    { validator: propFile, trigger: 'change' },
  ],
});

// 处理文件选择
const handleFileChange = (event: any) => {
  formItem.file = event.target.files[0];
  formItem.fileSize = formItem.file ? formItem.file.size : 0;
  // 触发表单验证
  if (ruleFormRef.value) {
    ruleFormRef.value.validateField('file');
  }
};

// 上传文件函数
const uploadFile = async (file: File) => {
  const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
  let chunks = Math.ceil(file.size / chunkSize);
  formItem.progressAll = chunks;
  formItem.progressUsed = 0;
  formItem.status = '';

  const limit = pLimit(5); // 设置最大并发数为5

  // 创建分片数组
  const chunkPromises = [];
  for (let i = 0; i < chunks; i++) {
    let start = i * chunkSize;
    let end = Math.min(file.size, start + chunkSize);
    let chunk = file.slice(start, end);
    chunkPromises.push({ chunk, index: i });
  }

  try {
    // 串行上传分片（避免并发过多）
    for (const { chunk, index } of chunkPromises) {
      await limit(() => uploadChunk(chunk, index, chunks, file.name));
    }
    // 通知完成
    await notifyComplete(chunks, file.name);
  } catch (error) {
    ElMessage.error('上传磁盘失败');
    formItem.status = 'exception';
    formItem.disabled = false;
  }
};

// 上传单个分片
const uploadChunk = async (chunk: Blob, index: number, total: number, filename: string) => {
  let formData = new FormData();
  formData.append('chunk', chunk);
  formData.append('index', index.toString());
  formData.append('total', total.toString());
  formData.append('filename', filename);
  formData.append('storage_pool_id', formItem.storagePoolId);

  try {
    await diskSlice(formData);
    formItem.progressUsed++;
  } catch (error) {
    formItem.status = 'exception';
    formItem.disabled = false;
    throw error;
  }
};

// 通知上传完成
const notifyComplete = async (total: number, filename: string) => {
  try {
    await diskUpload({
      name: `${formItem.repo}_${formItem.tag}`, // 使用仓库名和标签作为磁盘名
      total: total,
      filename: filename,
      storage_pool_id: formItem.storagePoolId,
      size: formItem.fileSize,
      remark: `容器镜像: ${formItem.repo}:${formItem.tag}`
    });

    formItem.status = 'success';
    setTimeout(() => {
      ElMessage.success('上传磁盘操作完成');
      formItem.isShow = false;
      emit('returnOK', 'refresh');
      // 重置表单
      resetForm();
    }, 1000);
  } catch (error) {
    formItem.status = 'exception';
    formItem.disabled = false;
    ElMessage.error('上传完成通知失败');
  }
};

// 重置表单
const resetForm = () => {
  formItem.file = null;
  formItem.fileSize = 0;
  formItem.progressAll = 0;
  formItem.progressUsed = 0;
  formItem.status = '';
  formItem.disabled = false;
  formItem.host = '';
  formItem.repo = '';
  formItem.tag = '';
};

const emit = defineEmits(['returnOK']);
const confirm = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(val => {
      if (val) {
        if (formItem.file) {
          formItem.disabled = true;
          uploadFile(formItem.file);
        } else {
          ElMessage.warning('请选择要上传的文件');
        }
      }
    });
  }
};

// 打开弹窗
const openDialog = async (treeItem: any, storagePoolId?: string) => {
  formItem.isShow = true;
  formItem.storagePoolId = storagePoolId || ''; // 设置存储池ID
  nextTick(() => {
    formItem.host = treeItem.ip;
    resetForm();
  });
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
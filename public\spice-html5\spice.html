<!--
   Copyright (C) 2012 by <PERSON> <<EMAIL>>

   This file is part of spice-html5.

   spice-html5 is free software: you can redistribute it and/or modify
   it under the terms of the GNU Lesser General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   spice-html5 is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANT<PERSON><PERSON>ITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU Lesser General Public License for more details.

   You should have received a copy of the GNU Lesser General Public License
   along with spice-html5.  If not, see <http://www.gnu.org/licenses/>.

   --------------------------------------------------
    Spice Javascript client template.
    Refer to main.js for more detailed information
   --------------------------------------------------

-->

<!doctype html>
<html>
    <head>

        <title>Spice Javascript client</title>
        <link rel="stylesheet" type="text/css" href="spice.css" />

        <!-- ES2015/ES6 modules polyfill -->
        <script type="module">
            window._spice_has_module_support = true;
        </script>
        <script>
            window.addEventListener("load", function() {
                if (window._spice_has_module_support) return;
                var loader = document.createElement("script");
                loader.src = "thirdparty/browser-es-module-loader/dist/" +
                    "browser-es-module-loader.js";
                document.head.appendChild(loader);
            });
        </script>

        <script type="module" crossorigin="anonymous">
            import * as SpiceHtml5 from './src/main.js';

            var host = null, port = null;
            var sc;
            var autoConnectEnabled = false;

            function spice_error(e)
            {
                console.log("SPICE连接错误:", e);
                // 向父页面发送错误消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'spice-error',
                        message: e.toString()
                    }, '*');
                }
                disconnect();
            }

            // 解析URL参数并设置Host和Port
            function parseUrlParams() {
                const urlParams = new URLSearchParams(window.location.search);

                console.log("🔍 解析URL参数:", window.location.search);

                // 获取host参数
                const hostParam = urlParams.get('host');
                if (hostParam) {
                    host = hostParam;
                    document.getElementById('host').value = hostParam;
                    console.log("📡 设置Host:", hostParam);
                }

                // 获取port参数
                const portParam = urlParams.get('port');
                if (portParam) {
                    port = portParam;
                    document.getElementById('port').value = portParam;
                    console.log("🔌 设置Port:", portParam);
                }

                // 获取password参数
                const passwordParam = urlParams.get('password');
                if (passwordParam) {
                    document.getElementById('password').value = passwordParam;
                    console.log("🔐 设置Password");
                }

                // 检查是否启用自动连接
                const autoconnect = urlParams.get('autoconnect') || urlParams.get('auto');
                if (autoconnect === '1' || autoconnect === 'true') {
                    autoConnectEnabled = true;
                    console.log("🚀 启用自动连接");
                }

                return { host: hostParam, port: portParam, autoconnect: autoConnectEnabled };
            }

            // 自动连接函数
            function autoConnect() {
                if (autoConnectEnabled && host && port) {
                    console.log("🔄 开始自动连接到", host + ":" + port);

                    // 向父页面发送连接开始消息
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'spice-auto-connect-started'
                        }, '*');
                    }

                    // 延迟一点时间确保页面完全加载
                    setTimeout(() => {
                        connect();
                    }, 500);
                } else {
                    console.log("⚠️ 自动连接未启用或缺少连接参数");
                }
            }

            function connect()
            {
                var host, port, password, scheme = "ws://", uri;

                host = document.getElementById("host").value;
                port = document.getElementById("port").value;
                password = document.getElementById("password").value;

                console.log("🔄 开始连接到:", host + ":" + port);

                if ((!host) || (!port)) {
                    console.log("❌ 必须设置host和port");
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'spice-error',
                            message: '必须设置host和port'
                        }, '*');
                    }
                    return;
                }

                if (sc) {
                    sc.stop();
                }

                uri = scheme + host + ":" + port;
                console.log("🔗 连接URI:", uri);

                // 向父页面发送连接中消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'spice-connecting'
                    }, '*');
                }

                document.getElementById('connectButton').innerHTML = "Stop Connection";
                document.getElementById('connectButton').onclick = disconnect;

                try
                {
                    sc = new SpiceHtml5.SpiceMainConn({
                        uri: uri,
                        screen_id: "spice-screen",
                        dump_id: "debug-div",
                        message_id: "message-div",
                        password: password,
                        onerror: spice_error,
                        onagent: agent_connected,
                        onsuccess: function() {
                            console.log("✅ SPICE连接成功");
                            // 向父页面发送连接成功消息
                            if (window.parent !== window) {
                                window.parent.postMessage({
                                    type: 'spice-connected'
                                }, '*');
                            }
                        }
                    });
                }
                catch (e)
                {
                    console.error("❌ SPICE连接异常:", e.toString());
                    if (window.parent !== window) {
                        window.parent.postMessage({
                            type: 'spice-error',
                            message: e.toString()
                        }, '*');
                    }
                    alert(e.toString());
                    disconnect();
                }
            }

            function disconnect()
            {
                console.log("🔌 断开SPICE连接");

                // 向父页面发送断开连接消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'spice-disconnected'
                    }, '*');
                }

                if (sc) {
                    sc.stop();
                }
                document.getElementById('connectButton').innerHTML = "Start Connection";
                document.getElementById('connectButton').onclick = connect;
                if (window.File && window.FileReader && window.FileList && window.Blob)
                {
                    var spice_xfer_area = document.getElementById('spice-xfer-area');
                    if (spice_xfer_area != null) {
                      document.getElementById('spice-area').removeChild(spice_xfer_area);
                    }
                    document.getElementById('spice-area').removeEventListener('dragover', SpiceHtml5.handle_file_dragover, false);
                    document.getElementById('spice-area').removeEventListener('drop', SpiceHtml5.handle_file_drop, false);
                }
                console.log("✅ SPICE连接已断开");
            }

            function agent_connected(sc)
            {
                window.addEventListener('resize', SpiceHtml5.handle_resize);
                window.spice_connection = this;

                SpiceHtml5.resize_helper(this);

                if (window.File && window.FileReader && window.FileList && window.Blob)
                {
                    var spice_xfer_area = document.createElement("div");
                    spice_xfer_area.setAttribute('id', 'spice-xfer-area');
                    document.getElementById('spice-area').appendChild(spice_xfer_area);
                    document.getElementById('spice-area').addEventListener('dragover', SpiceHtml5.handle_file_dragover, false);
                    document.getElementById('spice-area').addEventListener('drop', SpiceHtml5.handle_file_drop, false);
                }
                else
                {
                    console.log("File API is not supported");
                }
            }
            /* SPICE port event listeners
            window.addEventListener('spice-port-data', function(event) {
                // Here we convert data to text, but really we can obtain binary data also
                var msg_text = arraybuffer_to_str(new Uint8Array(event.detail.data));
                DEBUG > 0 && console.log('SPICE port', event.detail.channel.portName, 'message text:', msg_text);
            });

            window.addEventListener('spice-port-event', function(event) {
                DEBUG > 0 && console.log('SPICE port', event.detail.channel.portName, 'event data:', event.detail.spiceEvent);
            });
            */

            // 监听来自父页面的消息
            window.addEventListener('message', function(event) {
                console.log("📨 收到父页面消息:", event.data);

                switch(event.data.type) {
                    case 'spice-auto-connect':
                    case 'spice-connect-now':
                        if (event.data.host && event.data.port) {
                            console.log("🔄 收到自动连接命令");
                            document.getElementById('host').value = event.data.host;
                            document.getElementById('port').value = event.data.port;
                            host = event.data.host;
                            port = event.data.port;
                            autoConnectEnabled = true;
                            setTimeout(connect, 100);
                        }
                        break;
                    case 'spice-start-connection':
                    case 'force-connect':
                        console.log("🚀 收到强制连接命令");
                        setTimeout(connect, 100);
                        break;
                    case 'connect':
                        if (event.data.auto) {
                            console.log("🔄 收到自动连接命令");
                            setTimeout(connect, 100);
                        }
                        break;
                }
            });

            // 页面加载完成后的初始化
            window.addEventListener('load', function() {
                console.log("📄 SPICE页面加载完成");

                // 解析URL参数
                const params = parseUrlParams();

                // 向父页面发送准备就绪消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'spice-ready',
                        params: params
                    }, '*');
                }

                // 如果启用了自动连接，则开始连接
                if (params.autoconnect && params.host && params.port) {
                    console.log("🚀 页面加载完成，开始自动连接");
                    autoConnect();
                }
            });

            document.getElementById('connectButton').onclick = connect;
            document.getElementById('sendCtrlAltDel').addEventListener('click', function(){ SpiceHtml5.sendCtrlAltDel(sc); });
        </script>

    </head>

    <body>

        <div id="login">
            <button onclick="open_nav()">&#9776; SPICE</button>
            <p id="hostname">Host Console</p>
        </div>

        <div id="Sidenav" class="SidenavClosed" style="width: 0;">
            <p class="closebtn" onclick="close_nav()">&#10006;</p>
            <label for="host">Host:</label> <input type='text' id='host' value='************'> <!-- localhost --><br>
            <label for="port">Port:</label> <input type='text' id='port' value='5900'><br>
            <label for="password">Password:</label> <input type='password' id='password' value=''><br>
            <button id="connectButton">Start Connection</button><br>
            <button id="sendCtrlAltDel">Send Ctrl-Alt-Delete</button>
            <button id="debugLogs">Toggle Debug Logs</button>
            <div id="message-div" class="spice-message" style="display: none;"></div>

            <div id="debug-div">
            <!-- If DUMPXXX is turned on, dumped images will go here -->
            </div>
        </div>

        <div id="spice-area">
            <div id="spice-screen" class="spice-screen"></div>
        </div>

        <script>
            function show_debug_Logs() {
                var content = document.getElementById('message-div')
                if (content.style.display === 'block') {
                    content.style.display = 'none';
                } else {
                    content.style.display = 'block';
                }
            }

            function display_hostname() {
                var title = new URLSearchParams(window.location.search);
                name = title.getAll('title');
                name = name.split('(')[0];
                document.getElementById('hostname').innerHTML = (name);
            }

            function open_nav() {
                document.getElementById('Sidenav').className = 'SidenavOpen';
            }

            function close_nav() {
                document.getElementById('Sidenav').className = 'SidenavClosed';
            }

            document.getElementById('debugLogs').addEventListener('click', function() { show_debug_Logs(); });
            display_hostname()
        </script>
    </body>
</html>

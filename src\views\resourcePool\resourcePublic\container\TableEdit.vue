<template>
  <el-dialog
    v-model="formItem.isShow"
    title="编辑容器"
    width="500"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="容器名称" prop="name">
        <el-input v-model="formItem.name"  placeholder="请输入集群名称"/>
      </el-form-item>
      <el-form-item label="CPU" prop="cpu">
        <el-input v-model="formItem.cpu" type="number" placeholder="请输入容器虚拟CPU数量" />
      </el-form-item>
      <el-form-item label="内存" prop="memory">
        <el-input v-model="formItem.memory" type="number" placeholder="请输入内存大小(MB)" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="ColonyEdit">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { containerEdit } from '/@/api/ResourcePool/container'; // 接口
import { propName,propCPU, propMem } from '/@/model/resource.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  id: '',
  name: '',
  cpu: '',
  memory: '',
});
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" },
  ],
  cpu: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propCPU, trigger: "change" },
  ],
  memory: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propMem, trigger: "change" },
  ],
})

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        containerEdit({
          id: formItem.id,
          name: formItem.name,
          cpu: formItem.cpu,
          memory: formItem.memory
        })
        .then(res => {
          ElMessage.success('编辑容器操作已完成')
          emit('returnOK', 'refresh');
        })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		formItem.isShow = true;
    formItem.id = row.id
    formItem.name = row.name
    formItem.cpu = row.cpu
    formItem.memory = row.memory
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
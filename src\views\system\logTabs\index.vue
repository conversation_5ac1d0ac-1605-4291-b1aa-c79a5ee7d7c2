<template>
	<div class="monitoring-area layout-padding">
		<el-card>
			<div class="log-general-area">
				<TableSearch ref="searchRef" :searchParams="state.searchParams" :search="state.searchColumns" @search="onSearch" />
				<div class="btn-group">
					<el-button :icon="Search" type="primary" @click="onSearch">查询</el-button>
					<a :href="state.fileUrl" style="margin-left: 10px">
            <el-button :icon="Download" type="primary" :disabled='state.disabled'>导出日志</el-button>
          </a>
				</div>
			</div>
			<div class="log-table">
				<div class="infinite-scroll-table" ref="scrollContainer">
					<el-table
						ref="tableRef"
						v-loading="state.infiniteScroll.loading"
						:data="state.infiniteScroll.allData"
						stripe
						height="100%"
						style="width: 100%"
					>
						<el-table-column
							v-for="column in state.columns"
							:key="column.prop"
							:prop="column.prop"
							:label="column.label"
							:width="column.width"
							:min-width="column.minWidth"
							:fixed="column.fixed"
							:sortable="column.sortable"
							:show-overflow-tooltip="true"
						>
							<template #default="{ row }" v-if="column.prop === 'exectime'">
								<span>{{ conversionTime(row.exectime) }}</span>
							</template>
							<template #default="{ row }" v-else-if="column.prop === 'role'">
								<span>{{ translationRole(row.role) }}</span>
							</template>
							<template #default="{ row }" v-else-if="column.prop === 'result'">
								<el-tag :type="row.result == '成功'?'success':'info'">{{ row.result }}</el-tag>
							</template>
							<template #default="{ row }" v-else>
								<span>{{ row[column.prop] }}</span>
							</template>
						</el-table-column>
					</el-table>

					<!-- 加载更多提示 -->
					<div v-if="state.infiniteScroll.loading" class="loading-more">
						<el-icon class="is-loading"><Loading /></el-icon>
						<span>正在加载更多数据...</span>
					</div>

					<!-- 没有更多数据提示 -->
					<div v-if="!state.infiniteScroll.hasMore && state.infiniteScroll.allData.length > 0" class="no-more-data">
						<span>已加载全部数据</span>
					</div>

					<!-- 空数据提示 -->
					<div v-if="!state.infiniteScroll.loading && state.infiniteScroll.allData.length === 0" class="empty-data">
						<img src="/@/assets/images/no-data.svg" alt="" class="empty-img" />
						<span>暂无数据</span>
					</div>
				</div>
			</div>
		</el-card>
	</div>
</template>
<script setup lang="ts" name="LogTabs">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search, Download, Loading } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { translationRole } from '/@/model/system.ts';
import { logColumns,logSearch,defaultTime,cookieList } from '/@/model/logManage.ts'; // 表列、正则
import { logTableQuery, logConditionQuery } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
const TableSearch = defineAsyncComponent(() => import('/@/components/table/Search.vue'));
const searchRef = ref();
const tableRef = ref();
const scrollContainer = ref();

// 时间转换函数
const conversionTime = (timeStr: string) => {
	if (!timeStr) return '-';
	return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss');
};

// 定义变量内容
const state = reactive({
	searchParams: {} as EmptyObjectType, // 搜索参数
	searchColumns: [] as Array<TableSearchType>, // 搜索表单配置
	columns: logColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	categoryData: [],
	actionData: [],
	resultData: [],
	fileUrl: 'javascript:void(0)',
	disabled: false,
	// 无限滚动相关状态
	infiniteScroll: {
		loading: false, // 是否正在加载
		finished: false, // 是否已加载完所有数据
		currentPage: 1, // 当前页码
		pageSize: 20, // 每页数据量
		allData: [] as any[], // 所有已加载的数据
		hasMore: true, // 是否还有更多数据
	},
});
// 搜索条件查询
// const searchQuery = () => {
// 	logConditionQuery().then((res: any) => {
// 		state.categoryData = res.category
// 		state.actionData = res.action
// 		state.resultData = res.result
// 	})
// 	searchData()
// };
// 初始化搜索条件
const searchData = () => {
	let listData = logSearch(true)
	listData.forEach((em:any) => {
		if (em.prop == 'times') {
			// em.options = [defaultTime(0),defaultTime(24)]
			em.options = []
		}
		if (em.prop == 'category') {
			em.options = state.categoryData
		}
		if (em.prop == 'role') {
			// let list = 'list=aaa; role=sysadm; coole=bbb'
			// em.options = cookieList(list)
			em.options = cookieList(document.cookie)
		}
		if (em.prop == 'action') {
			em.options = state.actionData
		}
		if (em.prop == 'result') {
			em.options = state.resultData
		}
	});
	state.searchColumns = listData
};
// 模拟生成日志数据
const generateLogData = (page: number, pageSize: number) => {
	const actions = ['login', 'logout', 'create', 'update', 'delete', 'view'];
	const results = ['成功', '失败'];
	const categories = ['system', 'user', 'security', 'operation'];
	const users = ['admin', 'user1', 'user2', 'operator', 'guest'];

	const data = [];
	const startIndex = (page - 1) * pageSize;

	for (let i = 0; i < pageSize; i++) {
		const index = startIndex + i;
		const randomDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);

		data.push({
			id: `log_${index}`,
			exectime: randomDate.toISOString().slice(0, 19).replace('T', ' '),
			username: users[Math.floor(Math.random() * users.length)],
			category: categories[Math.floor(Math.random() * categories.length)],
			role: 'sysadm',
			action: actions[Math.floor(Math.random() * actions.length)],
			result: results[Math.floor(Math.random() * results.length)],
			message: `${actions[Math.floor(Math.random() * actions.length)]}操作${results[Math.floor(Math.random() * results.length)]} - 第${index + 1}条日志`
		});
	}

	return data;
};

const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async (resolve) => {
		// 模拟网络延迟
		setTimeout(() => {
			const currentPage = page.pageNum || 1;
			const pageSize = page.pageSize || state.infiniteScroll.pageSize;

			// 模拟总数据量为500条
			const totalRecords = 500;
			const maxPage = Math.ceil(totalRecords / pageSize);

			// 生成当前页数据
			const listData = generateLogData(currentPage, pageSize);

			// 判断是否还有更多数据
			const hasMore = currentPage < maxPage;

			console.log(`📊 加载第${currentPage}页数据，每页${pageSize}条，总共${totalRecords}条，还有更多：${hasMore}`);

			resolve({
				data: listData,
				total: totalRecords,
				hasMore: hasMore,
				currentPage: currentPage
			});
		}, 300); // 模拟300ms网络延迟
	});
};
// 无限滚动相关方法
// 重置无限滚动状态
const resetInfiniteScroll = () => {
	state.infiniteScroll.loading = false;
	state.infiniteScroll.finished = false;
	state.infiniteScroll.currentPage = 1;
	state.infiniteScroll.allData = [];
	state.infiniteScroll.hasMore = true;
	console.log('🔄 重置无限滚动状态');
};

// 加载初始数据
const loadInitialData = async () => {
	console.log('📊 开始加载初始数据');
	resetInfiniteScroll();
	await loadMoreData();
	// 设置滚动监听
	setupScrollListener();
};

// 加载更多数据
const loadMoreData = async () => {
	if (state.infiniteScroll.loading || !state.infiniteScroll.hasMore) {
		console.log('⏸️ 跳过加载：', {
			loading: state.infiniteScroll.loading,
			hasMore: state.infiniteScroll.hasMore
		});
		return;
	}

	console.log(`📥 开始加载第${state.infiniteScroll.currentPage}页数据`);
	state.infiniteScroll.loading = true;

	try {
		const result: any = await getTableData(state.searchParams, {
			pageNum: state.infiniteScroll.currentPage,
			pageSize: state.infiniteScroll.pageSize
		});

		if (result.data && result.data.length > 0) {
			// 追加新数据到现有数据
			state.infiniteScroll.allData.push(...result.data);
			state.infiniteScroll.currentPage++;
			state.infiniteScroll.hasMore = result.hasMore !== false;

			console.log(`✅ 成功加载${result.data.length}条数据，总计${state.infiniteScroll.allData.length}条`);
		} else {
			// 没有更多数据
			state.infiniteScroll.hasMore = false;
			console.log('📄 没有更多数据了');
		}
	} catch (error) {
		console.error('❌ 加载数据失败:', error);
		ElMessage.error('加载数据失败');
	} finally {
		state.infiniteScroll.loading = false;
	}
};

// 设置表格滚动监听
const setupScrollListener = () => {
	nextTick(() => {
		if (tableRef.value) {
			// 获取表格内部的滚动容器
			const tableElement = tableRef.value.$el;
			const scrollWrapper = tableElement.querySelector('.el-table__body-wrapper');

			if (scrollWrapper) {
				console.log('📋 设置表格滚动监听器');

				scrollWrapper.addEventListener('scroll', (event: Event) => {
					const target = event.target as HTMLElement;
					if (!target) return;

					const { scrollTop, scrollHeight, clientHeight } = target;
					const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

					// 当滚动到底部80%时触发加载
					if (scrollPercentage > 0.8 && !state.infiniteScroll.loading && state.infiniteScroll.hasMore) {
						console.log('🔽 触发滚动加载，滚动百分比:', Math.round(scrollPercentage * 100) + '%');
						loadMoreData();
					}
				});
			} else {
				console.warn('⚠️ 未找到表格滚动容器');
			}
		}
	});
};

// 收索
const onSearch = () => {
	// 搜索事件
	state.searchParams = Object.assign({}, searchRef.value.form);
	console.log('🔍 执行搜索，参数:', state.searchParams);
	nextTick(() => {
		refreshTable();
	});
};

// 表格刷新事件
const refreshTable = () => {
	// 重置无限滚动状态并重新加载数据
	resetInfiniteScroll();
	loadInitialData();
};
onMounted(() => {
	searchData();
	// 初始化加载数据
	loadInitialData();
});
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
	.log-general-area {
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: space-between;
		.btn-group {
			display: flex;
			justify-content: right;
		}
	}
	.log-table {
		width: 100%;
		height: calc(100% - 50px);
		position: relative;

		.infinite-scroll-table {
			width: 100%;
			height: 100%;
			position: relative;

			.el-table {
				height: 100%;
			}

			// 加载更多提示样式
			.loading-more {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 20px;
				color: var(--el-color-primary);
				font-size: 14px;
				background: #f8f9fa;
				border-top: 1px solid var(--el-border-color-light);

				.el-icon {
					margin-right: 8px;
					font-size: 16px;
				}
			}

			// 没有更多数据提示样式
			.no-more-data {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 15px;
				color: var(--el-color-info);
				font-size: 13px;
				background: #f5f7fa;
				border-top: 1px solid var(--el-border-color-lighter);
			}

			// 空数据提示样式
			.empty-data {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 60px 20px;
				color: var(--el-color-info);

				.empty-img {
					width: 100px;
					height: 100px;
					margin-bottom: 16px;
					opacity: 0.6;
				}

				span {
					font-size: 14px;
				}
			}
		}
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
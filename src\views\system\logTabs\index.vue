<template>
	<div class="monitoring-area layout-padding">
		<el-card>
			<div class="log-general-area">
				<TableSearch ref="searchRef" :searchParams="state.searchParams" :search="state.searchColumns" @search="onSearch" />
				<div class="btn-group">
					<el-button :icon="Search" type="primary" @click="onSearch">查询</el-button>
					<a :href="state.fileUrl" style="margin-left: 10px">
            <el-button :icon="Download" type="primary" :disabled='state.disabled'>导出日志</el-button>
          </a>
				</div>
			</div>
			<div class="log-table">
				<MyTable ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData">
					<!-- 时间 -->
					<template #exectime="{ row }">
						<span>{{ conversionTime(row.exectime) }}</span>
					</template>
					<!-- 角色 -->
					<template #role="{ row }">
						<span>{{ translationRole(row.role) }}</span>
					</template>
					<!-- 结果 -->
					<template #result="{ row }">
						<el-tag :type="row.result == '成功'?'success':'info'">{{ row.result }}</el-tag>
					</template>
				</MyTable>
			</div>
		</el-card>
	</div>
</template>
<script setup lang="ts" name="LogTabs">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search, Download } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { translationRole } from '/@/model/system.ts';
import { logColumns,logSearch,defaultTime,cookieList } from '/@/model/logManage.ts'; // 表列、正则
import { logTableQuery, logConditionQuery } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableSearch = defineAsyncComponent(() => import('/@/components/table/Search.vue'));
const searchRef = ref();

// 定义变量内容
const state = reactive({
	searchParams: {} as EmptyObjectType, // 搜索参数
	searchColumns: [] as Array<TableSearchType>, // 搜索表单配置
	columns: logColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	categoryData: [],
	actionData: [],
	resultData: [],
	fileUrl: 'javascript:void(0)',
	disabled: false,
});
// 搜索条件查询
// const searchQuery = () => {
// 	logConditionQuery().then((res: any) => {
// 		state.categoryData = res.category
// 		state.actionData = res.action
// 		state.resultData = res.result
// 	})
// 	searchData()
// };
// 初始化搜索条件
const searchData = () => {
	let listData = logSearch(true)
	listData.forEach((em:any) => {
		if (em.prop == 'times') {
			// em.options = [defaultTime(0),defaultTime(24)]
			em.options = []
		}
		if (em.prop == 'category') {
			em.options = state.categoryData
		}
		if (em.prop == 'role') {
			// let list = 'list=aaa; role=sysadm; coole=bbb'
			// em.options = cookieList(list)
			em.options = cookieList(document.cookie)
		}
		if (em.prop == 'action') {
			em.options = state.actionData
		}
		if (em.prop == 'result') {
			em.options = state.resultData
		}
	});
	state.searchColumns = listData
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async (resolve) => {
		let listData:any = [
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
			{exectime: '2022-01-01 00:00:00',username: 'admin',category: 'system',role: 'sysadm',action: 'login',result: '成功',message: '登录成功'},
		];
			resolve({
				data: listData, // 数据
				total: listData.length, // 总数
			});
	});
};
// 收索
const onSearch = () => {
	// 搜索事件
	state.searchParams = Object.assign({}, searchRef.value.form);
	nextTick(() => {
		refreshTable();
	});
};

const tableRef = ref();
// 表格刷新事件
const refreshTable = () => {
	tableRef.value.handleSearch();
};
onMounted(() => {
	searchData();
});
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
	.log-general-area {
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: space-between;
		.btn-group {
			display: flex;
			justify-content: right;
		}
	}
	.log-table {
		width: 100%;
		height: calc(100% - 50px);
		position: relative;
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
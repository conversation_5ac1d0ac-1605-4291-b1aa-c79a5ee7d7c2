// 存储管理表
const storageManageColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name', sortable: true, align: 'left' },
	{ label: '类型', prop: 'type_code_display', align: 'center' },
	{ label: '总容量', tdSlot: 'capacity', align: 'center' },
	{ label: '已分配容量', tdSlot: 'allocation', align: 'center' },
	{ label: '可用容量', tdSlot: 'available', align: 'center' },
	{ label: '存储目录', prop: 'storage_local_dir', align: 'center' },
	{ label: '磁盘', tdSlot: 'storage_volume', align: 'center' },
	{ label: '是否启用', prop: 'status', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
const fcColumns = [
	{ type: 'selection', wrap: true },
	{ label: '存储资源名称', prop: 'name', sortable: true },
	{ label: 'WWN', prop: 'wwn', align: 'center' },
	{ label: '链路状态', prop: 'wwn', align: 'center' },
	{ label: '资源容量', prop: 'wwn', align: 'center' },
	{ label: '可用容量', prop: 'wwn', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '所属存储池', prop: 'wwn', align: 'center' },
	{ label: '扫描时间', prop: 'wwn', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// fc 新建存储池表
const fcSorageColumns = [
	{ type: 'selection', wrap: true },
	{ label: '存储资源', prop: 'name', sortable: true },
	{ label: 'WWN', prop: 'wwn', align: 'center' },
	{ label: '链路状态', prop: 'status', align: 'center' },
	{ label: '存储容量', prop: 'size', align: 'center' },
	{ label: '关联主机数', prop: 'number', align: 'center' },
	{ label: '扫描时间', prop: 'time', align: 'center' },
];
const iscsiColumns = [
	{ type: 'selection', wrap: true },
	{ label: '存储资源', tdSlot: 'device_name', sortable: true },
	{ label: '存储IP', prop: 'ip_mgmt', align: 'center' },
	{ label: 'CHAP认证', prop: 'wwn', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '存储资源', prop: 'wwn', align: 'center' },
	{ label: '上次扫描时间', prop: 'wwn', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// iscsi 新建存储池表
const iscsiSorageColumns = [
	// { type: 'selection', wrap: true },
	{ type: 'expand', expand: 'expandContent' },
	{ label: '存储资源', prop: 'name', sortable: true },
	{ label: 'IQN', prop: 'target_name', align: 'center' },
	{ label: '链路状态', prop: 'status', align: 'center' },
	{ label: '存储容量', prop: 'size', align: 'center' },
	// { label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '扫描时间', prop: 'updated_at', align: 'center' },
	{ label: '状态', tdSlot: 'login_status', align: 'center', width: 90, },
];
// 设备详情表
const iscsiDetailsColumns = [
	{ label: '存储资源名称', prop: 'storage_device_device_name', sortable: true },
	{ label: 'IQN', prop: 'target_name', align: 'center' },
	{ label: '资源容量', prop: 'size', align: 'center' },
	{ label: '可用容量', prop: 'used', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '所属存储池', prop: 'wwn', align: 'center' },
	{ label: '上次扫描时间', prop: 'updated_at', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
const nasColumns = [
	{ type: 'selection', wrap: true },
	{ label: '存储设备名称', tdSlot: 'name', sortable: true },
	{ label: '存储IP', prop: 'wwn', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '存储资源', prop: 'wwn', align: 'center' },
	{ label: '上次扫描时间', prop: 'wwn', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 设备详情表
const nasDetailsColumns = [
	{ label: '存储目录', prop: 'name', sortable: true },
	{ label: '链路状态', prop: 'iqn', align: 'center' },
	{ label: '资源容量', prop: 'size', align: 'center' },
	{ label: '可用容量', prop: 'used', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '所属存储池', prop: 'wwn', align: 'center' },
	{ label: '上次扫描时间', prop: 'wwn', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
const nvmeColumns = [
	{ type: 'selection', wrap: true },
	{ label: '存储资源名称', prop: 'name', sortable: true },
	{ label: '链路状态', prop: 'wwn', align: 'center' },
	{ label: '资源容量', prop: 'wwn', align: 'center' },
	{ label: '可用容量', prop: 'wwn', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '所属存储池', prop: 'wwn', align: 'center' },
	{ label: '扫描时间', prop: 'wwn', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
const distributedColumns = [
	{ type: 'selection', wrap: true },
	{ label: '存储资源名称', prop: 'name', sortable: true },
	{ label: '监控节点', prop: 'wwn', align: 'center' },
	{ label: '关联主机数', prop: 'wwn', align: 'center' },
	{ label: '存储资源', prop: 'wwn', align: 'center' },
	{ label: '上次扫描时间', prop: 'wwn', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];

const storeColumns = [
	{ type: 'selection', wrap: true },
	{ type: 'expand', expand: 'expandContent' },
	{ label: '名称', tdSlot: 'name', sortable: true },
	{ label: '类型', prop: 'type', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
	// { type: 'selection', wrap: true },
	// { label: '名称', prop: 'name', sortable: true, align: 'left' },
	// { label: '类型', prop: 'type_code_display', align: 'center' },
	// { label: '总容量', tdSlot: 'capacity', align: 'center' },
	// { label: '已分配容量', tdSlot: 'allocation', align: 'center' },
	// { label: '可用容量', tdSlot: 'available', align: 'center' },
	// { label: '存储目录', prop: 'storage_local_dir', align: 'center' },
	// { label: '磁盘', tdSlot: 'storage_volume', align: 'center' },
	// { label: '是否启用', prop: 'status', align: 'center' },
	// { label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
const localColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name', sortable: true },
	{ label: '类型', tdSlot: 'type', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
export {
	storageManageColumns,
	fcColumns,
	fcSorageColumns,
	iscsiColumns,
	iscsiSorageColumns,
	iscsiDetailsColumns,
	nasColumns,
	nasDetailsColumns,
	nvmeColumns,
	distributedColumns,
	storeColumns,
	localColumns,
};

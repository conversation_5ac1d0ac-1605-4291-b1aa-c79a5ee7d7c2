<template>
  <el-dialog
    v-model="formItem.isShow"
    title="编辑告警规则"
    append-to-body
    width="700"
  >
    <el-form
      ref="ruleFormRef"
      :model="formItem"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="当前规则">
        <el-input v-model="formItem.name" disabled/>
      </el-form-item>
      <div v-if="props.tableRow.expr_code=='0'">
        <el-form-item label="告警级别">
          <el-radio-group v-model="formItem.radio">
            <el-radio border value="critical">严重告警</el-radio>
            <el-radio border value="major">重要告警</el-radio>
            <el-radio border value="warning">次要告警</el-radio>
            <el-radio border value="info">提示告警</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item></el-form-item>
      </div>
      <div v-if="props.tableRow.expr_code!=='0'">
        <el-form-item prop="criticalValue">
          <template #label>
            <el-checkbox v-model="formItem.criticalCheck" label="严重告警" />
          </template>
          <el-input v-model="formItem.criticalValue" type="number" :disabled="!formItem.criticalCheck">
            <template #append><span>{{props.tableRow.unit}}</span></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="majorValue">
          <template #label>
            <el-checkbox v-model="formItem.majorCheck" label="重要告警" />
          </template>
          <el-input v-model="formItem.majorValue" type="number" :disabled="!formItem.majorCheck">
            <template #append><span>{{props.tableRow.unit}}</span></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="warningValue">
          <template #label>
            <el-checkbox v-model="formItem.warningCheck" label="次要告警" />
          </template>
          <el-input v-model="formItem.warningValue" type="number" :disabled="!formItem.warningCheck">
            <template #append><span>{{props.tableRow.unit}}</span></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="infoValue">
          <template #label>
            <el-checkbox v-model="formItem.infoCheck" label="提示告警" />
          </template>
          <el-input v-model="formItem.infoValue" type="number" :disabled="!formItem.infoCheck">
            <template #append><span>{{props.tableRow.unit}}</span></template>
          </el-input>
        </el-form-item>
        <el-form-item></el-form-item>
      </div>
      <el-form-item label="持续时间" prop="for">
        <el-input v-model="formItem.for" type="number">
          <template #append><span>分钟</span></template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="RulesEdit">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { alarmRulesEdit } from '/@/api/LogManage'; // 接口

const props = defineProps({
  tableRow: {
    type: Object,
    required: true
  },
  editTime: {
    type: String,
    required: true
  }
});
const propFor = (rule: any, value: any, callback: any) => {
	const regex = /^[1-9]\d*$/;
  regex.test(value)?callback():callback(new Error('请输入1以上的整数'))
};
const propCritical = (rule: any, value: any, callback: any) => {
	const regex = /^(?:[1-9]|[1-9][0-9]|100)$/;
  if (formItem.criticalCheck) {
    regex.test(value)?callback():callback(new Error('请输1-100之内的整数'))
  }else {callback()}
};
const propMajor = (rule: any, value: any, callback: any) => {
	const regex = /^(?:[1-9]|[1-9][0-9]|100)$/;
  if (formItem.majorCheck) {
    if (regex.test(value)) {
      if(formItem.criticalCheck){
        if(Number(value)>Number(formItem.criticalValue)) {
          callback()
        }else {
          callback(new Error("重要告警应小于严重告警"))
        }
      }else{callback()}
    } else {
      callback(new Error('请输1-100之内的整数'));
    }
  }else {callback()}
};
const propWarning = (rule: any, value: any, callback: any) => {
	const regex = /^(?:[1-9]|[1-9][0-9]|100)$/;
  if (formItem.warningCheck){
    if (regex.test(value)) {
      if(formItem.criticalCheck&&!formItem.majorCheck){
        if(Number(value)>Number(formItem.criticalValue)) {
          callback()
        }else {
          callback(new Error("次要告警应小于严重告警"))
        }
      }else if(formItem.majorCheck) {
        if(Number(value)>Number(formItem.majorValue)) {
          callback()
        }else {
          callback(new Error("次要告警应小于重要告警"))
        }
      }
    }else {
      callback(new Error('请输1-100之内的整数'));
    }
      if (formItem.majorCheck) {
        if(formItem.criticalCheck){
          if(Number(formItem.criticalValue)>=Number(value)) {
            callback()
          }else {
            callback(new Error("重要告警应小于严重告警"))
          }
        }else{callback()}
      }else {callback()}
  }else {callback()}
}
const propInfo = (rule: any, value: any, callback: any) => {
	const regex = /^(?:[1-9]|[1-9][0-9]|100)$/;
  if (formItem.infoCheck) {
    if (regex.test(value)) {
      if(formItem.criticalCheck&&!formItem.majorCheck&&!formItem.warningCheck) {
        if(Number(value)>Number(formItem.criticalValue)) {
          callback()
        }else {
          callback(new Error("提示告警应小于严重告警"))
        }
      }else if(formItem.majorCheck&&!formItem.warningCheck){
        if(Number(value)>Number(formItem.majorValue)) {
          callback()
        }else {
          callback(new Error("提示告警应小于重要告警"))
        }
      }else if(formItem.warningCheck) {
        if(Number(value)>Number(formItem.warningValue)) {
          callback()
        }else {
          callback(new Error("提示告警应小于次要告警"))
        }
      }
    }else {
      callback(new Error('请输1-100之内的整数'));
    }
  }else {callback()}
}
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  name: '',
  radio: '',
  criticalCheck: false,
  criticalValue: '1',
  majorCheck: false,
  majorValue: '1',
  warningCheck: false,
  warningValue: '1',
  infoCheck: false,
  infoValue: '1',
  for: '1'
});

const rules = reactive<FormRules>({
  for: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propFor, trigger: "change" },
  ],
  criticalValue: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propCritical, trigger: "change" },
  ],
  majorValue: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propMajor, trigger: "change" },
  ],
  warningValue: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propWarning, trigger: "change" },
  ],
  infoValue: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propInfo, trigger: "change" },
  ],
})
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        let data = {
          critical_value: '0',
          major_value: '0',
          warning_value: '0',
          info_value: '0',
          for_interval: formItem.for+'m',
          name: formItem.name,
          id: props.tableRow.id
        }
        if (props.tableRow.expr_code=='0') {
          if (formItem.radio == 'critical') {
            data.critical_value = '-1'
          }else if (formItem.radio == 'major') {
            data.major_value = '-1'
          }else if (formItem.radio == 'warning') {
            data.warning_value = '-1'
          }else if (formItem.radio == 'info') {
            data.info_value = '-1'
          }
        }else {
          if (formItem.criticalCheck) {
            data.critical_value = formItem.criticalValue
          }
          if (formItem.majorCheck) {
            data.major_value = formItem.majorValue
          }
          if (formItem.warningCheck) {
            data.warning_value = formItem.warningValue
          }
          if (formItem.infoCheck) {
            data.info_value = formItem.infoValue
          }
        }
        formItem.isShow = false;
        alarmRulesEdit(data)
        .then(res => {
          if (res.msg == "ok"){
            ElMessage.success('编辑告警规则操作完成');
            emit('returnOK', 'refresh')
            formItem.isShow = false;
          }else {
            ElMessage.error(res.msg);
          }
        }).catch((error) => {ElMessage.error('编辑告警规则操作失败')})
      }
    })
  }
}
watch(
  ()=> props.editTime,
  (val)=>{
    formItem.isShow = true;
    formItem.name = props.tableRow.name
    if(props.tableRow.expr_code == '0') {
      if(props.tableRow.critical_value == '-1') {
        formItem.radio = 'critical'
      }else if(props.tableRow.major_value == '-1') {
        formItem.radio = 'major'
      }else if(props.tableRow.warning_value == '-1') {
        formItem.radio = 'warning'
      }else if(props.tableRow.info_value == '-1') {
        formItem.radio = 'info'
      }
    }else {
      formItem.criticalCheck = props.tableRow.critical_value == '0' ? false : true
      formItem.criticalValue = props.tableRow.critical_value.toString()
      formItem.majorCheck = props.tableRow.major_value == '0' ? false : true
      formItem.majorValue = props.tableRow.major_value.toString()
      formItem.warningCheck = props.tableRow.warning_value == '0' ? false : true
      formItem.warningValue = props.tableRow.warning_value.toString()
      formItem.infoCheck = props.tableRow.info_value == '0' ? false : true
      formItem.infoValue = props.tableRow.info_value.toString()
    }
    formItem.for = props.tableRow.for_interval.split('m')[0]
  }
);
</script>
import request from '/@/utils/request';
// 用户 查询
export const userQuery = () => {
	return request({
		url: '/user/v1/user/all',
		method: 'get',
	});
};
// 用户 新建
export const userNew = (data: object) => {
	return request({
		url: '/user/v1/user/add',
		method: 'post',
		data,
	});
};
// 用户 修改
export const userEdit = (data: object) => {
	return request({
		url: '/user/v1/user/update',
		method: 'put',
		data,
	});
};
// 用户 状态
export const userStatus = (data: object) => {
	return request({
		url: '/user/v1/user/status',
		method: 'put',
		data,
	});
};
// 用户 重置密码
export const userReset = (data: object) => {
	return request({
		url: '/user/v1/user/resetpassword',
		method: 'put',
		data,
	});
};
// 用户 删除
export const userDelete = (data: object) => {
	return request({
		url: '/user/v1/user/delete',
		method: 'delete',
		data,
	});
};
// 权限获取
export const powerTreeQuery = (data: object) => {
	return request({
		url: '/acapi/v1/auth/userinfo',
		method: 'get',
		params: data,
	});
};
// 权限修改
export const powerTreeEdit = (data: object) => {
	return request({
		url: '/acapi/v1/auth/update',
		method: 'put',
		data,
	});
};
// 权限重置
export const powerTreeReset = (data: object) => {
	return request({
		url: '/acapi/v1/auth/default',
		method: 'put',
		data,
	});
};
// 权限可用性
export const powerUsabilityQuery = (data: object) => {
	return request({
		url: '/acapi/v1/auth/module',
		method: 'post',
		data,
	});
};
// 权限可用性code码
export const powerCodeQuery = (data: object) => {
	return request({
		url: '/acapi/v1/auth/check',
		method: 'post',
		data,
	});
};
// 权限查询
export const authorizeQuery = () => {
	return request({
		url: '/acapi/authentication/info',
		method: 'get',
	});
};

// 自动宕机迁移查询
export const automaticShutdownQuery = () => {
	return request({
		url: '/theapi/auto/evacuate',
		method: 'get',
	});
};
// 自动宕机迁移修改
export const automaticShutdownEdit = (data: object) => {
	return request({
		url: '/theapi/auto/evacuate',
		method: 'post',
		data,
	});
};
// 自动退出时间 查询
export const quitTimeQuery = () => {
	return request({
		url: '/theapi/session/out/time',
		method: 'get',
	});
};
// 自动退出时间 修改
export const quitTimeEdit = (data: object) => {
	return request({
		url: '/theapi/v1/session/out/time/update',
		method: 'put',
		data,
	});
};
// 防病毒配置 查询
export const antivirusConfigQuery = () => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'get',
	});
};
// 防病毒配置 修改
export const antivirusConfigEdit = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};

// 管理节点 查询
export const managementNodeQuery = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};
// 管理节点 扫描
export const managementNodeScan = () => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'get',
	});
};
// 管理节点 添加
export const managementNodeAdd = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'post',
		data,
	});
};
// 管理节点 删除
export const managementNodeDelete = (data: object) => {
	return request({
		url: '/theapi/v1/暂无',
		method: 'delete',
		data,
	});
};

<template>
	<el-dialog v-model="formItem.isShow" title="新建磁盘" append-to-body class="dialog-500" :close-on-click-modal="false" :show-close='false'>
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="磁盘名称" prop="name">
				<el-input v-model="formItem.name" :disabled="formItem.disabled" placeholder="请输入磁盘名称" />
			</el-form-item>
			<el-form-item label="创建方式" prop="way">
				<el-radio-group v-model="formItem.way">
					<el-radio value="new" :disabled="formItem.disabled">普通创建</el-radio>
					<el-radio value="disk" :disabled="formItem.disabled">上传本地磁盘</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="formItem.way == 'new'" label="存储格式" prop="format">
				<el-select v-model="formItem.format" style="width: 100%">
					<el-option v-for="item in formItem.formatData" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item v-if="formItem.way == 'new'" label="磁盘容量" prop="disk">
				<el-input v-model="formItem.disk" type="number">
					<template #append>
						<el-select v-model="formItem.unit" style="width: 80px">
							<el-option label="MB" value="MB" />
							<el-option label="GB" value="GB" />
							<el-option label="TB" value="TB" />
						</el-select>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item v-if="formItem.way == 'new'" label="置备类型" prop="type">
				<el-select v-model="formItem.type" style="width: 100%">
					<el-option label="精简置备" :value="1" />
					<el-option label="厚置备延迟置零" :value="2" />
					<el-option label="厚置备置零" :value="3" />
				</el-select>
			</el-form-item>
			<el-form-item label="备注">
				<el-input v-model="formItem.remark" :disabled="formItem.disabled" :rows="2" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息" />
			</el-form-item>
			<el-form-item v-if="formItem.way == 'disk'" label="上传磁盘" prop="file">
				<el-upload
					ref="uploadRef"
					class="upload-demo"
					:action="uploadAction"
					:limit="1"
					:on-exceed="handleExceed"
					:auto-upload="false"
					:before-upload="beforeUpload"
					:http-request="customUpload"
					:on-change="onChange"
					:on-remove="onRemove"
					:disabled="formItem.disabled"
					drag
				>
					<el-icon class="el-icon--upload"><UploadFilled /></el-icon>
					<div class="el-upload__text">
						将文件拖到此处，或<em>点击上传</em>
					</div>
				</el-upload>
				<!-- 上传进度 -->
				<div v-if="formItem.progressAll > 0" class="upload-progress">
					<el-progress
						:percentage="parseFloat((formItem.progressUsed/formItem.progressAll*100).toFixed(1))"
						:status="formItem.status"
						:stroke-width="8"
					/>
				</div>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm" :disabled="formItem.disabled">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="DiskNew">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type {
  UploadInstance,
  UploadProps,
  FormInstance,
  FormRules,
  UploadRawFile,
  UploadRequestOptions,
  UploadFile
} from 'element-plus';
import { ElMessage, genFileId } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { diskFormat, diskNew, diskSlice, diskUpload } from '/@/api/ResourcePool/storage.ts'; // 接口
import { propName, propNumber } from '/@/model/resource.ts'; // 表列、正则
import pLimit from 'p-limit';

const props = defineProps({
	tableRow: {
		type: Object,
		required: true,
	},
	newTime: {
		type: String,
		required: true,
	},
});
const ruleFormRef = ref<FormInstance>();
const uploadRef = ref<UploadInstance>();

const formItem = reactive({
	isShow: false,
	name: '',
	way: 'new',
	format: '',
	formatData: [],
	disk: '1',
	unit: 'MB',
	type: 1,
	remark: '',
	file: null as File | null,
	fileSize: 0,
	progressAll: 0,
	progressUsed: 0,
	status: '' as 'success' | 'exception' | '',
	disabled: false,
});
const propFile = (rule: any, value: any, callback: any) => {
	if (!formItem.file) {
		callback(new Error('请选择文件'));
	} else {
		callback();
	}
};
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'change' },
	],
	way: [{ required: true, message: '必选项', trigger: 'blur' }],
	format: [{ required: true, message: '必选项', trigger: 'blur' }],
	disk: [
		{ required: true, message: '必选项', trigger: 'blur' },
		{ validator: propNumber, trigger: 'change' },
	],
	type: [{ required: true, message: '必选项', trigger: 'blur' }],
	file: [
		{ required: true, message: '必选项', trigger: 'change' },
		{ validator: propFile, trigger: 'change' },
	],
});
// 上传相关配置
const uploadAction = '/upload/v5/upload/chunk'; // 这个不会被使用，因为我们用自定义上传

// 查询存储格式
const formatQuery = () => {
	diskFormat().then((res: any) => {
		formItem.formatData = res;
		formItem.format = res[0].id;
	});
};

// 处理文件超出限制
const handleExceed: UploadProps['onExceed'] = (files) => {
	uploadRef.value!.clearFiles();
	const file = files[0] as UploadRawFile;
	file.uid = genFileId();
	uploadRef.value!.handleStart(file);
};

// 上传前的钩子
const beforeUpload = (file: File) => {
	formItem.file = file;
	formItem.fileSize = file.size;
	// 触发表单验证
	if (ruleFormRef.value) {
		ruleFormRef.value.validateField('file');
	}

	return false; // 阻止自动上传，我们手动控制
};

// 文件状态改变时的钩子
const onChange = (uploadFile: UploadFile) => {
	if (uploadFile.raw) {
		formItem.file = uploadFile.raw;
		formItem.fileSize = uploadFile.raw.size;
		if (ruleFormRef.value) {
		ruleFormRef.value.validateField('file');
	}
	}
};

// 文件移除时的钩子
const onRemove = () => {
	formItem.file = null;
	formItem.fileSize = 0;
	formItem.progressAll = 0;
	formItem.progressUsed = 0;
	formItem.status = '';
	if (ruleFormRef.value) {
		ruleFormRef.value.validateField('file');
	}
};

// 自定义上传函数（这个不会被调用，因为我们手动控制上传）
const customUpload = (options: UploadRequestOptions) => {
	// 这里不做任何操作，因为我们在 confirm 中手动处理上传
	return Promise.resolve();
};
// 执行分片上传
const uploadFile = async (file: File) => {
	const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
	let chunks = Math.ceil(file.size / chunkSize);
	formItem.progressAll = chunks;
	formItem.progressUsed = 0;
	formItem.status = '';
	formItem.disabled = true;

	const limit = pLimit(3); // 设置最大并发数为3

	try {
		// 串行上传分片
		for (let i = 0; i < chunks; i++) {
			let start = i * chunkSize;
			let end = Math.min(file.size, start + chunkSize);
			let chunk = file.slice(start, end);

			await limit(() => uploadChunk(chunk, i, chunks, file.name));
		}

		// 通知完成
		await notifyComplete(chunks, file.name);
	} catch (error) {
		ElMessage.error('上传本地磁盘失败');
		formItem.status = 'exception';
		formItem.disabled = false;
	}
};

// 上传单个分片
const uploadChunk = async (chunk: Blob, index: number, total: number, filename: string) => {
	let formData = new FormData();
	formData.append('chunk', chunk);
	formData.append('index', index.toString());
	formData.append('total', total.toString());
	formData.append('filename', filename);
	formData.append('storage_pool_id', props.tableRow.id);

	try {
		await diskSlice(formData);
		formItem.progressUsed++;
	} catch (error) {
		formItem.status = 'exception';
		formItem.disabled = false;
		throw error;
	}
};

// 通知上传完成
const notifyComplete = async (total: number, filename: string) => {
	try {
		await diskUpload({
			name: formItem.name,
			total: total,
			filename: filename,
			storage_pool_id: props.tableRow.id,
			remark: formItem.remark,
			size: formItem.fileSize
		});

		formItem.status = 'success';
		setTimeout(() => {
			ElMessage.success('新建磁盘-上传本地磁盘操作完成');
			formItem.isShow = false;
			emit('returnOK', 'refresh');
			resetUploadForm();
		}, 1500);
	} catch (error) {
		formItem.status = 'exception';
		formItem.disabled = false;
		ElMessage.error('上传完成通知失败');
	}
};

// 重置上传表单
const resetUploadForm = () => {
	formItem.file = null;
	formItem.fileSize = 0;
	formItem.progressAll = 0;
	formItem.progressUsed = 0;
	formItem.status = '';
	formItem.disabled = false;
	// 清空上传组件
	if (uploadRef.value) {
		uploadRef.value.clearFiles();
	}
};
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			let size = 0
			if (formItem.unit == 'MB') {
			  size = parseInt(formItem.disk)*1024*1024
			}else if(formItem.unit == 'GB') {
			  size = parseInt(formItem.disk)*1024*1024*1024
			}else if(formItem.unit == 'TB') {
			  size = parseInt(formItem.disk)*1024*1024*1024*1024
			}
			if (val) {
				if (formItem.way == 'new') {
					formItem.isShow = false;
					diskNew({
						// host: '***********',
						storage_device_id: props.tableRow.storage_device_id,
						storage_pool_id: props.tableRow.id,
						name: formItem.name,
						path: '', // 默认是存储池路径
						encrypt: 0, // 加密
						volume_type:  formItem.format, // 存储卷类型
						join_type: 3, // 加入类型
						capacity: size,
						preallocation: formItem.type, // 置备类型
						remark: formItem.remark,
					})
					.then((res:any) => {
						emit('returnOK', 'refresh');
					})
				}else {
					if (formItem.file) {
						uploadFile(formItem.file);
					} else {
						ElMessage.warning('请选择要上传的文件');
					}
				}
			}
		});
	}
};
watch(
	() => props.newTime,
	() => {
		formItem.isShow = true;
		formItem.disabled = false;
		formItem.unit = 'MB';
		formItem.remark = '';
		resetUploadForm();
		formatQuery();
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
</script>

<style lang="scss" scoped>
.upload-demo {
  width: 100%;
}

.upload-progress {
  margin-top: 10px;
	width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 90px;
	padding: 0;

  .el-icon--upload {
    font-size: 40px;
    color: #c0c4cc;
    margin-bottom: 0px;
		padding: 0;
  }

  .el-upload__text {
    color: #606266;
    font-size: 14px;

    em {
      color: #409eff;
      font-style: normal;
    }
  }
}
:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}
</style>
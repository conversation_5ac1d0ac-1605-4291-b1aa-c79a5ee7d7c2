import request from '/@/utils/request';
// 存储池 查询
export const storagePollQuery = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/list',
		method: 'post',
		data,
	});
};
// 存储池 新建
export const storagePollNew = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/add',
		method: 'post',
		data,
	});
};
// 存储池 编辑
export const storagePollEdit = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/put',
		method: 'put',
		data,
	});
};
// 存储池 删除
export const storagePollDelete = (data: object) => {
	return request({
		url: '/theapi/v5/store/pool/local/delete',
		method: 'delete',
		data,
	});
};
// 存储池 新建 存储类型
export const storageTypeQuery = () => {
	return request({
		url: '/theapi/v5/store/pool/type/list',
		method: 'get',
	});
};
// 磁盘 查询
export const DiskQuery = (data: object) => {
	return request({
		url: '/theapi/v5/storage/pool/volume/list',
		method: 'post',
		data,
	});
};
// 磁盘 新建
export const diskNew = (data: object) => {
	return request({
		url: '/theapi/v5/storage/pool/volume',
		method: 'post',
		data,
	});
};
// 磁盘 切片
export const diskSlice = (data: any) => {
	return request({
		url: '/upload/v5/upload/chunk',
		method: 'post',
		headers: {
			'Content-Type': 'multipart/form-data',
		},
		data,
	});
};
// 磁盘 上传完成
export const diskUpload = (data: object) => {
	return request({
		url: '/upload/v5/upload/complete',
		method: 'post',
		data,
	});
};
// 磁盘 存储格式
export const diskFormat = () => {
	return request({
		url: '/theapi/v5/store/volume/type/list',
		method: 'get',
	});
};
// 磁盘 编辑
export const diskEdit = (data: object) => {
	return request({
		url: '/theapi/v5/storage/pool/volume/update',
		method: 'put',
		data,
	});
};
// 磁盘 删除
export const diskDelete = (data: object) => {
	return request({
		url: '/theapi/v5/storage/pool/volume/delete',
		method: 'delete',
		data,
	});
};

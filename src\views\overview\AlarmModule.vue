<template>
  <div>
    <el-table stripe :data="state.nodeData" :show-header='false'>
			<el-table-column prop="summary" label="告警名称" />
			<el-table-column prop="activeAt" label="告警时间">
				<template #default="{row}">
          <span class="status-warn">{{ row.activeAt }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="description" label="告警详情" />
			<el-table-column prop="severity" label="告警类型" width="100">
				<template #default="{row}">
          <span class="status-warn">{{ row.severity }}</span>
				</template>
			</el-table-column>
			<template #empty>
        <img src="../../assets/images/no-data.svg" alt="" class="minimg-wrap" />
			</template>
		</el-table>
  </div>
</template>
<script setup lang="ts" name="AlarmModule">
  import { defineAsyncComponent, reactive, onMounted, nextTick, ref,markRaw } from 'vue';
	import { Search } from '@element-plus/icons-vue';
	import { dayjs } from 'element-plus';
  import { alarmDataQuery  } from '/@/api/Overview';
  
  const state = reactive({
    nodeData: [],
  })
  const getData = () => {
    alarmDataQuery().then((res:any)=>{
      // state.nodeData = res
    })
  }


</script>
<style lang="scss" scoped>
  .minimg-wrap {
		height: 130px;
		margin-top: 30px;
	}
	.no-datas-wrap {
		margin-top: -25px;
	}
</style>